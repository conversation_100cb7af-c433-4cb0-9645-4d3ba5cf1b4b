<template>
  <el-drawer
    :title="title"
    v-if="drawer"
    custom-class="drawer-form drawer-detail"
    :size="drawerSize"
    :visible.sync="drawer"
    :direction="direction"
    :destroy-on-close="true"
  >
    <el-form
      ref="form"
      :model="form"
      v-loading="formLoading"
      :label-position="labelPosition"
      :inline="inlineFlag"
      :label-width="labelWidth + 'px'"
      :size="size"
      class="el-form-self"
      :style="formStyle"
    >
      <template v-for="(item, index) in forms">
        <el-col
          v-if="!item.hidden"
          :key="index"
          :xs="item.xs ? item.xs : 24"
          :sm="item.sm ? item.sm : 24"
          :md="item.md ? item.md : 24"
          :lg="item.lg ? item.lg : 24"
          :xl="item.xl ? item.xl : 24"
        >
          <el-form-item
            :style="item.detailStyle"
            :label="item.label ? item.label + (item.showColon ? '：' : '') : ''"
            :prop="item.prop"
            :labelWidth="item.labelWidth ? item.labelWidth : null"
          >
            <div
              :style="
                valueWidth
                  ? valueWidth
                  : drawerSize && labelWidth
                  ? 'max-width:' +
                    (parseInt(drawerSize) - parseInt(labelWidth) - 70) +
                    'px'
                  : ''
              "
            >
              <!-- 图片、音频、视频、文档 -->
              <template
                v-if="
                  item.type === 'fileImg' ||
                    item.type === 'uploadImg' ||
                    item.type === 'fileVideoOrAudio' ||
                    item.type === 'fileDocument' ||
                    item.type === 'fileVideo'
                "
              >
                <view-file
                  :type="item.type"
                  :data="form[item.prop] ? form[item.prop] : {}"
                ></view-file>
              </template>

              <!--富文本:内容仅展示-->
              <template v-else-if="item.type === 'editorText'">
                <div
                  class="editorText"
                  v-show="form[item.prop]"
                  v-html="form[item.prop] ? form[item.prop] : ''"
                ></div>
              </template>

              <!--所属分类-->
              <template v-else-if="item.type === 'classifyText'">
                <div
                  class="tag-box"
                  v-if="form[item.prop] && form[item.prop].length > 0"
                >
                  <span
                    class="word"
                    v-for="(tagItem, index) in form[item.prop]"
                    :key="index"
                  >
                    {{ tagItem[item.tagLabel] }}
                    <i v-show="index != form[item.prop].length - 1">、</i>
                  </span>
                </div>
              </template>

              <div v-else-if="item.type == 'title'" class="part-title">
                <span class="border"></span>
                <span class="title">{{ item.title }}</span>
              </div>

              <!-- 文字 -->
              <div v-else style="word-wrap: break-word;line-height: 32px;">
                {{ item.formatter ? item.formatter(form) : form[item.prop] }}
              </div>
            </div>
          </el-form-item>
        </el-col>
      </template>

      <el-col v-if="lists" :xs="24" :sm="24" :lg="24" :style="btnStyle">
        <template v-for="(btn, index2) in lists">
          <el-button
            :key="index2"
            :disabled="btn.disabled"
            :type="btn.type"
            :size="btn.size ? btn.size : 'medium'"
            :icon="btn.icon"
            :style="btn.style"
            @click="btn.func"
            >{{ btn.btnText }}</el-button
          >
        </template>
      </el-col>
    </el-form>
    <div class="drawer-footer detail-footer">
      <el-button size="medium" @click="close">关 闭</el-button>
    </div>
  </el-drawer>
</template>

<script>
// import viewFile from './small/viewFile'
export default {
  props: {
    size: { type: String, default: "small" },
    direction: { type: String, default: "rtl" },
    drawerSize: { type: String, default: "580px" },
    valueWidth: { type: String, default: "" },
    title: { type: String },
    labelPosition: { type: String, default: "right" },
    labelWidth: { type: String },
    formStyle: { type: Object },
    inlineFlag: { type: Boolean, default: false },
    forms: { type: Array }, // 表单组,
    lists: { type: Array }, // 按钮组
    btnStyle: { type: String, default: "margin-top:30px;text-align:center" }
  },
  //  components: { viewFile },
  data() {
    const form = {};
    const { forms } = this.$props;
    forms.forEach(item => {
      if (!item.prop || item.hidden) return false;
      if (
        item.type === "daterange" ||
        item.type === "checkboxList" ||
        item.type === "checkbox" ||
        item.type === "uploadImg" ||
        item.type === "cascader" ||
        (item.type === "select" && item.multiple)
      ) {
        form[item.prop] = [];
      } else {
        form[item.prop] = "";
      }
    });
    return {
      formLoading: false,
      drawer: false,
      form
    };
  },
  methods: {
    open() {
      this.drawer = true;
    },

    close() {
      this.drawer = false;
    },

    showLoading() {
      this.formLoading = true;
    },

    hideLoading() {
      this.formLoading = false;
    },
    // 表单赋值
    initforms(formEdit) {
      this.$nextTick(() => {
        const form = {};
        this.forms.forEach(item => {
          if (!item.prop || item.hidden) return false;
          if (
            item.type === "daterange" ||
            item.type === "checkboxList" ||
            item.type === "checkbox" ||
            item.type === "uploadImg" ||
            item.type === "cascader" ||
            (item.type === "select" && item.multiple)
          ) {
            form[item.prop] = [];
          } else {
            form[item.prop] = "";
          }
        });
        if (formEdit) {
          this.form = Object.assign(form, formEdit);
        } else {
          this.form = Object.assign({}, form);
        }
        this.loading = false;
        if (this.$refs.form && this.$refs.form.clearValidate) {
          this.$refs.form.clearValidate();
          const drawerArr = document.getElementsByClassName("drawer-detail");
          const footer = document.getElementsByClassName("detail-footer");
          footer[0].style.width = drawerArr[0].style.width;
        }
      });
    },
    // 绑定部分值(此时表单已渲染)
    initfields(obj) {
      this.form = Object.assign(this.form, obj);
    }
  }
};
</script>

<style lang="scss">
.drawer-detail {
  .el-form-item {
    width: 100%;
    margin-bottom: 18px !important;
  }
  .el-form-item__label {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #606266;
    float: left;
    text-align: right;
  }

  .el-form-item__content {
    font-family: PingFangSC-Regular;
    float: left;
    font-weight: 400;
    font-size: 14px;
    // min-width: calc(100% - 80px);
    color: #303133;
    line-height: 14px;
  }

  .el-drawer__header {
    background: #f8f9fb;
    padding: 0 16px;
    margin: 16px;
    height: 50px;
    font-family: PingFangSC-Medium;
    font-weight: 600;
    font-size: 18px;
    color: #303133;
    line-height: 50px;
  }
  .el-drawer__body {
    padding: 17px 24px 64px 36px;

    .part-title {
      width: 542px;
      height: 30px;
      background: rgb(51, 102, 255, 0.05);
      color: #303133;
      font-weight: 600;
      padding-left: 8px;

      .border {
        display: inline-block;
        width: 3px;
        margin-top: 8px;
        height: 14px;
        border-left: 3px solid #3366ff;
      }

      .title {
        display: inline-block;
        vertical-align: top;
        margin-left: 6px;
        height: 30px;
      }
    }
  }
  .drawer-footer {
    text-align: right;
    padding: 16px;
    background-color: #fff;
    box-shadow: 0 -2px 8px 0 rgba(200, 201, 204, 0.3);
    bottom: 0;
    position: fixed;
    width: 580px;
    right: 0;
  }
}
</style>
