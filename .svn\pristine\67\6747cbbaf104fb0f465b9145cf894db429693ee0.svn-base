/** When your routing table is too long, you can split it into small modules**/
import Layout from '@/layout'

const shareRoute = [{
    path: '/share',
    component: Layout,
    name: 'share',
    redirect: 'share',
    children: [{
      path: 'shareList',
      component: () => import('@/views/share/list'),
      name: 'shareList',
      meta: {
        title: '成单分享广场',
        icon: 'share',
      }
    }, ]
  },

  {
    path: '/addShare',
    component: Layout,
    name: 'shareParent',
    redirect: 'share',
    children: [{
      path: 'addShare',
      component: () => import('@/views/share/add'),
      name: 'addShare',
      meta: {
        title: '发布分享',
        icon: 'addShare',
      }
    }]
  },
  {
    path: '/mineShare',
    component: Layout,
    name: 'mineShareParent',
    redirect: 'mineShare',
    children: [{
      path: 'mineShare',
      component: () => import('@/views/share/mine'),
      name: 'mineShare',
      meta: {
        title: '我的分享',
        icon: 'mineShare',
      }
    }]
  },
  {
    path: '/myShare',
    component: Layout,
    name: 'myShareParent',
    redirect: 'myShare',
    children: [{
      path: 'myShare',
      component: () => import('@/views/share/my'),
      name: 'myShare',
      meta: {
        title: '抄送给我',
        icon: 'myShare',
      }
    }]
  }
]

export default shareRoute
