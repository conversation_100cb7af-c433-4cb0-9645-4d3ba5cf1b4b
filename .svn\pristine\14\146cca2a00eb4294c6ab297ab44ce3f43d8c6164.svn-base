import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [{
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
      path: '/redirect/:path*',
      component: () => import('@/views/redirect/index')
    }]
  },
  {
    path: '/',
    hidden: true,
    redirect: '/login',
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },



  {
    path: '/',
    component: Layout,
    name: "salary-analysis",
    meta: {
      title: '分司薪资分析报表',
      icon: 'salary'
    },
    children: [
      {
        path: 'salary-analysis',
        component: () => import('@/views/statistics/salary-analysis'),
        name: 'salary-analysis',
        meta: {
          title: '薪资分析报表',
          noCache: true,
          value: 'salary-analysis_index'
        }
      },
    ]
  },



  {
    path: '/',
    component: Layout,
    name: "userManage",
    meta: {
      title: '员工管理',
      icon: 'user'
    },
    children: [{
      path: 'userManage',
      component: () => import('@/views/userManage/index'),
      name: 'userManage',
      meta: {
        title: '花名册',
        noCache: true,
        value: 'User_index'
      }
    },
    // {
    //   path: 'groups',
    //   component: () => import('@/views/groups/index'),
    //   name: 'groups',
    //   meta: {
    //     title: '部门管理',
    //     noCache: true,
    //     value: 'Role_index'
    //   }
    // },
    {
      path: 'addContract',
      component: () => import('@/views/contract/add/index'),
      name: 'addContract',
      meta: {
        title: '新建合同',
        noCache: true,
        value: 'addContract_index'
      }
    },
    {
      path: 'contract',
      component: () => import('@/views/contract/index'),
      name: 'contract',
      meta: {
        title: '合同管理',
        noCache: true,
        value: 'contract_index'
      }
    },
    // {
    //   path: 'contractFile',
    //   component: () => import('@/views/contractFile/index'),
    //   name: 'contractFile',
    //   meta: {
    //     title: '合同附件',
    //     noCache: true,
    //     value: 'contractFile_index'
    //   }
    // }
  ]
  },
  {
    path: '/',
    component: Layout,
    name: "addSalary",
    meta: {
      title: '工资管理',
      icon: 'positionSalary'
    },
    children: [
      {
        path: 'addSalary',
        component: () => import('@/views/payroll/add/index'),
        name: 'addSalary',
        meta: {
          title: '新建工资',
          noCache: true,
          value: 'Salary_index'
        }
      },
    //   {
    //   path: 'positionSalary',
    //   component: () => import('@/views/positionSalary/index'),
    //   name: 'positionSalary',
    //   meta: {
    //     title: '宽带薪酬岗位明细',
    //     noCache: true,
    //     value: 'Salary_index'
    //   }
    // },
    {
      path: 'payroll',
      component: () => import('@/views/payroll/index'),
      name: 'payroll',
      meta: {
        title: '工资列表',
        // noCache: false,
        keepAlive: true,
        value: 'payroll_index'
      }

    }
    // {
    //   path: 'attachment',
    //   component: () => import('@/views/attachment/index'),
    //   name: 'attachment',
    //   meta: {
    //     title: '工资单附件',
    //     noCache: true,
    //     value: 'attachment_index'
    //   }
    // },
    // {
    //   path: 'attendance',
    //   component: () => import('@/views/attendance/index'),
    //   name: 'attendance',
    //   meta: {
    //     title: '考勤管理',
    //     noCache: true,
    //     value: 'attendance_index'
    //   }
    // }
  ]
  },

  {
    path: '/',
    component: Layout,
    name: "share",
    meta: {
      title: '知识库',
      icon: 'share'
    },
    children: [{
        path: 'whzd',
        component: () => import('@/views/share/whzd'),
        name: 'whzd',
        meta: {
          title: '文化制度',
          noCache: true,
          value: 'whzd_index'
        }
      },
      // {
      //   path: 'zyjn',
      //   component: () => import('@/views/share/zyjn'),
      //   name: 'zyjn',
      //   meta: {
      //     title: '专业技能',
      //     noCache: true,
      //     value: 'zyjn_index'
      //   }
      // },
      {
        path: 'glsj',
        component: () => import('@/views/share/glsj'),
        name: 'glsj',
        meta: {
          title: '管理视角',
          noCache: true,
          value: 'glsj_index'
        }
      },
      {
        path: 'rlzy',
        component: () => import('@/views/share/rlzy'),
        name: 'rlzy',
        meta: {
          title: '人力资源',
          noCache: true,
          value: 'rlzy_index'
        }
      },
      {
        path: 'ywtz',
        component: () => import('@/views/share/ywtz'),
        name: 'ywtz',
        meta: {
          title: '业务拓展',
          noCache: true,
          value: 'ywtz_index'
        }
      },
      {
        path: 'zyck',
        component: () => import('@/views/share/zyck'),
        name: 'zyck',
        meta: {
          title: '专业财会',
          noCache: true,
          value: 'zyck_index'
        }
      },
      {
        path: 'fwfx',
        component: () => import('@/views/share/fwfx'),
        name: 'fwfx',
        meta: {
          title: '法务',
          noCache: true,
          value: 'fwfx_index'
        }
      },
      {
        path: 'xrrzx',
        component: () => import('@/views/share/xrrzx'),
        name: 'xrrzx',
        meta: {
          title: '新人入职训',
          noCache: true,
          value: 'xrrzx_index'
        }
      },
      {
        path: 'czsc',
        component: () => import('@/views/share/czsc'),
        name: 'czsc',
        meta: {
          title: '操作手册',
          noCache: true,
          value: 'czsc_index'
        }
      },
      {
        path: 'zhts',
        component: () => import('@/views/share/zhts'),
        name: 'zhts',
        meta: {
          title: '综合提升',
          noCache: true,
          value: 'zhts_index'
        }
      },
      {
        path: 'qtzl',
        component: () => import('@/views/share/qtzl'),
        name: 'qtzl',
        meta: {
          title: '其他资料',
          noCache: true,
          value: 'qtzl_index'
        }
      },

      // {
      //   path: 'excellent',
      //   // Location: screenLeft,
      //   component: Layout,
      //   name: "excellent",
      //   meta: {
      //     title: '优秀案例分享',
      //     icon: 'excellent'
      //   },
      //   children: [{
      //       path: 'rlzy',
      //       component: () => import('@/views/share/whzd'),
      //       name: 'rlzy',
      //       meta: {
      //         title: '人力资源',
      //         noCache: true,
      //         value: 'rlzy_index'
      //       }
      //     },
      //     {
      //       path: 'ywtz',
      //       component: () => import('@/views/share/whzd'),
      //       name: 'ywtz',
      //       meta: {
      //         title: '业务拓展',
      //         noCache: true,
      //         value: 'ywtz_index'
      //       }
      //     },
      //     {
      //       path: 'zyck',
      //       component: () => import('@/views/share/whzd'),
      //       name: 'zyck',
      //       meta: {
      //         title: '专业财会',
      //         noCache: true,
      //         value: 'zyck_index'
      //       }
      //     },
      //     {
      //       path: 'fwfx',
      //       component: () => import('@/views/share/whzd'),
      //       name: 'fwfx',
      //       meta: {
      //         title: '法务',
      //         noCache: true,
      //         value: 'fwfx_index'
      //       }
      //     },
      //     {
      //       path: 'qtzl',
      //       component: () => import('@/views/share/whzd'),
      //       name: 'qtzl',
      //       meta: {
      //         title: '其他资料',
      //         noCache: true,
      //         value: 'qtzl_index'
      //       }
      //     },
      //   ]}
  ]
  },



  {
    path: '/',
    component: Layout,
    name: "set",
    meta: {
      title: '管理员空间',
      icon: 'set'
    },
    children: [{
      path: 'dict',
      component: () => import('@/views/dict/index'),
      name: 'dict',
      meta: {
        title: '数据字典',
        icon: 'dict',
        noCache: true,
        value: 'dict_index'
      }
     },
     {
      path: 'logs',
      component: () => import('@/views/logs/index'),
      name: 'logs',
      meta: {
        title: '日志管理',
        icon: 'logs',
        noCache: true,
        value: 'logs_index'
      }
    }
    ]
  },


  // {
  //   path: '/',
  //   component: Layout,
  //   name: "dict",
  //   redirect: '/dict',
  //   children: [{
  //     path: 'dict',
  //     component: () => import('@/views/dict/index'),
  //     name: 'dict',
  //     meta: {
  //       title: '数据字典',
  //       icon: 'dict',
  //       noCache: true,
  //       value: 'dict_index'
  //     }
  //   }]
  // },
  // {
  //   path: '/',
  //   component: Layout,
  //   name: "logs",
  //   redirect: '/logs',
  //   children: [{
  //     path: 'logs',
  //     component: () => import('@/views/logs/index'),
  //     name: 'logs',
  //     meta: {
  //       title: '日志管理',
  //       icon: 'logs',
  //       noCache: true,
  //       value: 'logs_index'
  //     }
  //   }]
  // },
  {
    path: '/',
    component: Layout,
    name: "statistics",
    meta: {
      title: '数据报表',
      icon: 'statistics'
    },
    children: [{
        path: 'statistics',
        component: () => import('@/views/statistics/index'),
        name: 'statistics',
        meta: {
          title: '岗位人数统计',
          noCache: true,
          value: 'statistics_index'
        }
      },
      {
        path: 'statistics1',
        component: () => import('@/views/statistics/deptIndex'),
        name: 'statistics1',
        meta: {
          title: '部门人数统计',
          noCache: true,
          value: 'statistics1_index'
        }
      }
  ]
  },
]







const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
