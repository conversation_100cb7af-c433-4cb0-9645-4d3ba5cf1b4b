<template>
  <el-row style="margin:0 0 20px">
    <editor id="tinymce" v-model="value" :init="init" />
  </el-row>
</template>

<script>
// import { uploadFile } from '@/api/mqmc-upmsx'
import tinymce from 'tinymce/tinymce'
import 'tinymce/themes/silver/theme'
import 'tinymce/icons/default/icons.min.js'
import editor from '@tinymce/tinymce-vue'
import 'tinymce/plugins/link'
import 'tinymce/plugins/code'
import 'tinymce/plugins/table'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/contextmenu'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/colorpicker'
import 'tinymce/plugins/textcolor'
export default {
  components: {
    editor
  },
  data() {
    return {
      value: '',
      init: {
        language_url: './tinymce/langs/zh_CN.js', // 语言包的路径
        language: 'zh_CN', // 语言
        skin_url: './tinymce/skins/ui/oxide', // skin路径
        content_css: './tinymce/skins/content/default/content.css',
        height: 600, // 编辑器高度
        branding: false,
        plugins: 'lists table wordcount',
        toolbar:
          'undo redo |  formatselect | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image  table | removeformat'
      }
    }
  },
  mounted() {
    tinymce.init({})
  },
  methods: {
    handleSave() {
      this.$emit('handleSave', this.value)
    },
    initEditor() {
      tinymce.init({})
    }
  }
}
</script>
