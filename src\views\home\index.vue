<template>
  <el-row class="page-container">
    <el-row class="page-main" style="font-family:'黑体'">
      <el-row>
        <el-row class="block">
          <span class="demonstration">计算时间：</span>
          <el-date-picker
            v-model="value"
            value-format="yyyy-MM-dd"
            :default-value="new Date()"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="change"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-row>
        <el-row style="margin-top:25px">
          <el-row style="height:40vh">
            <el-col :span="12">
              <div class="grid-content bg-purple">
                <div style="font-weight:bold;padding-bottom:20px;font-size:20px">用户模块</div>
                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">日增用户：</span>
                    {{data.userDayCount}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">总用户：</span>
                    {{data.userCount}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">男性用户：</span>
                    {{data.manCount}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">女性用户：</span>
                    {{data.womanCount}}
                  </el-col>
                </el-row>
                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">活跃用户总数：</span>
                    {{data.activeCount}}
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content bg-purple-light">
                <div style="font-weight:bold;padding-bottom:20px;font-size:20px">收益模块</div>
                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">日收益：</span>
                    {{data.incomeDaySum}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">微信：</span>
                    {{data.wxSum}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">支付宝：</span>
                    {{data.aliSum}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">ios：</span>
                    {{data.iosSum}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">收益总额：</span>
                    {{data.incomeSum}}
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>

          <el-row style="height:40vh">
            <el-col :span="12">
              <div class="grid-content bg-purple">
                <div style="font-weight:bold;padding-bottom:20px;font-size:20px">钻石模块</div>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">钻石充值总数：</span>
                    {{data.rechargeSum}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">消耗钻石总数：</span>
                    {{data.consumeSum}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">日充值：</span>
                    {{data.rechargeDaySum}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">日消耗：</span>
                    {{data.consumeDaySum}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">留存钻石数：</span>
                    {{data.retentionSum}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">文字聊天消耗：</span>
                    {{data.textRetentionSum}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">语音聊天消耗：</span>
                    {{data.voiceRetentionSum}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">视频聊天消耗：</span>
                    {{data.videoRetentionSum}}
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content bg-purple-light">
                <div style="font-weight:bold;padding-bottom:20px;font-size:20px">积分模块</div>
                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">获取积分总数：</span>
                    {{data.rechargeSumByIn}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">日获取积分数：</span>
                    {{data.rechargeDaySumByIn}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">提现积分总数：</span>
                    {{data.consumeSumByIn}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">日提现积分数：</span>
                    {{data.consumeDaySumByIn}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">留存积分数：</span>
                    {{data.retentionSumByIn}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">文字聊天获取：</span>
                    {{data.textRetentionSumByIn}}
                  </el-col>
                </el-row>

                <el-row style="padding: 20px 25px">
                  <el-col :span="12">
                    <span class="label">语音聊天获取：</span>
                    {{data.voiceRetentionSumByIn}}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">视频聊天获取：</span>
                    {{data.videoRetentionSumByIn}}
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
        </el-row>
      </el-row>
    </el-row>
  </el-row>
</template>

<script>
import { revenue } from '@/api/user'
export default {
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      value: '',
      data: {}
    }
  },
  mounted() {
    this.getNowTime()
  },
  methods: {
    change(val) {
      this.value = val
      this.getData()
    },
    getNowTime() {
      var now = new Date()
      var year = now.getFullYear() //得到年份
      var month = now.getMonth() //得到月份
      var date = now.getDate() //得到日期
      month = month + 1
      month = month.toString().padStart(2, '0')
      date = date.toString().padStart(2, '0')
      this.value = [`${year}-${month}-${date}`, `${year}-${month}-${date}`]
      this.getData()
    },
    getData() {
      let data = {
        startTime: this.value[0] + ' 00:00:00',
        endTime: this.value[1] + ' 23:59:59'
      }
      revenue(data).then(res => {
        if (res.code == 200) {
          this.data = res.data
          for (let o in this.data) {
            if (!this.data[o]) {
              this.data[o] = 0
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  font-size: 20px;
  // min-height: calc(100vh - 134px);
  // font-family: PingFangSC-Medium;
  // font-weight: 700;
  // width: 100%;
  // justify-content: center;
  // align-items: center;
  // font-size: 32px;
  // .part {
  //   width: 50px;
  //   height: 500px;
  //   border: 1px solid red;
  // }
  .label {
    font-weight: normal;
  }
}
</style>
