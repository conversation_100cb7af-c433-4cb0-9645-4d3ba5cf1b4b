import {
  fetch,
  post,
  put,
  postToken,
  deleteHttp
} from '@/utils/request'

// 登录
export const login = data => {
  return post(`/login`, data)
}

// 用户信息
export const getUserInfo = data => {
  return post(`/agent/userInfo/getUserInfo`, data)
}


// 渠道管理
export const getSubordinateList = data => {
  return post(`/agent/subordinate/getSubordinateList`, data)
}

// 创建下级
export const createSubordinate = data => {
  return post(`/agent/subordinate/createSubordinate`, data)
}

// 停用账号
export const disableSubordinateById = data => {
  return post(`/agent/subordinate/disableSubordinateById`, data)
}

// 删除账号
export const delSubordinateById = data => {
  return post(`/agent/subordinate/delSubordinateById`, data)
}

// 数据统计
export const getStatisticsList = data => {
  return post(`/agent/statistics/getStatisticsList`, data)
}

// 收益明细
export const getProfitList = data => {
  return post(`/agent/profit/getProfitList`, data)
}

// 获取用户信息列表
export const getRegisterRecordList = data => {
  return post(`/agent/registerRecord/getRegisterRecordList`, data)
}

// 下级提现记录
export const getSubCashOutList = data => {
  return post(`/agent/cashOut/getSubCashOutList`, data)
}

// 保存支付宝信息
export const updateUserALiInfo = data => {
  return post(`/agent/userInfo/updateUserALiInfo`, data)
}

// 提现
export const saveCashOut = data => {
  return post(`/agent/cashOut/saveCashOut`, data)
}

// 保存密码
export const updatePassword = data => {
  return post(`/agent/userInfo/updatePassword`, data)
}

// 获取落地页链接
export const getChannelUrlList = data => {
  return post(`/agent/channelUrl/getChannelUrlList`, data)
}

// 我的提现列表
export const getMyCashOutList = data => {
  return post(`/agent/cashOut/getMyCashOutList`, data)
}

// 更新提现状态
export const updateCashOutStatus2 = data => {
  return post(`/agent/cashOut/updateCashOutStatus`, data)
}











































// 社区用户
export const getUserInfoList = data => {
  return fetch(`/admin/user/getUserInfoList`, data)
}

// 用户信息处理  3--个性签名违规  2--头像违规  1-- 昵称违规
export const userViolations = data => {
  return fetch(`/admin/user/UserViolations`, data)
}

// 钻石/积分管理
export const getUserDiamondListByMetaType = data => {
  return fetch(`/admin/userDiamond/getUserDiamondListByMetaType`, data)
}

// 订单管理
export const getOrderList = data => {
  return fetch(`/admin/order/getOrderList`, data)
}

// 提现管理
export const getCashOutList = data => {
  return fetch(`/admin/userDiamond/getCashOutList`, data)
}

// 用户黑名单
export const joinBlack = data => {
  return post(`/admin/user/joinBlack`, data)
}

// 动态列表
export const getDynamicList = data => {
  return fetch(`/admin/dynamic/getDynamicList`, data)
}
// 删除动态
export const delDynamic = data => {
  return post(`/admin/dynamic/delDynamic`, data)
}

// 设置动态-是否推荐
export const updateDynamicStatus = data => {
  return post(`/admin/dynamic/updateDynamicStatus`, data)
}

// 评论列表
export const getCommentList = data => {
  return fetch(`/admin/comment/getCommentList`, data)
}

// 删除评论
export const delComment = data => {
  return post(`/admin/comment/delComment`, data)
}

// 约会列表
export const getAppointmentList = data => {
  return fetch(`/admin/appointment/getAppointmentList`, data)
}

// 删除约会
export const delAppointmentList = data => {
  return post(`/admin/appointment/delAppointmentList`, data)
}


// 主页
export const revenue = data => {
  return fetch(`/admin/revenue/revenue`, data)
}

// 获取用户照片墙
export const getUserPhotoList = data => {
  return fetch(`/admin/user/getUserPhotoList`, data)
}


// 获取视频列表
export const getUserVideo = data => {
  return fetch(`/admin/user/getUserVideo`, data)
}

// 更新视频状态
export const updateUserVideoStatus = data => {
  return post(`/admin/user/updateUserVideoStatus`, data)
}

// 用户信息变更列表
export const getUserInfoByReviewList = data => {
  return fetch(`/admin/user/getUserInfoByReviewList`, data)
}

// 审核用户信息状态
export const updateUserReviewType = data => {
  return post(`/admin/user/updateUserReviewType`, data)
}


// 获取运营设置
export const getExamineSwitch = data => {
  return post(`/admin/examineSwitch/getExamineSwitch`, data)
}

// 获取运营设置
export const updateExamineSwitch = data => {
  return post(`/admin/examineSwitch/updateExamineSwitch`, data)
}

// 处理举报
export const updateReportStatus = data => {
  return post(`/admin/report/updateReportStatus`, data)
}

// 审核提现
export const updateCashOutStatus = data => {
  return post(`/admin/userDiamond/updateCashOutStatus`, data)
}


// 糖圈列表
export const getTangquanList = data => {
  return fetch(`/admin/tangquan/getTangquanList`, data)
}

// 更新糖圈状态
export const updateTangquanStatus = data => {
  return post(`/admin/tangquan/updateTangquanStatus`, data)
}

//  解除封禁
export const cannotBlack = data => {
  return post(`/admin/user/cannotBlack`, data)
}

//  聊天内容
export const getImMessageLis = data => {
  return fetch(`/admin/userImMessage/getImMessageList`, data)
}

//  用户分享管理（查询和列表其他用户管理一样）
export const getShareInfoDetailList = data => {
  return fetch(`/admin/user/getShareInfoDetailList`, data)
}

// /admin/userDiamond/getUserDiamondListByMetaType













// 分隔









// 专家列表
export const profList = data => {
  return fetch(`/prof/page`, data)
}

// 新增专家
export const addProf = data => {
  return post(`/prof`, data)
}

// 修改专家
export const updateProf = data => {
  return put(`/prof`, data)
}

// 删除专家
export const deleteProf = data => {
  return deleteHttp(`/prof/${data.id}`)
}

// 会员列表
export const vipList = data => {
  return fetch(`/vip/page`, data)
}

// 用户列表
export const userList = data => {
  return fetch(`/user/page`, data)
}

// 新增用户
export const addUser = data => {
  return post(`/user`, data)
}

// 修改用户
export const updateUser = data => {
  return put(`/user`, data)
}

// 删除用户
export const deleteUser = data => {
  return deleteHttp(`/user/${data.id}`)
}

// 获取用户
export const getUser = data => {
  return fetch(`/user/${data.id}`)
}

// 订单列表
export const orderList = data => {
  return fetch(`/order/page`, data)
}

// 新增订单
export const addOrder = data => {
  return post(`/order`, data)
}

// 修改订单
export const updateOrder = data => {
  return put(`/order`, data)
}

// 修改订单状态
export const updateOrderStatus = data => {
  return put(`/order/status`, data)
}

// 删除订单
export const deleteOrder = data => {
  return deleteHttp(`/order/${data.id}`)
}

// 增加协议
export const addInformed = data => {
  return post(`/informed`, data)
}

// 修改协议
export const putInformed = data => {
  return put(`/informed`, data)
}

// 获取协议详情
export const getInformedDetail = data => {
  return fetch(`/informed/${data.id}`)
}

// 获取协议列表
export const getInformedList = data => {
  return fetch(`/informed/page`, data)
}

// 停用启用协议
export const putInformedStatus = data => {
  return put(`/informed/switch/${data.id} `)
}

// 留言列表
export const getChatList = data => {
  return fetch(`/chat/list`, data)
}

// 留言列表
export const getChatInfoList = data => {
  return fetch(`/chat/info/list`, data)
}

// 获取用户信息
export const getToken = data => {
  return fetch(`/sys/token`)
}

// 删除留言
export const delChat = data => {
  return deleteHttp(`/chat/del?chatId=${data.id}`)
}

// 留言已读
export const putUpdRead = data => {
  return put(`/chat/updRead?isRead=${data.isRead}&senderId=${data.senderId}&receiverId=${data.receiverId}`)
}

// 修改密码
export const putPwd = data => {
  return put(`/sys/password`, data)
}

// 增加轮播图
export const addBanner = data => {
  return post(`/banner`, data)
}

// 修改轮播图
export const putBanner = data => {
  return put(`/banner`, data)
}

// 获取轮播图
export const getBanner = data => {
  return fetch(`/banner/page`, data)
}

// 预约量、线上收入、成功率统计
export const getOperationAppointment = data => {
  return fetch(`/operation/appointment`, data)
}

// 爽约量、退款统计
export const getOperationBroken = data => {
  return fetch(`/operation/broken`, data)
}

// 推广量统计
export const getOperationPromotion = data => {
  return fetch(`/operation/promotion`, data)
}

// 退款
export const postRefund = data => {
  return post(`/pay/refund`, data)
}

// 举报列表
export const getReportList = data => {
  return fetch(`/admin/report/getReportList`, data)
}


// 用户详情
export const getUserDetail = data => {
  return post(`/admin/user/getUserDetail`, data)
}


// 微信审核
export const getUserAuthList = data => {
  return post(`/admin/user/getUserAuthList`, data)
}

// 审核通过
export const passAuth = data => {
  return post(`/admin/user/passAuth`, data)
}

// 审核不通过
export const refuseUserContactStatus = data => {
  return post(`/admin/user/refuseUserContactStatus`, data)
}

// 驳回实人认证
export const refuseUserAuthStatus = data => {
  return post(`/admin/user/refuseUserAuthStatus`, data)
}


