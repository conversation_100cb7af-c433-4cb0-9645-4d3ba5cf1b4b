import {
  fetch,
  post,
  put,
  formDataPost,
  deleteHttp
} from '@/utils/request'

// 查询列表
export const getUsersList = data => {
  return fetch(`/users`, data)
}

// 保存
export const saveUsers = data => {
  return post(`/users`, data)
}

// 更新
export const putUsers = data => {
  return put(`/users`, data)
}

// 详情
export const getDetail = data => {
  return fetch(`/users/${data.id}`)
}

// 删除
export const deleteUsers = data => {
  return deleteHttp(`/users/${data.id}`)
}


export const logoutUser = data => {
  return fetch(`/users/logout`, data)
}



// 下载模板
export const getUserTemplate = data => {
  return fetch(`/users/file/downExcel`, data, 'blob')
}


export const uploadInfo = data => {
  return post(`/users/file/upload`, data)
}



//合同信息
export const contractDetail = data => {
  return fetch(`/contract/getContract`, data)
}



// 启用禁用
export const updateStatus = data => {
  return post(`/users/setStatus`, data)
}


  // 查询部门
  export const getDeptName = data => {
    return fetch(`/user/groups/findDeptList`,data)
  }


  //查询职级薪水
  // export const getRankSalary = data => {
  //   return fetch(`/position/salary/queryList`,data)
  // }


    //查询岗位角色
    export const getRoleName = data => {
      return fetch(`/user/roles/findRoleList`,data)
    }
  

  // 获取部门下的岗位 -- 不包含子级部门
export const findPostByDeptId = data => {
  return fetch(`/user/roles/findPostByDeptId`, data)
}


//修改密码
export const updatePwd = data => {
  return formDataPost(`/users/updatePwd`, data)
}


// 重置密码
export const resetpwd = data => {
  return formDataPost(`/users/resetpwd`, data, {
    'Content-Type': 'multipart/form-data'
  })
}
