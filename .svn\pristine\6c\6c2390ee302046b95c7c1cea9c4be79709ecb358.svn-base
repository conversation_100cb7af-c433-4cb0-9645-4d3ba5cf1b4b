/** When your routing table is too long, you can split it into small modules**/
import Layout from '@/layout'

const settingRoute = [{
    path: '/company',
    component: Layout,
    name: 'company',
    redirect: 'noRedirect',
    children: [{
      path: 'companyInfo',
      component: () => import('@/views/setting/companyInfo/index'),
      name: 'companyInfo',
      meta: {
        title: '企业信息',
        icon: 'companyInfo',
        noCache: true,
        value: 'Company_index'
      }
    }]
  }, {
    path: '/setting',
    component: Layout,
    name: 'sysSetting',
    redirect: 'noRedirect',
    children: [{
      path: 'orgStructure',
      component: () => import('@/views/setting/orgStructure/index'),
      name: 'orgStructure',
      meta: {
        title: '组织架构',
        icon: 'orgStructure',
        noCache: true,
        value: 'Role_index'
      }
    }]
  },
  {
    path: '/setting',
    component: Layout,
    name: 'sysUserManage',
    redirect: 'noRedirect',
    children: [{
      path: 'userManage',
      component: () => import('@/views/setting/userManage/index'),
      name: 'userManage',
      meta: {
        title: '员工管理',
        icon: 'userManage',
        noCache: true,
        value: 'User_index'
      }
    }]
  },
]

export default settingRoute
