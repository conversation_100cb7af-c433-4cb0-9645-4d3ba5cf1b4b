<template>
  <el-row class="page-container">
    <el-row class="page-main">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self 
           :columns="columns" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>

      <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true"
        @handleConfirm="handleConfirm" :label-width="'90'" :title="drawTitle"></draw-form>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import {queryPage} from "@/views/positionSalary/api";
import {addSalary,deletetSalary,updateSalary,getSalaryByid} from "./api";

export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
        total: 0,
        id: 0,
        drawTitle: "",
        drawForms: [
          {
              type: "input",
              label: "岗位名称",
              prop: "levelName",
              placeholder: "请填写岗位名称",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写岗位名称",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              label: "岗位编码",
              prop: "levelCode",
              placeholder: "请填写岗位编码",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写岗位编码",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "select",
              label: "职级、管理层级(P/M)",
              prop: "levelCodePm",
              placeholder: "请填写职级、管理层级(P/M)",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写职级、管理层级(P/M)",
                  trigger: "blur"
                }
              ],
              opts: [
                {
                  label: "P1",
                  value: "P1"
                },
                {
                  label: "P2",
                  value: "P2"
                },
                {
                  label: "P3",
                  value: "P3"
                },
                {
                  label: "P4",
                  value: "P4"
                },
                {
                  label: "P4M1",
                  value: "P4M1"
                },
                {
                  label: "P5",
                  value: "P5"
                },
                {
                  label: "P5M2",
                  value: "P5M2"
                },
                {
                  label: "P6",
                  value: "P6"
                },
                {
                  label: "P6M3",
                  value: "P6M3"
                },
                {
                  label: "P7",
                  value: "P7"
                },
                {
                  label: "P7M4",
                  value: "P7M4"
                },
                {
                  label: "P8",
                  value: "P8"
                },
                {
                  label: "P9",
                  value: "P9"
                },
                {
                  label: "P10",
                  value: "P10"
                },
              ],
            },
            {
              type: "input",
              class: "inputMore9",
              label: "基础工资（城市最低标准）",
              prop: "levelSalary",
              placeholder: `请填写基础工资`,
              rules: [
                {
                  required: true,
                  message: "请填写基础工资",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "岗位工资（档级2）",
              prop: "postSalary2",
              placeholder: `请填写岗位工资`,
              rules: [
                {
                  required: true,
                  message: "请填写岗位工资",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "岗位工资+1（档级3）",
              prop: "postSalary3",
              placeholder: `请填写岗位工资+1`,
              rules: [
                {
                  required: true,
                  message: "请填写岗位工资+1",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "岗位工资+2（档级4）",
              prop: "postSalary4",
              placeholder: `请输入岗位工资+2`,
              rules: [
                {
                  required: true,
                  message: "请填写岗位工资+2",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "岗位工资+3（档级5)",
              prop: "postSalary5",
              placeholder: `请输入岗位工资+3`,
              rules: [
                {
                  required: true,
                  message: "请填写岗位工资+3",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "岗位绩效",
              prop: "postPerformanceSalary",
              placeholder: `请填写岗位绩效`,
              rules: [
                {
                  required: true,
                  message: "请填写岗位绩效",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "电话补贴",
              prop: "telephoneSalary",
              placeholder: `请填写电话补贴`,
              rules: [
                {
                  required: true,
                  message: "请填写电话补贴",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "交通补贴",
              prop: "trafficSalary",
              placeholder: `请填写交通补贴`,
              rules: [
                {
                  required: true,
                  message: "请填写交通补贴",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "餐补",
              prop: "mealSalary",
              placeholder: `请填写餐补`,
              rules: [
                {
                  required: true,
                  message: "请填写餐补",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              class: "inputMore9",
              label: "满勤奖",
              prop: "fullSalary",
              placeholder: `请填写满勤奖`,
              rules: [
                {
                  required: true,
                  message: "请填写满勤奖",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "textarea",
              class: "inputMore9",
              label: "岗级描述",
              prop: "levelDesc",
              placeholder: `请输入岗级描述`,
              resize: "auto",
              autosize: true
            },
            {
              type: "select",
              class: "inputMore9",
              label: "所属公司",
              prop: "type",
              placeholder: `请选择所属公司`,
              opts: [
                {
                  label: "总部",
                  value: 1
                },
                {
                  label: "蚌埠分公司",
                  value: 2
                },
                {
                  label: "合肥分公司",
                  value: 3
                },
                {
                  label: "常州分公司",
                  value: 4
                }
              ],
              rules: [
                {
                  required: true,
                  message: "请选择所属公司",
                  trigger: "change"
                }
              ]
            },

        ],
        dataList: [],
        columns: [
          {
            value: "levelName",
            label: "岗级名称",
            width: 120
          },
          {
            value: "levelCodePm",
            label: "职级、管理层级(P/M)"
          },
          {
            value: "levelSalary",
            label: "基础工资（城市最低标准要求）"
          },
          {
            value: "levelSalary",
            label: "基础工资（城市最低标准要求）"
          },
          {
            value: "postSalary2",
            label: "岗位工资（档级2）"
          },
          {
            value: "postSalary3",
            label: "岗位工资+1（档级3）"
          },
          {
            value: "postSalary4",
            label: "岗位工资+2（档级4）"
          },
          {
            value: "postSalary5",
            label: "岗位工资+3（档级5）"
          },
          {
            value: "postPerformanceSalary",
            label: "岗位绩效"
          },
          {
            value: "telephoneSalary",
            label: "电话补贴"
          },
          {
            value: "trafficSalary",
            label: "交通补贴"
          },
          {
            value: "mealSalary",
            label: "餐补"
          },
          {
            value: "fullSalary",
            label: "满勤奖"
          },
          {
            value: "levelDesc",
            label: "岗级描述",
            resize: "auto"
          },
          {
            value: "type",
            label: "所属分公司",
            formatter(row) {
                return row.type == '0' 
                ? "全部" 
                : row.type == '1' 
                ? "总部" 
                : row.type == '2' 
                ? "蚌埠分公司" 
                : row.type == '3' 
                ? "合肥分公司" 
                : row.type == '4' 
                ? "常州分公司" 
                : "";
            }
          },
          {
            value: "createdAt",
            label: "创建时间",
            width: 180,
            formatter(row) {
              return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          {
            value: "updatedAt",
            label: "更新时间",
            width: 180,
            formatter(row) {
              return date.dateFormat(row.updatedAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          {
            label: "操作",
            fixed: "right",
            width: 180,
            operType: "button",
            operations: [
              {
                label: "编辑",
                type: "primary",
                func: this.handleEdit,
              },
              {
                label: "删除",
                type: "danger",
                func: this.handleDelete,
                
              }
            ]
          }

        ],

      searchList: [
        {
          label: "岗位名称",
          labelWidth: "80px",
          type: "input",
          prop: "levelName"
        },
        {
          label: "职级、管理层级(P/M)",
          labelWidth: "160px",
          type: "input",
          prop: "levelCodePm"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "新增",
              icon: "el-icon-plus",
              func: this.handleAdd
            }
          ]
        }
      ]
    };

  },
  watch: {},
  mounted() {
    this.handleSearch();
  },
  methods: {

    handleAdd() {
      this.drawTitle = "新增薪酬";
      // this.drawForms[this.drawForms.length - 1].hidden = false;
      // this.drawForms[this.drawForms.length - 2].hidden = false;
      // this.drawForms[this.drawForms.length - 3].hidden = false;
      this.$refs.drawForm.open();
      this.initForm = { };
      // this.drawForms[this.drawForms.length - 1].disabled = true;
      this.$refs.drawForm.initforms(this.initForm);
    },


    handleDelete(row) {
      this.$confirm(`确定要删除该记录吗?`, "提示", {
        type: "warning"
      })
        .then(() => {
          this.tableLoading = true;
          deletetSalary({ id: row.id })
            .then(res => {
              if (res.code == 200) {
                this.$message.success("删除成功");
                this.handleSearch();
              } else {
                this.tableLoading = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },


    handleEdit(row) {
      this.drawTitle = "编辑薪酬";
      this.$refs.drawForm.open();
      this.$refs.drawForm.showLoading();
      getSalaryByid({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            this.$refs.drawForm.initforms(res.data);
            this.$nextTick(function() {
              this.initForm = Object.assign({}, this.$refs.drawForm.form);
            });
          }
          this.$refs.drawForm.hideLoading();
        })
        .catch(err => {
          console.log(err);
          this.$refs.drawForm.hideLoading();
        });
    },


    handleConfirm(obj) {
      this.$refs.drawForm.showLoading();
      const params = Object.assign({}, obj);
      if (obj.id) {
        updateSalary(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("更新成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      } else {
        addSalary(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("保存成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      }
    },
    

    close() {
        this.initForm = {};
        this.$refs.drawForm.close();
      },

      handleFormSearch(form) {
        this.pageIndex = 1;
        this.handleSearch(form);
      },

      tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

      handleSearch(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;
        this.tableLoading = true;
        queryPage(params).then(res => {
          if (res.code == 200) {
            this.total = res.data.total;
            this.dataList = res.data.records;
          }
          this.tableLoading = false;
        });
      }
  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>