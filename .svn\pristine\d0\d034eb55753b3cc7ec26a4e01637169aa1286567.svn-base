<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
      <el-row>
        <direct-search
          ref="form"
          :formDefaults="formDefaults"
          :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }"
          :forms="searchList"
          @handleSearch="handleFormSearch"
        />
      </el-row>
      <!-- :tab-type="'index'"
      :tab-label="'序号'"-->
      <el-col style="padding:15px 0">
        <el-table-self
          :columns="columns"
          :current-page="pageIndex"
          :list-loading="listLoading"
          :table-data="dataList"
          :total-count="total"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :tab-index="tabIndex"
          @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange"
        />
      </el-col>

      <draw-form
        ref="drawForm"
        :forms="drawForms"
        :inline-flag="true"
        @handleConfirm="handleConfirm"
        :label-width="'110'"
        :title="drawTitle"
      ></draw-form>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import drawForm from "@/components/FormComponents/draw";
import paginationMixin from "@/components/TabComponents/mixin";
import { createSubordinate } from "@/api/user";
import { getUsersList, saveUsers, deleteUsers, putUsers } from "./api";
import date from "@/utils/date";

export default {
  components: {
    elTableSelf,
    drawForm,
    directSearch
  },
  mixins: [paginationMixin],
  data() {
    return {
      drawTitle: "",
      listLoading: false,
      total: 0,
      id: 0,
      uploadObj: {},
      auditEdit: null,
      cacheForm: {},
      formDefaults: {},
      drawForms: [
        // {
        //   type: "input",
        //   class: "inputMore9",
        //   label: "可用-able，不可用-unable",
        //   prop: "status",
        //   placeholder: `请填写可用-able，不可用-unable`,
        // },
      ],
      fileList: [],
      dataList: [],
      columns: [
        {
          value: "name",
          width: 240,
          label: "部门名称"
        },
        {
          value: "parentId",
          label: "父节点"
        },
        {
          value: "disabled",
          label: "是否可用",
          formatter(row) {
            return row.disabled ? "不可用" : "可用";
          }
        },
        {
          label: "注册时间",
          value: "createdAt",
          formatter(row) {
            return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "更新时间",
          value: "updatedAt",
          formatter(row) {
            return date.dateFormat(row.updatedAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "操作",
          fixed: "right",
          width: 180,
          operType: "button",
          operations: [
            {
              label: "编辑",
              type: "primary",
              func: this.handleEdit
            },
            {
              label: "删除",
              type: "danger",
              func: this.handleDelete
            }
          ]
        }
      ],
      searchList: [
        {
          label: "部门名称",
          labelWidth: "120px",
          type: "input",
          prop: "name"
        },
        {
          label: "",
          labelWidth: "20px",
          type: "button",
          func: this.handleFormSearch,
          value: "查询",
          color: "primary"
        },
        {
          label: "",
          fixed: "right",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              value: "查询",
              color: "primary"
            },
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "新增",
              icon: "el-icon-plus",
              func: this.handleAdd
            }
          ]
        }
      ]
    };
  },
  watch: {},
  mounted() {
    this.handleSearch();
  },
  methods: {
    handleAdd() {
      this.drawTitle = "新增";
      this.$refs.drawForm.open();
      this.$refs.drawForm.initforms({
        name: "1",
        password: "1",
        realName: "1",
        sex: 1,
        mobile: "13101010202",
        groupId: 1,
        roleId: 1,
        subCom: "1",
        duty: "1",
        birthday: "2023-11-08",
        entryTime: "2023-11-16",
        leaveTime: "2023-11-25"
      });
    },
    handleEdit(row) {
      this.drawTitle = "编辑";
      this.$refs.drawForm.open();
      this.$refs.drawForm.initforms(row);
    },

    handleDelete(row) {
      this.$confirm(`确定要删除${row.realName}吗？`, "提示", {
        type: "warning"
      })
        .then(() => {
          this.tableLoading = true;
          deleteUsers({ id: row.id })
            .then(res => {
              if (res.code == 200) {
                this.$message.success("删除成功");
                this.handleSearch();
              } else {
                this.tableLoading = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },

    handleConfirm(row) {
      if (row.id) {
        putUsers(row).then(res => {
          if (res.code == 200) {
            this.$message.success("更新成功！");
            this.handleSearch();
            this.$refs.drawForm.close();
          }
        });
      } else {
        saveUsers(row).then(res => {
          if (res.code == 200) {
            this.$message.success("创建成功！");
            this.handleSearch();
            this.$refs.drawForm.close();
          }
        });
      }
    },

    handleFormSearch(form) {
      this.pageIndex = 1;
      this.handleSearch(form);
    },

    tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

    handleSearch(form) {
      this.cacheForm = this.cacheForm || form;
      const params = Object.assign(this.cacheForm, form);
      params.page = this.pageIndex;
      params.pageSize = this.pageSize;
      getUsersList(params).then(res => {
        if (res.code == 200) {
          this.total = res.data.total;
          this.dataList = res.data.records;
        }
        this.listLoading = false;
      });
    }
  }
};
</script>

<style lang="scss" scope>
.page-container .page-main {
  padding-top: 30px 25px;
}
.statistics {
  text-align: center;
  height: 120px;
  line-height: 120px;
}
</style>
