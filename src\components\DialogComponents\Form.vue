<template>
  <el-dialog
    center
    :title="title"
    :class="dialogComponent"
    :visible.sync="visible"
    :width="width"
    top="3%"
    :close-on-click-modal="closeModal"
    @close="close"
  >
    <div class="main">
      <el-form
        ref="form"
        :rules="rules"
        :model="form"
        label-position="right"
        :label-width="labelWidth"
        @submit.native.prevent
      >
        <el-row>
          <template v-for="(item,index) in formData">
            <el-col
              v-if="!item.hidden"
              :key="item.field"
              :span="item.spanCount?item.spanCount:countLine"
              :class="item.class"
            >
              <el-form-item
                :key="index"
                :label="item.name?`${item.name}:`:''"
                :label-width="item.labelWidth"
                :prop="item.field"
                :rules="item.rules"
                :class="item.className"
                :style="item.style"
              >
                <!-- 按钮控制 -->
                <el-button
                  v-if="item.operation"
                  class="operBtn"
                  :type="item.operation.type?item.operation.type:'text'"
                  :autofocus="item.autofocus"
                  @click="item.operation.func"
                >{{ item.operation.text?item.operation.text:'新增' }}</el-button>

                <!-- 提示 -->
                <el-tooltip
                  v-if="item.tips"
                  slot="label"
                  effect="dark"
                  :content="item.tips"
                  placement="top"
                >
                  <span>{{ item.name }}：</span>
                </el-tooltip>

                <!-- 输入框 input -->
                <el-input
                  v-if="item.type === 'input'"
                  v-model="form[item.field]"
                  :type="item.inputType?item.inputType:''"
                  :maxlength="item.maxlength"
                  :minlength="item.minlength"
                  :placeholder="item.placeholder"
                  :clearable="item.clearable"
                  :disabled="item.disabled"
                  :resize="item.resize"
                  :rows="item.rows"
                  :autosize="item.autosize"
                  @input="item.func?item.func($event):{}"
                >
                  <span v-if="item.unit" :slot="item.slot?item.slot:'append'">{{ item.unit }}</span>
                </el-input>

                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="item.type === 'number'"
                  v-model="form[item.field]"
                  :min="item.min"
                  :max="item.max"
                  :precision="item.precision"
                  :controls="item.controls || false"
                  :disabled="item.disabled"
                  :placeholder="item.placeholder"
                  @input="item.func?item.func($event):{}"
                />

                <!-- 模糊查询输入 -->
                <el-autocomplete
                  v-else-if="item.type === 'autocomplete'"
                  v-model="form[item.field]"
                  popper-class="my-autocomplete"
                  :fetch-suggestions="item.func"
                  :placeholder="item.placeholder"
                  clearable
                  @select="item.selectfun"
                >
                  <template slot-scope="{ item }">
                    <div class="name">
                      {{ item.value }}
                      <span v-if="item.specification">({{ item.specification }})</span>
                    </div>
                    <span class="description">{{ item.description }}</span>
                  </template>
                </el-autocomplete>

                <!-- 选择器 -->
                <el-select
                  v-else-if="item.type === 'select'"
                  v-model="form[item.field]"
                  :multiple="item.multiple"
                  :disabled="item.disabled"
                  :placeholder="item.placeholder?item.placeholder:'请选择'"
                  filterable
                  clearable
                  :value-key="item.key?item.key:'value'"
                  @change="item.func?item.func($event):{}"
                >
                  <el-option
                    v-for="(opt,optIndex) in item.opts"
                    :key="optIndex"
                    :label="opt.label"
                    :value="item.isSelect?opt.selectValue:opt.value"
                  >
                    <span style="float: left">{{ opt.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 10px"
                    >{{ opt.description }}</span>
                  </el-option>
                </el-select>

                <!-- 模糊查询选择 -->
                <el-select
                  v-else-if="item.type === 'remote'"
                  v-model="form[item.field]"
                  filterable
                  remote
                  reserve-keyword
                  :placeholder="item.placeholder"
                  :remote-method="item.remoteFunc"
                  @change="item.func?item.func($event):{}"
                >
                  <el-option
                    v-for="(opt,optIndex) in item.opts"
                    :key="optIndex"
                    :label="opt.label"
                    :value="item.isSelect?opt.selectValue:opt.value"
                  />
                </el-select>

                <!-- 日期 -->
                <el-date-picker
                  v-else-if="item.type === 'date'"
                  v-model="form[item.field]"
                  :type="item.dateType?item.dateType:'date'"
                  :placeholder="item.placeholder"
                  :picker-options="item.options"
                  :format="item.format"
                  :value-format="item.valueFormat"
                  @change="item.func?item.func($event):{}"
                />

                <!-- 图片 -->

                <img-preview
                  size="mini"
                  v-else-if="item.type === 'img'"
                  :removeEnabled="false"
                  :uploadEnabled="false"
                  :fileList="form[item.field]"
                ></img-preview>

                <!-- 单选框 -->
                <el-radio
                  v-for="(opt,optIndex) in item.opts"
                  v-else-if="item.type === 'radio'"
                  :key="optIndex"
                  v-model="form[item.field]"
                  :class="item.class"
                  :label="opt.value"
                  @change="item.func?item.func($event):{}"
                >{{ opt.label }}</el-radio>

                <!-- 多选框 -->
                <el-checkbox-group v-else-if="item.type === 'checkbox'" v-model="form[item.field]">
                  <el-checkbox
                    v-for="(opt,optIndex) in item.opts"
                    :key="optIndex"
                    :label="opt.label"
                  />
                </el-checkbox-group>

                <!-- 级联 -->
                <el-cascader
                  v-else-if="item.type === 'cascader'"
                  v-model="form[item.field]"
                  :options="item.list"
                  :clearable="true"
                  :filterable="true"
                />

                <!-- 树形 -->
                <el-tree
                  v-else-if="item.type === 'tree'"
                  ref="tree"
                  :data="treeList"
                  show-checkbox
                  :node-key="item.nodeKey||'id'"
                  :props="item.props || defaultProps"
                />

                <el-upload
                  v-else-if="item.type==='upload'"
                  action="''"
                  :file-list="attachmentList"
                  :limit="1"
                  :multiple="false"
                  :before-remove="beforeRemove"
                  :before-upload="beforeUpload"
                  :accept="accept"
                  :http-request="handleUpload"
                  list-type="picture-card"
                  :on-remove="handleRemove"
                >
                  <i class="el-icon-plus"></i>
                </el-upload>

                <el-row v-else-if="item.type === 'chat'" style="max-height:300px;overflow-y: auto;">
                  <div v-for="(item,index) in form[item.field]" :key="index">{{ item.value }}</div>
                </el-row>

                <!-- 导入文件 -->
                <template v-else-if="item.type === 'import'">
                  <div class="import-container" @click="handleImport">
                    <span v-if="form[item.field]">{{ form[item.field] }}</span>
                    <span v-else class="wait">{{ item.placeholder }}</span>
                    <i class="el-icon-upload" />
                  </div>
                  <input
                    ref="fileInput"
                    type="file"
                    class="import-file"
                    :accept="item.accept"
                    @change="handleFileChange($event)"
                  />
                </template>

                  <span v-else-if="item.type === 'note'">{{ item.label }}</span>

                <!-- <img v-else-if="item.type === 'image'" :src="form[item.field]" />
                <span v-else-if="item.type === 'span'">{{form[item.field]}}</span>-->
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <template v-if="footerBtn&&footerBtn.length == 0">
        <el-button v-if="btnCancel" size="small" @click="visible = false;">取消</el-button>
        <el-button size="small" type="primary" :loading="loading" @click="handleConfirm">确认</el-button>
      </template>
      <template v-else>
        <el-button
          v-for="(item,index) in footerBtn"
          :key="index"
          size="small"
          type="primary"
          :loading="loading"
          @click="handleConfirm(item.value)"
        >{{item.name}}</el-button>

        <!-- <el-button size="small" type="primary" :loading="loading" @click="handleConfirm(3)">个性签名违规</el-button>
        <el-button size="small" type="primary" :loading="loading" @click="handleConfirm(2)">头像违规</el-button>
        <el-button size="small" type="primary" :loading="loading" @click="handleConfirm(1)">昵称违规</el-button>-->
      </template>
    </div>
  </el-dialog>
</template>

<script>
import { uploadFile, deleteFile } from '@/api/file'
import imgPreview from '../UploadExcel/imgPreview'
export default {
  components: {
    imgPreview
  },
  props: {
    closeModal: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '系统提示'
    },
    countLine: {
      // 一行排列几个
      default: 24
    },
    width: {
      type: String,
      default: '365px'
    },
    labelWidth: {
      type: String,
      default: '100px'
    },
    btnCancel: {
      type: Boolean,
      default: true
    },
    footerBtn: {
      type: Array
    },
    formData: {
      type: Array
    },
    formEdit: {
      type: Object
    },

    rules: {
      type: Object
    },
    dialogComponent: {
      type: String,
      default: 'dialog-component'
    },
    treeList: {
      type: Array
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      form: {},
      imageUrl: '',
      attachmentList: [],
      accept: 'image/*',
      isSave: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.initforms()
      }
    }
  },
  methods: {
    changeField(field, value) {
      let _this = this
      this.$nextTick(() => {
        _this.$set(_this.form, field, value)
      })
    },
    beforeRemove(file, fileList) {
      return this.$confirm('确认删除该文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    },
    handleRemove(file, fileList) {
      const uid = file.uid || file.raw.uid
      // 删除图片
      const obj = this.attachmentList.find(item => item.uid == uid)
      const idx = this.attachmentList.findIndex(item => item.uid == uid)
      this.attachmentList.splice(idx, 1)
      console.log(this.attachmentList)
      // deleteFile(obj).then(res => {
      //     if (res.code === 1) {
      //         this.$message.success('删除成功')
      //     }
      // })
    },
    beforeUpload(file) {
      this.attachmentList = []
      return new Promise((resolve, reject) => {
        if (file.type.indexOf('image') == -1) {
          this.$message.error('只能上传图片')
          reject()
        }
        resolve()
      })
    },
    // 上传
    async handleUpload(row) {
      const file = row.file
      const formData = new FormData()
      formData.append('file', file)
      uploadFile(formData).then(res => {
        if (res.code === 1) {
          this.$message.success('上传成功')
          let obj = {
            id: res.data[0].id,
            url:
              'http://www.shareee.com.cn/registration-resource' +
              res.data[0].filePath
          }
          this.attachmentList.push(obj)
        }
      })
    },

    open() {
      this.visible = true
    },

    close() {
      this.attachmentList = []
      this.loading = false
      this.visible = false
    },

    handleConfirm(val) {
      this.loading = true
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.$refs.tree) {
            const halfChecks = this.$refs.tree[0].getHalfCheckedKeys()
            const checks = this.$refs.tree[0].getCheckedKeys()
            this.form.treeList = [...new Set(checks.concat(halfChecks))]
          }
          this.$emit('handleConfirm', this.form, val)
        } else {
          this.loading = false
        }
      })
    },

    // 初始化整个表单（可在初始化时赋值）
    initforms(formEdit) {
      const form = {}
      this.formData.forEach(item => {
        if (!item.field || item.hidden) return false
        if (item.type === 'select-input' || item.type === 'input-input') {
          form[item.field1] = ''
          form[item.field2] = ''
        } else if (
          item.type === 'checkbox' ||
          item.type === 'cascader' ||
          item.multiple
        ) {
          form[item.field] = []
        } else {
          form[item.field] = ''
        }
      })
      // 树形菜单
      if (this.treeList) {
        this.$nextTick(() => {
          this.$refs.tree[0].setCheckedKeys([])
          const list = this.formEdit ? this.formEdit.treeList : []
          list.forEach(item => {
            this.$refs.tree[0].setChecked(item, true)
          })
        })
        if (this.formEdit && this.formEdit.imageUrl) {
          this.imageUrl = this.webDomain + this.formEdit.imageUrl
        }
      }
      if (formEdit) {
        this.form = Object.assign(form, formEdit)
      }
      if (this.formEdit) {
        this.form = Object.assign(form, this.formEdit)
      } else {
        this.form = Object.assign({}, form)
      }
      this.loading = false

      this.$nextTick(() => {
        if (this.$refs.fileInput && this.$refs.fileInput[0]) {
          this.$refs.fileInput[0].value = ''
        }
        this.$refs.form.clearValidate()
      })
    },

    // 给表单部分字段赋值（此时表单已初始化）
    initFields(obj) {
      for (const key in obj) {
        this.form[key] = obj[key]
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    }
  }
}
</script>

<style lang="scss">
.dialog-component {
  .el-form-item {
    margin-bottom: 15px;
  }
  .el-select,
  .el-input,
  .el-input-number {
    width: 100%;
  }
  .el-input-number .el-input__inner {
    text-align: left;
  }
  .input-with-select {
    .el-input__suffix {
      position: relative !important;
      right: 20px;
      font-size: 14px;
    }
    .el-input__inner {
      width: 130px;
    }
    .el-input--mini .el-input__inner {
      text-align: center;
    }
  }
  .el-input-group__prepend {
    padding: 0;
  }
  .input-with-input {
    .el-input-group__prepend {
      background-color: #ffffff;
      width: 120px;
      padding: 0;
      border: 0;
    }
  }
  .red-background .el-input__inner {
    background: #c92639;
    color: #fff;
    border-color: #c30d23 !important;
  }
  .blue-background .el-input__inner {
    background: #0097ff;
    color: #fff;
    border-color: #036eb8 !important;
  }
}
</style>

<style lang="scss" scoped>
.avatar-line {
  text-align: center;
  img {
    width: 120px;
  }
}

.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;
    .name {
      text-overflow: ellipsis;
      overflow: hidden;
      + .description,
      span {
        font-size: 12px;
      }
      + .description {
        color: #b4b4b4;
      }
    }

    .highlighted .description {
      color: #ddd;
    }
  }
}

.dialog-component .el-input__suffix {
  position: relative !important;
}

.el-autocomplete {
  width: 100%;
}
</style>
<style>
.drugDetail .boold .el-form-item__content,
.diagnosis .boold .el-form-item__content {
  margin-left: 0px !important;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style>
.hide .el-upload--picture-card {
  display: none;
}
.main .file-preview__wrapper {
  justify-content: left;
}
</style>

