/** When your routing table is too long, you can split it into small modules**/
import Layout from '@/layout'

const newsRoute = [{
    path: '/news',
    component: Layout,
    name: 'news',
    redirect: 'news',
    children: [{
      path: 'newsInfo',
      component: () => import('@/views/news/index'),
      name: 'newsInfo',
      meta: {
        title: '系统消息',
        icon: 'news',
        value: 'news_index'
      }
    }]
  }

]

export default newsRoute
