<template>
  <el-drawer
    :title="title"
    v-if="drawer"
    custom-class="drawer-form cust-vip-visit"
    :size="drawerSize"
    :visible.sync="drawer"
    :wrapperClosable="false"
    :direction="direction"
    :destroy-on-close="true"
  >
    <el-row class="basic-info">
      <template v-for="(item, index) in forms">
        <div v-if="item.type == 'title'" class="part-title" :key="index">
          <span class="border"></span>
          <span class="title">{{ item.title }}</span>
        </div>
        <div v-else class="basic-info-item" :key="index">
          <span class="label">{{ item.label }}：</span>
          <span class="value" v-if="item.type == 'phones'">
          </span>
          <span class="value" v-else>{{
            item.formatter ? item.formatter(form) : form[item.prop]
          }}</span>
        </div>
      </template>
      <el-form
        :label-position="labelPosition"
        label-width="110px"
        :inline="true"
        :model="formVisit"
      >
    
        <el-form-item label="下次回访日期" required>
          <!-- 日期 -->
          <el-date-picker
            class="el-input--small-8"
            :clearable="false"
            v-model="formVisit.nextVisit"
            type="date"
            :value-format="'yyyy-MM-dd'"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
    
        <el-form-item label="回访记录" class="visit-records" required>
          <el-input
            type="textarea"
            :placeholder="visitPlaceholder"
            :autosize="{ minRows: 4, maxRows: 8 }"
            v-model="formVisit.content"
          ></el-input>
        </el-form-item>

        <el-form-item style="text-align: right; width: 100%">
        
          <el-button type="primary" @click="addRecordData(false)"
            >添加回访记录</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>
    <el-row style="margin-top: 20px; margin-bottom: 10px">
      <el-tabs v-model="activeName" @tab-click="handleClick">
       

         <el-tab-pane
          :label="'回访记录'"
          name="first"
        >
          <el-col
            v-for="item2 in form.fwRecordList"
            :key="item2.id"
            style="border-left: 5px solid #5FB878; font-size:14px;   border-radius: 0 2px 2px 0"
          >
            <div style="border-bottom: 1px dashed #c2c2c2;padding: 10px 15px;">   
              <div style="margin-top:20px"> 
                <span>下次回访日期：{{ item2.nextVisit }}</span>
                <span style="margin-left:20px">
                  添加回访记录时间：{{ item2.createdAt }}
                </span>
                <span style="margin-left:20px">
                  回访记录：{{ item2.content }}
                </span>
                <span>
                  <el-button type="danger" @click="deleteRecordData(item2.id)"
                    >删除回访记录</el-button>
                </span>
              </div>
            </div>
          </el-col>
        </el-tab-pane> 

      </el-tabs>
    </el-row>
    <div class="drawer-footer detail-footer">
      <el-button size="medium" @click="close">关 闭</el-button>
    </div>
  </el-drawer>
</template>

<script>
// import date from "@/utils/date";
import { addRecord,deleteRecord } from "./api";
import { mapGetters } from "vuex";
export default {
  props: {
    size: { type: String, default: "small" },
    direction: { type: String, default: "rtl" },
    drawerSize: { type: String, default: "80%" },
    valueWidth: { type: String, default: "" },
    labelPosition: { type: String, default: "right" },
    labelWidth: { type: String },
    formStyle: { type: Object },
    inlineFlag: { type: Boolean, default: false },
    lists: { type: Array }, // 按钮组
    btnStyle: { type: String, default: "margin-top:30px;text-align:center" }
  },
  computed: {
    ...mapGetters(["userInfo"])
  },
  data() {
    return {
      disabledCustTypeId: false,
      wxList: [],
      title: "",
      pickerOptions: {},
      visitPlaceholder: "",
      formLoading: false,
      drawer: false,
      form: {
        fwRecordList: []
       
      },
      forms: [
        {
          type: "title",
          title: "法务基础信息"
        },

        {
          label: "公司名称",
          prop: "cname",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },

        {
          label: "客户姓名",
          prop: "uname",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },

        {
          label: "联系方式",
          prop: "phone",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },

        {
          label: "城市",
          prop: "city",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          label: "首次服务时间",
          type: "date",
          prop: "firstServiceTime",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          label: "收款人",
          prop: "payee",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          label: "服务项目",
          prop: "serviceItems",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          label: "服务年限",
          prop: "serviceYear",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          label: "剩余款",
          prop: "surplusAmount",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          label: "下次跟进时间",
          type: "date",
          prop: "nextVisitTime",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          label: "结束日期",
          type: "date",
          prop: "endDate",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          label: "已使用合同份额",
          prop: "contractShare",
          xl: 4,
          lg: 4,
          md: 4,
          sm: 4,
          xs: 4
        },
        {
          type: "title",
          title: "添加回访记录"
        }
      ],
      formVisit: {
        content: "",
        fwClientId: "",
        nextVisit: ""
      },
      activeName: "first"
    };
  },
  methods: {

    addRecordData(flag) {
      if (!this.formVisit.content) {
        this.$message.warning("请填写回访记录");
        return;
      }

      if (!this.formVisit.nextVisit) {
        this.$message.warning("请填写下次回访日期");
        return;
      }

      this.formVisit.fwClientId = this.form.id;
      addRecord(this.formVisit).then(res => {
        if (res.code == 200) {
          this.$message.success("添加成功");
          this.close();
        }
      });
    },




     //删除回访记录
     deleteRecordData(recordId) {
      debugger
      this.$confirm(`确定要删除吗？`, "提示", {
        type: "warning"
      })
        .then(() => {
          this.tableLoading = true;
          deleteRecord({ id: recordId })
            .then(res => {
              if (res.code == 200) {
                this.$message.success("删除成功");
                this.close();
              } 
            })
            .catch(err => {
              console.log(err);
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },



    open() {
      this.drawer = true;
    },

    handleClick(tab, event) {
      console.log(tab, event);
    },

    close() {
      this.drawer = false;
      this.formVisit = {
        content: "",
        fwClientId: ""
      };
    },

    showLoading() {
      this.formLoading = true;
    },

    hideLoading() {
      this.formLoading = false;
    },
    // 表单赋值
    initforms(formEdit) {
    debugger
      this.$nextTick(() => {
        const form = {};
        this.forms.forEach(item => {
          if (!item.prop || item.hidden) return false;
          if (
            item.type === "daterange" ||
            item.type === "checkboxList" ||
            item.type === "checkbox" ||
            item.type === "uploadImg" ||
            item.type === "cascader" ||
            (item.type === "select" && item.multiple)
          ) {
            form[item.prop] = [];
          } else {
            form[item.prop] = "";
          }
        });
        if (formEdit) {
          this.form = Object.assign(form, formEdit);
        } else {
          this.form = Object.assign({}, form);
        }
        this.loading = false;
        this.title = "回访:" + this.form.uname; //+ "-" + this.form.clientTypeName;
        this.formVisit.fwClientId = this.form.id;
   
      });
    },
    // 绑定部分值(此时表单已渲染)
    initfields(obj) {
      debugger
      this.form = Object.assign(this.form, obj);
    }
  }
};
</script>

<style lang="scss">
.cust-vip-visit {
  .el-drawer__header {
    background: #f8f9fb;
    padding: 0 16px;
    margin: 16px;
    height: 50px;
    font-family: PingFangSC-Medium;
    font-weight: 600;
    font-size: 18px;
    color: #303133;
    line-height: 50px;
  }
  .el-drawer__body {
    padding: 0 24px 64px 36px;

    .basic-info {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      .part-title {
        width: 100%;
        height: 30px;
        margin: 20px 0;
        background: rgb(51, 102, 255, 0.05);
        color: #303133;
        font-weight: 600;
        padding-left: 8px;
        &:first-child {
          margin-top: 0;
        }
        .border {
          display: inline-block;
          width: 3px;
          margin-top: 8px;
          height: 14px;
          border-left: 3px solid #3366ff;
        }

        .title {
          display: inline-block;
          margin-left: 6px;
          height: 30px;
        }
      }
      .basic-info-item {
        display: inline-block;
        margin: 10px 0;
        .value {
          display: inline-block;
          margin-right: 45px;
        }
      }
    }
    .item-record {
      line-height: 36px;
      padding: 5px 0;
      font-size: 14px;
      border-bottom: 1px dashed #c2c2c2;
      .el-col {
        span {
          margin-left: 20px;
          &:first-child {
            margin-left: 0;
          }
        }
      }
      .right-content {
        text-align: right;
        span {
          color: #ee9636;
        }
      }
    }

    .visit-records {
      width: 100%;
      .el-form-item__content {
        width: calc(100% - 120px);
      }
    }
  }
  .drawer-footer {
    text-align: right;
    padding: 16px;
    z-index: 9;
    background-color: #fff;
    box-shadow: 0 -2px 8px 0 rgba(200, 201, 204, 0.3);
    bottom: 0;
    position: fixed;
    width: 580px;
    right: 0;
  }
}
</style>
