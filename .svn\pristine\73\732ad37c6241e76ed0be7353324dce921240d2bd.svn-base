<template>
  <el-row class="el-table-self">
    <el-table
      ref="selftab"
      v-loading="listLoading"
      :size="tabSize"
      :summary-method="getSummaries"
      :show-summary="showSummary"
      :span-method="spanMethod"
      :data="tableData"
      border
      :show-overflow-tooltip="true"
      :highlight-current-row="highLight"
      :height="tableHeight"
      :header-row-class-name="headerClass"
      :row-style="rowStyle"
      :cell-style="cellStyle"
      style="width: 100%;"
      @cell-click="cellClick"
      @row-click="rowClick"
      @sort-change="sortChange"
      @select="selectChange"
      @selection-change="selectionChange"
      @select-all="selectAll"
    >
      <el-table-column
        v-if="tabType"
        :show-overflow-tooltip="true"
        :type="tabType"
        width="55"
        align="center"
        :label="tabLabel"
        :selectable="selecTable"
        :index="tabIndex"
      />

      <template v-for="(column,index) in columns">
        <template v-if="!column.hidden">
          <el-table-column
            v-if="column.operations && !column.isHidden"
            :key="index"
            :show-overflow-tooltip="true"
            :fixed="column.fixed"
            :prop="column.value"
            :label="column.label"
            :width="column.width"
            align="center"
          >
            <template slot-scope="scope">
              <template
                v-for="(operate,operIndex) in column.operations.formatter?column.operations.formatter(scope.row):column.operations"
              >
                <template v-if="!operate.isHidden ||!operate.isHidden(scope.row,scope.$index)">
                  <el-button
                    v-if="column.operType === 'button' && !column.hidden"
                    :key="operIndex"
                    size="small"
                    :disabled="operate.formatter?operate.formatter(scope.row,scope.$index).disabled:operate.disabled"
                    :type="operate.formatter?operate.formatter(scope.row).type:operate.type||''"
                    :icon="operate.icon"
                    :plain="operate.plain"
                    :style="operate.style"
                    @click="operate.func?operate.func(scope.row,scope.$index):{}"
                  >{{ operate.formatter?operate.formatter(scope.row).label:operate.label?operate.label:scope.row[column.value] }}</el-button>

                  <template v-else-if="column.operType === 'icon' && !operate.hidden">
                    <el-button
                      :key="operIndex"
                      size="small"
                      type="text"
                      :icon="operate.iconClass"
                      :style="operate.style"
                      @click="operate.func(scope.row,scope.$index)"
                    >{{ operate.formatter?operate.formatter(scope.row).label:operate.label?operate.label:scope.row[column.value] }}</el-button>
                  </template>
                </template>
              </template>
            </template>
          </el-table-column>

          <el-table-column
            v-else-if="!column.isHidden && column.func"
            :key="index"
            :show-overflow-tooltip="true"
            :fixed="column.fixed"
            :prop="column.value"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            align="center"
            :sortable="column.sortable"
            :formatter="column.formatter"
            :class-name="column.className"
          >
            <template slot-scope="scope">
              <span
                :style="column.style"
                @click="column.func(scope.row,scope.$index)"
              >{{ column.formatter?column.formatter(scope.row).label:scope.row[column.value] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            v-else-if="!column.isHidden && column.type == 'img'"
            :key="index"
            :fixed="column.fixed"
            :prop="column.value"
            :width="column.width"
            :label="column.label"
            :min-width="column.minWidth"
            align="center"
            :sortable="column.sortable"
            :formatter="column.formatter"
            :class-name="column.className"
          >
            <template slot-scope="scope">
              <!-- <img :src="scope.row[column.value]" alt class="column-img" /> -->
              <img-preview
                size="mini"
                :removeEnabled="false"
                :uploadEnabled="false"
                :fileList="scope.row[column.value]"
              ></img-preview>
            </template>
          </el-table-column>

             <el-table-column
            v-else-if="!column.isHidden && column.type == 'baseImg'"
            :key="index"
            :fixed="column.fixed"
            :prop="column.value"
            :width="column.width"
            :label="column.label"
            :min-width="column.minWidth"
            align="center"
            :sortable="column.sortable"
            :formatter="column.formatter"
            :class-name="column.className"
          >
            <template slot-scope="scope">
              <img-preview
                v-if="scope.row.objectName == 'RC:ImgMsg'"
                size="mini"
                type="baseImg"
                :removeEnabled="false"
                :uploadEnabled="false"
                :fileList="scope.row[column.value]"
              ></img-preview> 
<!-- data:audio/wav;base64, -->
              <!-- <m-audio  v-else-if="scope.row.objectName == 'RC:VcMsg'" :src="scope.row[column.value]"  :showDuration="true"></m-audio> -->
              <!-- <audio v-else-if="scope.row.objectName == 'RC:VcMsg'" :src="'data:audio/amr;base64,'+scope.row[column.value]" controls="controls"></audio> -->
              <!-- <audio controls="controls" autobuffer="autobuffer" autoplay="autoplay">
<source src="data:audio/wav;base64,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" />
</audio> -->

              <!--  -->
              <!-- <img :src="scope.row[column.value]" alt class="column-img" /> -->
              <!-- <img :src="'data:image/png;base64,'+scope.row[column.value]" alt=""> -->
              <span v-else-if="scope.row.objectName == 'RC:TxtMsg'">
                {{scope.row[column.value]}}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            v-else-if="!column.isHidden && column.type == 'tag'"
            :key="index"
            :fixed="column.fixed"
            :prop="column.value"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            align="center"
            :sortable="column.sortable"
            :formatter="column.formatter"
            :class-name="column.className"
          >
            <template slot-scope="scope">
              <el-tag
                :type="column.tagTypeformatter?column.tagTypeformatter(scope.row):scope.row[column.tagType]"
              >{{ column.formatter?column.formatter(scope.row):scope.row[column.value] }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            v-else-if="!column.isHidden"
            :key="index"
            :show-overflow-tooltip="true"
            :fixed="column.fixed"
            :prop="column.value"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            align="center"
            :sortable="column.sortable"
            :formatter="column.formatter"
            :style="column.style"
            :class-name="column.className"
            @click.native="column.func?column.func:{}"
          />
        </template>
      </template>
    </el-table>
    <div v-if="pageSize" class="pagination-footer">
      <span class="description">{{ description }}</span>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-row>
</template>

<script>
import imgPreview from '../UploadExcel/imgPreview'
export default {
  components: {
    imgPreview
  },
  name: 'ElTableSelf',
  props: {
    listLoading: Boolean, // tab 加载层
    highLight: { type: Boolean, default: true },
    headerClass: { type: String, default: 'default' }, // 头部背景色Class名称，默认default
    tabType: String, // 对应列的类型，selection/index/expand
    tabLabel: String,
    tableHeight: Number, // 表格的高度
    tabSize: { type: String },
    tableData: Array, // 表格数据
    columns: Array, // 表格列配置数据,{vlaue:对应数据对象中的属性，label：对应的是标题文字，fixed:列是否固定，width:列宽， sortable：是否可排序，formatter:列格式化， className：对应的是列的样式类名}
    sortChange: { type: Function, default: () => {} }, // 点击列表头进行排序 { column, prop, order }
    description: String, // 分页脚底左侧的数据说明
    totalCount: Number, // 表格数据总数
    pageSizes: Array, // 决定每页显示的条数[10,15,20,25]
    pageSize: Number,
    spanMethod: Function,
    operType: { type: String, default: 'button' },
    getSummaries: Function,
    showSummary: { type: Boolean, default: false },
    type: { type: String, default: 'text' },
    currentPage: { type: Number, default: 1 },
    selecTable: Function,
    disabled: { type: Boolean, default: false },
    tabIndex: Function,
    cellStyle: Function,
    rowStyle: Function
  },
  methods: {
    // 切换页面显示条数
    handleSizeChange(val) {
      this.$emit('pageSizeChange', val)
    },

    // 跳转页码
    handleCurrentChange(val) {
      this.$emit('currentPageChange', val)
    },

    // 复选框事件
    selectionChange(selections) {
      this.$emit('selectionChange', selections)
    },

    // 复选框选中当前行事件
    selectChange(selections, row) {
      this.$emit('selectChange', selections, row)
    },

    // 行点击
    rowClick(row, event) {
      this.$emit('rowClick', row, event)
    },

    // 列点击
    cellClick(row, column, cell, event) {
      this.$emit('cellClick', row, column, cell, event)
    },

    // 单选行高亮
    setCurrentRow(row) {
      this.$refs.selftab.setCurrentRow(row)
    },

    // 全选事件
    selectAll(selections) {
      this.$emit('selectAll', selections)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table-self {
  font-size: 14px !important;
  .tab-svg {
    width: 24px;
    height: 24px;
    cursor: pointer;
    vertical-align: middle;
  }
}
.pagination-footer {
  margin-top: 10px;
  .description {
    float: left;
    margin-left: 20px;
    margin-top: 12px;
    font-size: 14px;
  }
  .el-pagination {
    float: right;
    margin-top: 8px;
    margin-bottom: 8px;
  }
}
.el-table__empty-block {
  position: relative;
  min-height: 60px;
  text-align: center;
  width: 100%;
  height: 100%;
}

.el-table__empty-text {
  position: absolute;
  left: 50%;
  width: 110px;
  height: 110px;
  top: 50%;
  line-height: 220px;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: #5e7382;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.tableSpan {
  display: inline-block;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.isPlan {
  font-size: 12px;
  color: #fff;
  padding: 2px 5px;
  background: #c1242a;
  border-radius: 3px;
}
/deep/ .default {
  th {
    background-color: #eef5fd !important;
    color: #586276;
  }
}
/deep/ .el-table__footer-wrapper tbody td {
  background: oldlace !important;
}
.column-img {
  object-fit: cover;
}
</style>
