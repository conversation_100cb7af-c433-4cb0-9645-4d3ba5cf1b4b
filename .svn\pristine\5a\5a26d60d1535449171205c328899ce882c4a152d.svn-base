import {
    fetch,
    post,
    put,
    deleteHttp
  } from '@/utils/request'
  

  // 文化制度列表查询
  export const qryPage = data => {
    return fetch(`/attachment/qryPage`, data)
  }


   // 专业技能列表查询
   export const skillsPage = data => {
    return fetch(`/attachment/skillsPage`, data)
  }


   // 管理视角列表查询
   export const mgtEyePage = data => {
    return fetch(`/attachment/mgtEyePage`, data)
  }

   // 综合提升列表查询
   export const allUpPage = data => {
    return fetch(`/attachment/allUpPage`, data)
  }


  // 人力资源列表查询
  export const rlzyPage = data => {
    return fetch(`/attachment/rlzyPage`, data)
  }


    // 业务拓展列表查询
    export const ywtzPage = data => {
    return fetch(`/attachment/ywtzPage`, data)
  }



  //专业财会列表查询
  export const zyckPage = data => {
    return fetch(`/attachment/zyckPage`, data)
  }


    
  //法务分享列表查询
  export const fwfxPage = data => {
  return fetch(`/attachment/fwfxPage`, data)
}


  //新人入职训列表查询
  export const xrrzxPage = data => {
    return fetch(`/attachment/xrrzxPage`, data)
  }

  
  //操作手册
  export const czscPage = data => {
    return fetch(`/attachment/czscPage`, data)
  }



  //其他资料列表查询
  export const qtzlPage = data => {
    return fetch(`/attachment/qtzlPage`, data)
  }


 // 查询详情
   export const getById = data => {
    return fetch(`/attachment/${data.id}`, data)
  }

  

  // 文件预览
  export const selectOne = data => {
    return fetch(`/preview/onlinePreview`, data)
  }   
    
//文件上传
  export const uploadInfo = data => {
    return post(`/attachment/file/uploads`, data)
  }
  

//逻辑删除
export const updateDeleted = data => {
  return  put(`/attachment`, data)
}