<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <!-- <search id="header-search" class="right-menu-item" /> -->

        <!-- <error-log class="errLog-container right-menu-item hover-effect" /> -->

        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->

        <!-- <el-tooltip content="Global Size" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>-->
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <span class="user-name">{{ userInfo.name}}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <span style="display: block" @click="open">修改密码</span>
          </el-dropdown-item>
          <!-- <el-dropdown-item>
            <span style="display: block" @click="viewQrcode">修改个人信息</span>
          </el-dropdown-item> -->
          <el-dropdown-item>
            <span style="display: block" @click="logout">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <dialog-form
      ref="audit"
      :width="'560px'"
      :title="dialogTitle"
      :form-data="auditData"
      :form-edit="auditEdit"
      @handleConfirm="handleConfirm"
    />
  </div>
</template>

<script>
import dialogForm from "@/components/FormComponents/dialogForm";
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
// import Screenfull from '@/components/Screenfull'
// import SizeSelect from '@/components/SizeSelect'
// import Search from '@/components/HeaderSearch'
import {
  updatePwd,
  logoutUser
} from "@/views/userManage/api";
export default {
  components: {
    Breadcrumb,
    Hamburger,
    // Screenfull,
    dialogForm
  },
  data() {
    return {
      dialogTitle: "",
      auditEdit: null,
      auditData: [],
      pwdAuditData: [
        {
          type: "input",
          name: "旧密码",
          inputType: "password",
          field: "oldPwd",
          rules: [
            {
              required: true,
              message: "请输入旧密码",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          inputType: "password",
          name: "新密码",
          field: "newPwd",
          rules: [
            {
              required: true,
              message: "请输入新密码",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          name: "重复密码",
          inputType: "password",
          field: "retypePwd",
          rules: [
            {
              required: true,
              message: "请输入重复密码",
              trigger: "blur"
            }
          ]
        }
      ],

      personAuditData: [
        {
          type: "input",
          name: "账号",
          field: "loginName",
          rules: [
            {
              required: true,
              message: "请填写账号",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          name: "姓名",
          field: "name",
          rules: [
            {
              required: true,
              message: "请填写姓名",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          name: "职务",
          field: "duty"
        },
        {
          type: "input",
          name: "手机号码",
          field: "phone",
          rules: [
            {
              required: true,
              message: "请填写手机号码",
              trigger: "blur"
            }
          ]
        }
      ]
    };
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device','userInfo'])
  },
  methods: {
    open() {
      this.dialogTitle = "修改密码";
      this.auditData = this.pwdAuditData.concat([]);
      this.auditEdit = {
        oldPwd: "",
        newPwd: "",
        retypePwd: ""
      };
      this.$refs.audit.open();
    },
    viewQrcode() {
      this.dialogTitle = "修改个人信息";
      this.auditData = this.personAuditData.concat([]);
      this.auditEdit = Object.assign({}, this.$store.getters.userInfo);
      this.$refs.audit.open();
    },

    handleConfirm() {
      if (this.dialogTitle == "修改密码") {
        let data = this.$refs.audit.form;
        if (data.newPwd != data.retypePwd) {
          this.$message.warning("两次密码不一致！");
          this.$refs.audit.loading = false;
          return;
        } else {
          updatePwd(this.$refs.audit.form).then(res => {
            if (res.code == 200) {
              this.$message.success(res.message);
              this.$refs.audit.close();
            } else {
              this.$message(res.message);
              this.$refs.audit.loading = false;
            }
          });
        }
      } 
    },

    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    logout() {
      logoutUser().then(async res => {
        if (res.code == 200) {
          await this.$store.dispatch("user/logout");
          this.$router.push(`/login?redirect=${this.$route.fullPath}`);
        }
      });
    },
 
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
