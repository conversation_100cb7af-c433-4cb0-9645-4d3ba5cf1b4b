<template>
  <el-row
    ref="uploadImg"
    :class="[hiddenUpload ? 'hidden-upload' : '', 'upload-img']"
  >
    <el-upload
      class="upload-demo"
      list-type="picture-card"
      action
      :limit="limit"
      :multiple="limit > 1"
      :accept="accept"
      :on-change="uploadChange"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :file-list="fileList"
      :auto-upload="false"
    >
      <i class="el-icon-plus"></i>
    </el-upload>
    <div class="upload-tip" v-if="message">{{ message }}</div>
    <div class="upload-tip" v-else-if="fileSize">大小不超过{{ fileSize }}M</div>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </el-row>
</template>
<script>
import { uploadFile, originUrl } from "@/api/file";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: ""
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    hiddenUpload() {
      return this.fileList.length == this.limit;
    }
  },
  props: {
    fileList: { type: Array, default: () => [] },
    fileSize: { type: Number, default: 2 },
    limit: { type: Number, default: 1 },
    accept: {
      type: String,
      default: ".png,.jpg,.jpeg,.bmp"
    },
    message: {
      type: String,
      default: ""
    }
  },
  methods: {
    /*
    文件变动
    */
    async uploadChange(file, list) {
      console.log(11111111);
      console.log(file);
      const fileLastName = file.name.substring(
        file.name.lastIndexOf("."),
        file.name.length
      );
      const isLt = file.size / 1024 / 1024 < this.fileSize;
      if (
        !(
          fileLastName == ".png" ||
          fileLastName == ".jpg" ||
          fileLastName == ".bmp" ||
          fileLastName == ".jpeg"
        )
      ) {
        this.$message.error("上传文件只能是图片格式!");
        list.splice(list.length - 1, 1);
        return false;
      }
      if (!isLt) {
        this.$message.error(`上传文件大小不能超过 ${this.fileSize}MB!`);
        list.splice(list.length - 1, 1);
        return false;
      }
      const obj = {
        fileType: "4",
        fileName: file.name,
        user: { id: this.userInfo.id },
        file: ""
      };
      // await getBase64(list[0].raw).then(res => {
      //   obj.file = res.split(",")[1];
      // });

      this.$emit("showLoading");

      var formdata = new FormData();
      formdata.append("files", file.raw);
      //importDevice：请求接口 formdata：传递参数
      uploadFile(formdata)
        .then(res => {
          if (res.code == 200) {
            for (let i = 0; i < res.data.length; i++) {
              res.data[i].name = res.data[i].fileName;
              res.data[i].url = res.data[i].fullPathOriginal;
            }
            this.$emit("updateList", res.data);
          }
          this.$emit("hideLoading");
        })
        .catch(err => {
          console.log(err);
          this.$emit("hideLoading");
        });
    },

    /*
      文件限制
    */
    handleExceed(files, fileList) {
      this.$message.error(`最多可上传${this.limit || 9}个文件！`);
    },

    /*
    删除文件
    */
    handleRemove(file, list) {
      this.$emit("updateList", list);
    },

    /*
    预览文件
    */
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.el-upload-list--picture-card .el-upload-list__item {
  margin: 0;
}
.upload-demo {
  height: 100px;
}
</style>
<style lang="scss">
.v-modal {
  z-index: 2000 !important;
}
.upload-img {
  .upload-tip {
    font-size: 14px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: #c2c5cc;
    margin-top: 8px;
  }
  .upload-demo {
    margin-top: 10px;
  }
  .el-upload--picture-card {
    width: 100px !important;
    height: 100px !important;
    border-radius: 2px;
    border: 1px dashed #dcdee0;
    line-height: 100px;
  }
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px !important;
    height: 100px !important;
  }

  .el-upload {
    i {
      font-size: 18px;
      margin: 41px;
      color: #bfc1c8;
    }
    &:hover {
      background: #edf4ff;
      border: 1px dashed #3366ff;
      border-radius: 2px;
    }
    &:disabled {
      background: #f7f8fa;
      border: 1px dashed #dcdee0;
      border-radius: 2px;
    }
  }

  .el-upload-list__item {
    img {
      border-radius: 100%;
    }
  }
}
.hidden-upload {
  .el-upload--picture-card {
    display: none;
  }
}
</style>
