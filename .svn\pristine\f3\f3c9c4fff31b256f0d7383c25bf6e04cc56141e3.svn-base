import {
    fetch,
    post,
    put,
    deleteHttp
  } from '@/utils/request'
  
  // 查询列表
  export const findPage = data => {
    return fetch(`/payroll`, data)
  }


  //税后应发金额汇总
  export const getAfterTaxSalarySum = data => {
    return fetch(`/payroll/getAfterTaxSalarySum`, data)
  }


  // 查询详情
  export const getById = data => {
    return fetch(`/attachment/${data.id}`, data)
  }


  // 查询部门
  export const getDeptName = data => {
    return fetch(`/user/groups/findDeptList`,data)
  }

  
  
  // 工资单查询列表
  export const queryPage = data => {
    return fetch(`/attachment`, data)
  }


  // 工资单、考勤文件逻辑删除
  export const updateDeleted = data => {
    return  put(`/attachment`, data)
  }



  // 考勤查询列表
  export const selPage = data => {
    return fetch(`/attachment/selPage`, data)
  }

  //批量导入
  export const uploadInfo = data => {
    return post(`/attachment/file/uploads`, data)
  }
  


  //工资单模板下载
export const getPayrollTemplate = data => {
  return fetch(`/payroll/file/downExcel`, data, 'blob')
}


// 考勤下载模板
export const getAttendanceTemplate = data => {
  return fetch(`/attendance/file/downExcel`, data, 'blob')
}

// 工资单、考勤预览文件
export const getByFileId = data => {
  return fetch(`/attachment/${data.id}`, data)
}


  // 删除
  export const deletetPayroll = data => {
    return deleteHttp(`/payroll/${data.id}`)
  }



  //复制
  export const copyPayroll = data => {
    return post(`/payroll/copy`, data)
  }


// 导出
export const exporPayroll = data => {
  return fetch(`/payroll/payrollExport`, data, 'blob')
}