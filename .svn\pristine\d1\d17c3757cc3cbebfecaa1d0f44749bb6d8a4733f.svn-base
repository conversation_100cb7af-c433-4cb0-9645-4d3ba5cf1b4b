import {
  fetch,
  post,
  put,
  formDataPost,
  deleteHttp
} from '@/utils/request'

// 查询列表
export const getUsersList = data => {
  return fetch(`/user/groups`, data)
}

export const getAllGroupList = data => {
  return fetch(`/user/groups/all`, data)
}

// 保存客户
export const saveUsers = data => {
  return post(`/user/groups`, data)
}

// 更新客户
export const putUsers = data => {
  return put(`/user/groups`, data)
}

// 删除客户
export const deleteUsers = data => {
  return deleteHttp(`/user/groups/${data.id}`)
}
