/** @module Date */

/**
 * 格式化日期。如果value无法被new Date()转换为日期对象，返回空字符串，默认是YYYY-MM-DD。
 * @function dateFormat
 * 主要以下调用方式
 * 1、 返回当前日期年月日dateFormat()
 * 2、 返回当前日期时分秒dateFormat(null, 'hh:mm:ss')
 * 3、 返回当前日期年月日时分秒dateFormat(null, 'YYYY-MM-DD hh:mm:ss')
 * 4、 解析时间戳dateFormat(1545484848484, 'YYYY-MM-DD hh:mm:ss') 后面的格式也可以写成YYYY-MM-DD或hh:mm:ss
 */
const date = {
  dateFormat: (
    date = new Date(),
    format = 'YYYY-MM-DD'
  ) => {
    if(date){
      const d = new Date(date || new Date())
      const zeroize = val =>
        val < 10 ? `0${val}` : `${val}`
      return format.replace(
        /YYYY|MM|DD|hh|mm|ss/g,
        word =>
          ({
            YYYY: d.getFullYear(),
            MM: zeroize(d.getMonth() + 1),
            DD: zeroize(d.getDate()),
            hh: zeroize(d.getHours()),
            mm: zeroize(d.getMinutes()),
            ss: zeroize(d.getSeconds())
          }[word] || word)
      )
    }else{
      return '-'
    }
  },

  /**
     * 获取星期几，lang代表输出语言。date为日期，可以是任意能被new Date()格式化为日期对象的值。
     * @function getWeekday
     * @param {string} [lang='zh'] - 可选，输出语言，默认为'zh'，当该值为undefined时取'zh'——表示中文，'en'——表示英文。
     * @param {data} [date=new Date()] - 可选，日期，默认为当前日期。
     * @return {string}
     * 主要以下调用方式
     * 1、获取当天 getWeekday()
     * 2、获取某个具体日期 getWeekday('2019-04-26')或者getWeekday('2019/04/26')
     * 3、解析时间戳 getWeekday(153454878787)
     * 如果是获取英文的星期几，加一个参数即可 getWeekday('en')
     */
  getWeekday: (
    lang = 'zh',
    date = new Date()
  ) => {
    const day = new Date(date).getDay()
    return lang === 'en'
      ? [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday'
      ][day]
      : '星期' +
            '日一二三四五六'.charAt(day)
  },
  /**
     * 返回距离date为n天的日期
     * @function spreadDate
     * @param {number} n - 天数。当n为负数，返回过去的日期；当n为正数，返回未来的日期。
     * @param {date} [date=new Date()] - 可选，日期，默认为当前日期。
     * @return {date}
     * 调用方法与dateFormat类似，注意此方法调用时前面加上dateFormat方法
     * 比如当前日期的上一周（只要年月日） dateFormat(spreadDate(-7)) 和下一周（年月日时分秒）dateFormat(spreadDate(7), 'YYYY-MM-DD hh:mm:ss')
     * 也可以接收其它日期格式 dateFormat(spreadDate(1, '2018-07-09 11:11:12'), 'YYYY-MM-DD hh:mm:ss') 其中的日期格式也可以是2018/07/09
     */
  spreadDate: (n, date = new Date()) => {
    const d = new Date(date || new Date())
    return new Date(
      +d + n * 24 * 60 * 60 * 1000
    )
  }
}

export default date
