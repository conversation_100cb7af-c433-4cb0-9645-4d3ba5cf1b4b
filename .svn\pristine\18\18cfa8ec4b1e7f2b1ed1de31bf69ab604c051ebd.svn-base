<template>
  <el-row class="page-container">
    <el-row class="page-main">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self :columns="columns" :current-page="pageIndex" :list-loading="tableLoading" :table-data="dataList"
          :total-count="total" :page-sizes="pageSizes" :page-size="pageSize" @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>

      <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true"
        @handleConfirm="handleConfirm" :label-width="'90'" :title="drawTitle"></draw-form>
    </el-row>
  </el-row>

</template>

<!-- <template>
  <el-row>
    <el-divider content-position="left">其它附件</el-divider>
    <el-form-item label="">
                <div class="import-container">
                  <el-upload
                    action=""
                    :auto-upload="false"
                    :multiple="false"
                    class="upload-demo"
                    :limit="10"
                    :on-remove="handleRemove"
                    :on-change="uploadFileData"
                    :file-list="dataList"
                  >
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">
                      单个文件大小不超过2M
                    </div>
                  </el-upload>
                </div>
              </el-form-item>
  </el-row>
</template> -->



<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import { getUsers, queryPage, getContractById, getUserName,addContract,updateContract } from "./api";

export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
      total: 0,
      id: 0,
      drawTitle: "",
      dataList: [],
      drawForms: [
        {
          label: "员工姓名",
          type: "select",
          diyLabel: "name",
          diyValue: "id",
          isSelect: true,
          popperClass: "select-user-name",
          prop: "userId",
          dataOrigin: "userName",
          opts: []
        },
        {
          type: "input",
          label: "合同编号",
          prop: "contractNo",
          placeholder: "请填写合同编号",
          class: "inputMore9",
          note:
            "<div>合同编号填写规则：省份首字母/城市首字母/年份+4位顺序号</div>",
          rules: [
            {
              required: true,
              message: "请填写合同编号",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "合同主体",
          prop: "contractName",
          placeholder: `请填写合同主体`,
          class: "inputMore9",
          note:
            "<div>合同主体：填写公司名称，写全称</div>",
          rules: [
            {
              required: true,
              message: "请填写合同主体",
              trigger: "blur"
            }
          ]
        },
        {
          type: "radio",
          prop: "isRenewal",
          maxlength: 100,
          opts: [
            {
              label: "是",
              value: 1
            },
            {
              label: "否",
              value: 2
            }
          ],
          label: "合同是否已续签订"
        },
        {
          type: "date",
          class: "inputMore9",
          label: "合同签订日期",
          prop: "signDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择合同签订日期`,
          rules: [
            {
              required: true,
              message: "请选择合同签订日期",
              trigger: "blur"
            }
          ]
        },
        {
          type: "radio",
          prop: "oneTimes",
          maxlength: 100,
          opts: [
            {
              label: "是",
              value: 1
            },
            {
              label: "否",
              value: 2
            }
          ],
          label: "是否为首次签"
        },
        {
          type: "input",
          class: "inputMore9",
          label: "合同签订次数",
          prop: "times",
          placeholder: `请输入合同签订次数`,
          rules: [
            {
              required: true,
              message: "请输入合同签订次数",
              trigger: "blur"
            }
          ]
        },
        {
          type: "date",
          class: "inputMore9",
          label: "合同起始日期",
          prop: "startDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择合同起始日期`,
          rules: [
            {
              required: true,
              message: "请选择合同起始日期",
              trigger: "blur"
            }
          ]
        },
        {
          type: "date",
          class: "inputMore9",
          label: "合同到期日期",
          prop: "endDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择合同到期日期`,
          rules: [
            {
              required: true,
              message: "请选择合同到期日期",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          class: "inputMore9",
          label: "合同类型",
          prop: "contractType",
          placeholder: `请选择合同类型`,
          opts: [
            {
              label: "固定期限",
              value: 1
            },
            {
              label: "无固定期限",
              value: 2
            },
            {
              label: "实习期合同",
              value: 3
            },
          ],
          rules: [
            {
              required: true,
              message: "请选择合同类型",
              trigger: "change"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "合同年限（年）",
          prop: "contractLimit",
          placeholder: `请填写合同年限`,
          rules: [
            {
              required: true,
              message: "请填写合同年限",
              trigger: "blur"
            }
          ]
        },
      ],
      dataList: [],
      columns: [
        {
          label: "员工",
          value: "userName"
        },
        {
          label: "合同编号",
          value: "contractNo",
          copy: true,
          // hidden: true,
          width: 140
        },
        {
          label: "合同主体",
          value: "contractName"
        },

        {
          label: "合同是否已续签订",
          value: "isRenewal",
          formatter(row) {
            return row.isRenewal == 1 ? "是" : row.isRenewal == 2 ? "否" : "";
          }
        },
        {
          label: "合同签订日期",
          value: "signDate",
          formatter(row) {
            return date.dateFormat(row.signDate, "YYYY-MM-DD");
          }
        },

        {
          label: "是否为首次签",
          value: "oneTimes",
          formatter(row) {
            return row.oneTimes == 1 ? "是" : row.oneTimes == 2 ? "否" : "";
          }
        },
        {
          label: "合同签订次数",
          value: "times"
        },

        {
          label: "合同起始日期",
          width: 110,
          value: "startDate",
          formatter(row) {
            return date.dateFormat(row.startDate, "YYYY-MM-DD");
          }
        },
        {
          label: "合同到期日期",
          value: "endDate",
          formatter(row) {
            return date.dateFormat(row.endDate, "YYYY-MM-DD");
          }
        },
        {
          label: "合同类型",
          value: "contractType",
          formatter(row) {
            return row.contractType == '1'
              ? "固定期限"
              : row.contractType == '2'
                ? "无固定期限"
                : row.contractType == '3'
                  ? "实习期合同"
                  : "";
          }
        },
        {
          label: "合同年限",
          value: "contractLimit",
          width: 110,
        },

        {
          value: "createdAt",
          label: "创建时间",
          width: 180,
          formatter(row) {
            return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "操作",
          width: 200,
          operType: "button",
          operations: [
            {
              label: "编辑",
              type: "primary",
              func: this.handleEdit,
            }
            // {
            //   label: "删除",
            //   type: "danger",
            //   func: this.handleDelete,

            // }
          ]
        }

      ],

      searchList: [
        {
          label: "员工姓名",
          labelWidth: "80px",
          type: "input",
          prop: "userName"
        },
        {
          label: "合同名称",
          labelWidth: "80px",
          type: "input",
          prop: "contractName"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "新增",
              icon: "el-icon-plus",
              func: this.handleAdd
            }
          ]
        }
      ]
    };

  },
  watch: {},
  async mounted() {
    this.handleSearch();
    await this.initOpts();
  },
  methods: {
    handleAdd() {
      this.drawTitle = "新增合同";
      this.$refs.drawForm.open();
      this.initForm = {isRenewal: 1,oneTimes:1};
      this.$refs.drawForm.initforms(this.initForm);
    },


    getUsersData() {
      getUsers({ name: "" }).then(res => {
        if (res.code == 200) {
          this.data = res.data;
        }
      });
    },



    handleRemove(file) {
      for (let i = 0; i < this.form.fileList.length; i++) {
        if (file.id == this.form.fileList[i].id) {
          this.form.fileList.splice(i, 1);
          break;
        }
      }
    },


    uploadFileData(file) {
      if (!this.form.fileList) {
        this.form.fileList = [];
      }

      if (file.size > 2 * 1024 * 1024) {
        this.$message("文件过大，请上传小于2MB的文件〜");
        return false;
      }
      var formdata = new FormData();
      formdata.append("files", file.raw);
      //importDevice：请求接口 formdata：传递参数
      uploadFile(formdata).then(res => {
        if (res.code == 200) {
          for (let i = 0; i < res.data.length; i++) {
            res.data[i].name = res.data[i].fileName;
          }
          this.form.fileList = this.form.fileList.concat(res.data);
          this.$message.success("上传成功");
        }
      });
    },




    handleEdit(row) {
      this.drawTitle = "编辑合同";
      this.$refs.drawForm.open();
      this.$refs.drawForm.showLoading();
      getContractById({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            this.$refs.drawForm.initforms(res.data);
            this.$nextTick(function () {
              this.initForm = Object.assign({}, this.$refs.drawForm.form);
            });
          }
          this.$refs.drawForm.hideLoading();
        })
        .catch(err => {
          console.log(err);
          this.$refs.drawForm.hideLoading();
        });
    },




    handleConfirm(obj) {
      this.$refs.drawForm.showLoading();
      const params = Object.assign({}, obj);
      if (obj.id) {
        updateContract(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("更新成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      } else {
        addContract(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("保存成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      }
    },


    initOpts() {
      getUserName().then(res => {
        if (res.code == 200) {
          for (let i = 0; i < this.drawForms.length; i++) {
            if (this.drawForms[i].dataOrigin == "userName") {
              this.drawForms[i].opts = res.data;
              break;
            }
          }
        }
      });
    },


    close() {
      this.initForm = {};
      this.$refs.drawForm.close();
    },

    handleFormSearch(form) {
      this.pageIndex = 1;
      this.handleSearch(form);
    },

    tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

    handleSearch(form) {
      const params = Object.assign(this.$refs.form.form, form);
      params.page = this.pageIndex;
      params.pageSize = this.pageSize;
      this.tableLoading = true;
      queryPage(params).then(res => {
        if (res.code == 200) {
          this.total = res.data.total;
          this.dataList = res.data.list;
        }
        this.tableLoading = false;
      });
    }
  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>
<style>
.select-user-name {
  max-width: 700px !important;
}
</style>
