<template>
  <el-form
    ref="form"
    inline
    :model="form"
    :label-position="labelPosition"
    :label-width="labelWidth"
    :size="size"
    class="el-form-self direct-search"
    :style="formStyle"
    @submit.native.prevent
  >
    <template v-for="(item, index) in forms">
      <el-form-item
        v-if="!item.hidden"
        v-show="!item.isNotShow"
        :key="index"
        :label="`${item.label}`"
        :class="item.class"
        :prop="item.prop"
        :rules="item.rules"
        :label-width="item.labelWidth"
        :style="item.style"
      >
        <!-- <span slot="label" style="color:red;" v-if="item.labelSpecial&&!item.color">
                    {{item.labelSpecial}}
                    <span style="color:#606266;">{{item.label}}</span>
                </span>
        <span slot="label" v-if="item.labelSpecial&&item.color">{{item.labelSpecial}}</span>-->
        <span v-if="!item.label" slot="label" />
        <!-- 输入框 -->
        <el-input
          v-if="item.type === 'input'"
          :placeholder="
            item.placeholder ? item.placeholder : `请输入${item.label}`
          "
          v-model.trim="form[item.prop]"
          :disabled="item.disabled"
          :clearable="item.clearable || true"
          @clear="handleSearch"
          :maxlength="item.maxlength"
          :style="itemStyle || item.style"
          @keyup.enter.native="handleSearch"
        >
          <template v-if="item.slot" :slot="item.slot.slot">
            <el-button
              v-if="item.slot.type === 'button'"
              :icon="item.slot.icon"
              @click="handleSearch"
            />
          </template>
        </el-input>

        <!-- 下拉多选框 -->

        <el-tree-select
          v-else-if="item.type === 'treeSelect'"
          v-model="form[item.prop]"
          :selectParams="item.selectParams"
          @node-click="
            treeSelect(item.treeParams, item.prop, item.selectParams)
          "
          @select-clear="
            treeSelect(item.treeParams, item.prop, item.selectParams)
          "
          @check="treeSelect(item.treeParams, item.prop, item.selectParams)"
          :treeParams="item.treeParams"
          :treeRenderFun="renderFun"
          ref="treeSelect"
        />

        <!-- 模糊查询输入 -->
        <!-- <el-autocomplete :popper-class="item.popperClass" v-else-if="item.type === 'autocomplete'" v-model="form[item.prop]" :fetch-suggestions="item.func" :placeholder="item.placeholder" @select="handleSearch" clearable>
                    <template slot-scope="{ item }">
                        <div class="name">{{ item.value }}</div>
                        <span class="description">{{ item.description }}</span>
                    </template>
        </el-autocomplete>-->
        <!-- 模糊查询选择 -->
        <!-- <el-select v-else-if="item.type === 'remote'" v-model="form[item.prop]" filterable remote reserve-keyword :placeholder="item.placeholder" :remote-method="handleSearch">
                    <el-option v-for="(opt,optIndex) in item.opts" :key="optIndex" :label="opt.label" :value="item.isSelect?opt.selectValue:opt.value"></el-option>
        </el-select>-->

        <!-- 日期区间 -->
        <el-date-picker
          id="date-range"
          v-else-if="item.type === 'daterange'"
          v-model="form[item.prop]"
          :clearable="item.clearable || true"
          :type="item.dateType || 'daterange'"
          align="right"
          @change="handleSearch"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :picker-options="item.options"
          :format="item.format ? item.format : 'yyyy-MM-dd'"
          :value-format="item.valueFormat ? item.valueFormat : 'yyyy-MM-dd'"
        />

        <!-- 日期 -->
        <el-date-picker
          v-else-if="item.type === 'date'"
          v-model="form[item.prop]"
          :class="item.class ? item.class : 'el-input--small-8'"
          :type="item.dateType ? item.dateType : 'date'"
          :placeholder="item.placeholder"
          :clearable="!item.clearable ? item.clearable : true"
          :picker-options="item.options"
          :format="item.format"
          range-separator="-"
          :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
          :style="itemStyle || item.style"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />

        <el-time-picker
          v-else-if="item.type === 'time'"
          :class="item.class ? item.class : ''"
          :popper-class="item.popperClass ? item.popperClass : 'time-spinner'"
          is-range
          @change="handleSearch"
          value-format="HH:mm"
          format="HH:mm"
          :defalutValue="null"
          v-model="form[item.prop]"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
        ></el-time-picker>

        <template v-else-if="item.type === 'timeRange'">
          <div class="mft-time-range">
            <el-time-picker
              value-format="HH:mm"
              format="HH:mm"
              :defalutValue="null"
              placeholder="起始时间"
              v-model="form[item.prop1]"
            ></el-time-picker>
            <span class="range-separator">~</span>
            <el-time-picker
              value-format="HH:mm"
              format="HH:mm"
              placeholder="结束时间"
              :defalutValue="null"
              v-model="form[item.prop2]"
              :picker-options="{
                selectableRange:
                  (form[item.prop1] ? form[item.prop1] + ':00' : '00:00:00') +
                  ' - 23:59:00'
              }"
            ></el-time-picker>
          </div>
        </template>

        <!-- 下拉框 -->
        <el-select
          v-else-if="item.type === 'select' && !item.isHidden"
          v-model="form[item.prop]"
          clearable
          :placeholder="item.placeholder ? item.placeholder : '请选择'"
          :style="itemStyle || item.style"
          :filterable="item.filterable || false"
          :multiple="item.multiple"
          collapse-tags
          @change="item.func ? item.func($event) : {}"
        >
          <!-- @change="item.func?item.func($event):handleSearch()" -->
          <el-option
            v-for="(opt, optIndex) in item.opts"
            :key="optIndex"
            :label="item.isSelect ? opt[item.diyLabel] : opt.label"
            :value="item.isSelect ? opt[item.diyValue] : opt.value"
          />
        </el-select>

        <!-- 分页下拉框 -->
        <el-select
          v-else-if="item.type === 'pageSelect' && !item.isHidden"
          v-model="form[item.prop]"
          clearable
          class="page-select-list"
          :placeholder="item.placeholder ? item.placeholder : '请选择'"
          :style="itemStyle || item.style"
          filterable
          @visible-change="item.visibleChange"
          v-el-select-loadmore="item.loadmore"
          remote
          reserve-keyword
          :remote-method="item.queryList"
          :multiple="item.multiple"
          collapse-tags
          @change="item.func ? item.func($event) : handleSearch()"
        >
          <!-- @change="item.func?item.func($event):handleSearch()" -->
          <el-option
            v-for="(opt, optIndex) in item.opts"
            :key="optIndex"
            :label="item.isSelect ? opt[item.diyLabel] : opt.label"
            :value="item.isSelect ? opt[item.diyValue] : opt.value"
          />
        </el-select>

        <!-- 单选按钮 -->
        <el-radio-group
          v-else-if="item.type === 'radioList'"
          v-model="form[item.prop]"
          @keydown.native.capture.stop.prevent
          :placeholder="item.placeholder ? item.placeholder : '请选择'"
          :style="itemStyle"
        >
          <el-radio
            v-for="(opt, optIndex) in item.opts"
            :key="optIndex"
            :label="opt.label"
            :value="opt.value"
            >{{ opt.value }}</el-radio
          >
        </el-radio-group>

        <!-- 多选框 -->
        <el-checkbox-group
          v-else-if="item.type === 'checkbox'"
          v-model="form[item.prop]"
        >
          <el-checkbox
            v-for="(opt, optIndex) in item.opts"
            :key="optIndex"
            :label="opt.value"
            @change="item.func ? item.func($event, opt.value) : handleSearch()"
          >
            {{ opt.label }}
            <span
              v-if="opt.lineSuffix"
              style="
                display: inline-block;
                width: 5px;
                height: 16px;
                vertical-align: text-top;
              "
              :style="opt.style"
            />
          </el-checkbox>
        </el-checkbox-group>

        <!-- 级联 -->
        <el-cascader
          v-else-if="item.type === 'cascader'"
          v-model="form[item.prop]"
          :style="'width:' + item.width"
          :options="item.list"
          :popper-class="item.popperClass ? item.popperClass : ''"
          :props="item.props"
          :change-on-select="item.changeSelect ? item.changeSelect($event) : {}"
          :show-all-levels="item.showLevel"
        />

        <!-- 多选框 -->
        <el-checkbox-group
          v-else-if="item.type === 'checkboxList'"
          v-model="form[item.prop]"
        >
          <el-checkbox
            v-for="(opt, optIndex) in item.opts"
            :key="optIndex"
            :label="opt.value"
            @change="item.func ? item.func(form, opt.value) : {}"
            >{{ opt.label }}</el-checkbox
          >
        </el-checkbox-group>
        <!-- 按钮 -->
        <div v-else-if="item.type === 'btn'" :key="index">
          <el-checkbox
            v-if="item.showActive"
            @change="
              item.changeCheckbox ? item.changeCheckbox($event) : handleSearch()
            "
            v-model="form[item.activeProp]"
            >{{ item.activeLabel }}</el-checkbox
          >
          <el-button
            v-for="(btn, btnIndex) in item.list"
            :key="btnIndex"
            :size="btn.size ? btn.size : 'medium'"
            :icon="btn.icon"
            v-if="!btn.hidden"
            :style="btn.style"
            :type="btn.color"
            @click.native="btn.func ? btn.func(form) : {}"
            >{{ btn.value }}</el-button
          >
        </div>
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
export default {
  components: {},
  props: {
    size: { type: String, default: "small" },
    labelPosition: { type: String, default: "right" },
    labelWidth: { type: String, default: "0" },
    formStyle: { type: Object },
    forms: { type: Array }, // 表单组
    formDefaults: { type: Object },
    itemStyle: { type: Object }
  },

  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },

  data() {
    return {
      form: {}
    };
  },
  mounted() {
    this.initforms();
  },
  methods: {
    // 自定义render
    renderFun(h, { node, data, store }) {
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
        </span>
      );
    },

    // 表单赋值
    initforms(formEdit) {
      this.$nextTick(() => {
        const form = {};
        this.forms.forEach(item => {
          if (!item.prop || item.hidden) return false;
          if (
            item.type === "daterange" ||
            item.type === "checkboxList" ||
            item.type === "checkbox" ||
            item.type === "cascader" ||
            (item.type === "select" && item.multiple)
          ) {
            form[item.prop] = [];
          } else {
            form[item.prop] = "";
          }
        });

        if (formEdit) {
          this.form = Object.assign(form, formEdit);
        }

        if (this.formDefaults) {
          this.form = Object.assign(form, this.formDefaults);
        } else {
          this.form = Object.assign({}, form);
        }
        this.loading = false;
        if (this.$refs.form && this.$refs.form.clearValidate) {
          this.$refs.form.clearValidate();
        }
      });
    },

    treeSelect(obj, prop, params) {
      const ids = this.$refs.treeSelect[0].ids.concat([]);
      const arr = obj.data.concat([]);
      // for (let i = ids.length - 1; i >= 0; i--) {
      //   const temp = arr.filter((item) => item.id == ids[i]);
      //   if (temp.length > 0) {
      //     ids.splice(i, 1);
      //   }
      // }

      this.$refs.treeSelect[0].ids = ids;
      this.form[prop] = params.multiple ? ids : ids[0];
      // this.handleSearch();
    },

    // 查询
    handleSearch() {
      this.$emit("handleSearch", this.form);
    },

    // 给Form赋值
    initFields(obj) {
      this.form = obj;
      this.$nextTick(() => {
        if (this.$refs.form && this.$refs.form.clearValidate) {
          this.$refs.form.clearValidate();
        }
      });
    },

    // 给字段赋值
    initFieldsObj(obj) {
      for (const key in obj) {
        this.form[key] = obj[key];
      }
      this.$nextTick(() => {
        if (this.$refs.form && this.$refs.form.clearValidate) {
          this.$refs.form.clearValidate();
        }
      });
    }

    // changeHiddenForm(flag) {
    //     this.activeName = flag ? ['hiddenForm'] : []
    // }
  }
};
</script>

<style lang="scss" scoped>
.el-form-self {
  // margin-top: 16px;
  background-color: #fff;
  padding: 16px 16px 0 0;
  margin-bottom: 0 !important;
  .mft-time-range {
    width: 280px;
    .range-separator {
      display: inline-block;
      margin: 0 5px;
    }
    .el-date-editor.el-input {
      display: inline-block;
      width: 120px;
      .el-input--suffix .el-input__inner {
        padding: 0 !important;
      }
    }
  }
}

.el-form-item--small.el-form-item {
  margin-bottom: 16px;
}
</style>
<style>
.el-form-self .el-collapse-item__header {
  height: 0;
  line-height: 0;
}

.el-form-self .el-collapse {
  border: 0 !important;
}
.el-form-self .el-collapse-item__header {
  border: 0 !important;
}
.el-form-self .el-collapse-item__arrow {
  display: none;
}

.el-date-range-picker .is-plain {
  margin-left: 16px;
}
.input-with-select {
  display: none;
}

.el-tree-select .el-icon-close {
  display: none;
}
</style>
