import {
  fetch,
  post,
  put,
  formDataPost,
  deleteHttp
} from '@/utils/request'

// 查询列表
export const queryPage = data => {
  return fetch(`/attendance`, data)
}


// 更新
export const updateAttendance = data => {
  return put(`/attendance`, data)
}

// 详情
export const getAttendanceById = data => {
  return fetch(`/attendance/${data.id}`)
}

// 删除
// export const deleteUsers = data => {
//   return deleteHttp(`/users/${data.id}`)
// }



// 下载模板
export const getAttendanceTemplate = data => {
  return fetch(`/attendance/file/downExcel`, data, 'blob')
}


export const uploadInfo = data => {
  return post(`/attendance/file/upload`, data)
}


 // 查询所有员工
//  export const getUserName = data => {
//   return fetch(`/users/getUsers`,data)
// }