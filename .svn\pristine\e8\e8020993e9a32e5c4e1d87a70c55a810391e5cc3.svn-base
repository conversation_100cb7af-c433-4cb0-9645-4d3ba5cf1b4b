import {
  fetch,
  post,
  put,
  postToken,
  deleteHttp
} from '@/utils/request'


// 登录
export const login = data => {
  return fetch(`/users/login`, data)
}
// 用户列表
export const getUserInfo = data => {
  return post(`/users/page`, data)
}



// 用户列表
export const userList = data => {
  return fetch(`/users/page`, data)
}

// 新增用户
export const addUser = data => {
  return post(`/user`, data)
}

// 修改用户
export const updateUser = data => {
  return put(`/users`, data)
}

// 删除用户
export const deleteUser = data => {
  return deleteHttp(`/users/${data.id}`)
}

// 获取用户
export const getUser = data => {
  return fetch(`/users/${data.id}`)
}


