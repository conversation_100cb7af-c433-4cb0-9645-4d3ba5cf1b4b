// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}


// 文字按钮
.text-btn {
  font-size: 14px;
  margin-left: 16px;
  color: #3366FF !important;
  cursor: pointer;
  border: none;
  padding: 0;
  background-color: transparent;

  &:first-child {
    margin-left: 0;
  }

  &:disabled {
    color: #DCDEE0 !important;
    cursor: not-allowed;
  }
}

.text-btn-danger {
  font-size: 14px;
  margin-left: 16px;
  cursor: pointer;
  border: none;
  padding: 0;
  background-color: transparent;
  color: #FF2424;

  &:first-child {
    margin-left: 0;
  }

  &:disabled {
    color: #DCDEE0;
    cursor: not-allowed;
  }
}


.text-btn-warning {
  font-size: 14px;
  margin-left: 16px;
  cursor: pointer;
  border: none;
  padding: 0;
  background-color: transparent;
  color: #e6a23c;

  &:first-child {
    margin-left: 0;
  }

  &:disabled {
    color: #DCDEE0;
    cursor: not-allowed;
  }
}

.el-table--small{
  font-size: 13px;
}


// 输入框1：1-8个字符以内的单行文本框，固定宽高

.el-input--small-8,
.inputMore9 {
  width: 200px !important;
  background: #FFFFFF;
  font-size: 14px;
  border-radius: 2px;

  .el-input__count {
    font-size: 12px !important;
  }

  .el-input__inner {
    border-radius: 2px !important;
    // padding: 10px 12px;
    border: 1px solid #DCDEE0;
    font-size: 14px;
    height: 34px;
    color: #303133;
    line-height: 34px;

    &:hover {
      border: 1px solid #3366FF !important;
    }

    &:focus {
      caret-color: #3366FF !important;
      border: 1px solid #3366FF !important;
    }

    &:disabled {
      background: #F7F8FA;
      border: 1px solid #DCDEE0;
      color: #BFC1C8;
    }
  }


  .el-input__suffix {
    right: 12px;
    font-size: 12px !important;
    line-height: 34px;
  }
}

.more-input-260 {
  .el-input__inner {
    width: 250px;
  }
}

.more-input-230 {
  .el-input__inner {
    width: 230px;
  }
}

// 输入框1：9-25个字符以内的单行文本框，固定宽高

.inputMore9 {
  width: 420px !important;

  .el-input-number--small {
    width: 420px !important;
  }

  .el-textarea__inner {
    width: 420px !important;
  }

  // height: 34px;
}

// .el-textarea__inner {
//   padding: 7px 12px;
//   background: #FFFFFF;
//   width: 420px;
//   height: 34px !important;
//   min-height: 34px !important;
//   border-radius: 2px;
//   line-height: 20px;
//   font-size: 14px;
//   color: #303133;
//   border: 1px solid #DCDEE0;


//   &:hover {
//     border: 1px solid #3366FF !important;
//   }

//   &:focus {
//     caret-color: #3366FF !important;
//     border: 1px solid #3366FF !important;
//   }

//   &:disabled {
//     background: #F7F8FA;
//     border: 1px solid #DCDEE0;
//     color: #BFC1C8;
//   }
// }

// .el-textarea .el-input__count{
//   bottom: 1px !important;
//   right: 12px !important;
// }
.el-textarea__inner,
.el-range-input,
.el-input__inner,
.el-select__tags-text {
  color: #303133;
}


.inputMore25 {
  .el-textarea__inner {
    padding: 10px 12px;
    background: #FFFFFF;
    width: 420px;

    border-radius: 2px;
    line-height: 22px;
    font-size: 14px;
    color: #303133;
    border: 1px solid #DCDEE0;

    &:hover {
      border: 1px solid #3366FF !important;
    }

    &:focus {
      caret-color: #3366FF !important;
      border: 1px solid #3366FF !important;
    }

    &:disabled {
      background: #F7F8FA;
      border: 1px solid #DCDEE0;
      color: #BFC1C8;
    }
  }

  .el-input__count {
    bottom: 10px;
    color: #BFC1C8;
    line-height: 12px;
  }
}

.el-form-item__error {
  padding-top: 4px !important;
}

.el-form-item__label {
  font-weight: 400 !important;
}