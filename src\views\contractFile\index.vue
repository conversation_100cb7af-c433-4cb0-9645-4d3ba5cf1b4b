<template>
  <el-row class="page-container">
    <el-row class="page-main">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self 
           :columns="columns" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>

      <!-- <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true"
         :label-width="'90'" :title="drawTitle"></draw-form> -->
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import {selectPage,getById} from "./api";

export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
        total: 0,
        id: 0,
        drawTitle: "",
        dataList: [],
        columns: [
          {
            value: "fileName",
            label: "文件名称"
          },
          {
            value: "fileSize",
            label: "文件大小(KB)"

          },
          {
            value: "userName",
            label: "上传人"
          },
          {
            value: "createdAt",
            label: "上传时间",
            formatter(row) {
              return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          
         
          {
            label: "操作",
            fixed: "right",
            width: 180,
            operType: "button",
            operations: [
              {
                label: "下载文件",
                type: "primary",
                func: this.handleDownload,
              },
              // {
              //   label: "预览",
              //   type: "primary",
              //   func: this.handlePreView,
              // }
            ]
          }

        ],

      searchList: [
        {
          label: "文件名称",
          labelWidth: "80px",
          type: "input",
          prop: "fileName"
        },
        {
          label: "上传人",
          labelWidth: "100px",
          type: "input",
          prop: "userName"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
            
          ]
        }
      ]
    };

  },
  watch: {},
  mounted() {
    this.handleSearch();
  },
  methods: {


    // 下载文件
    handleDownload(row)  {
     getById({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            const fileUrl = row.fullPathOriginal;
            const link = document.createElement('a');
            link.href = fileUrl;
            link.target='_blank';
            link.download=row.fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
           
          }
          
        })
        .catch(err => {
          console.log(err);
      
        });

      // const fileUrl = 'https://bb-ck.oss-cn-hangzhou.aliyuncs.com/2024/03/05/71b955b824824624af30bf152a46db6bimport.xlsx';
      // const link = document.createElement('a');
      // link.href = fileUrl;
      // link.target='_blank';
      // link.download='71b955b824824624af30bf152a46db6bimport.xlsx';
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
  },




    close() {
        this.initForm = {};
        this.$refs.drawForm.close();
      },

      handleFormSearch(form) {
        this.pageIndex = 1;
        this.handleSearch(form);
      },

      tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

      handleSearch(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;
        this.tableLoading = true;
        selectPage(params).then(res => {
          if (res.code == 200) {
            this.total = res.data.total;
            this.dataList = res.data.list;
          }
          this.tableLoading = false;
        });
      }
  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>