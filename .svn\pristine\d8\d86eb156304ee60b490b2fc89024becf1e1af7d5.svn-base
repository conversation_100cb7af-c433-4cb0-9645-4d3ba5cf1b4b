<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
    <el-tabs>
      <el-tab-pane :label="'工资单列表'">


        <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>

      <el-row>
        <div>税后应发金额汇总：{{this.dataList.afterTaxSalarySum}} 元</div>
      </el-row>
     

      <el-col style="padding: 15px 0" >
        <el-table-self
           :columns="columns" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" show-overflow-tooltip/>
      </el-col>
    
     

       <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true" 
        :label-width="'90'" :title="drawTitle"></draw-form> 
      </el-tab-pane>


      <el-tab-pane :label="'工资单文件列表'">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList1"
          @handleSearch="handleFormSearch1" />
      </el-row>
      <el-col style="padding: 15px 0" >
        <el-table-self
           :columns="columns1" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList1"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" show-overflow-tooltip/>
      </el-col>

      <!-- <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true" 
        :label-width="'90'" :title="drawTitle"></draw-form> -->
      </el-tab-pane>




      <!-- <el-tab-pane
          label="1_工资单导入"
          name="second"
        
        >
          <span style="color:red">*</span>工资单导入
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              :on-change="uploadByJsqd"
              :file-list="fileList">
              <el-button type="plain" style="margin-left:10px">选择文件</el-button>
            </el-upload>
          </div>

          <div>
            <el-link
              type="primary"
              @click="getTemplateData"
              style="margin-top:10px;font-size: 16px;"
              >工资单模板下载</el-link>
          </div>
      </el-tab-pane>
 -->


      <!-- <el-tab-pane :label="'2_考勤附件'" class="">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList2"
          @handleSearch="handleFormSearch2" />
      </el-row>
      <el-col style="padding: 15px 0" >
        <el-table-self
           :columns="columns2" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList2"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" show-overflow-tooltip/>
      </el-col>

      <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true" 
        :label-width="'90'" :title="drawTitle"></draw-form>
      </el-tab-pane> -->



      <!-- <el-tab-pane
          label="2_考勤导入"
          name="second1"
        >
          <span style="color:red">*</span>考勤导入
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              :on-change="uploadByJsqd1"
              :file-list="fileList">
              <el-button type="plain" style="margin-left:10px">选择文件</el-button>
            </el-upload>
          </div>

          <div>
            <el-link
              type="primary"
              @click="getTemplateData1"
              style="margin-top:10px;font-size: 16px;"
              >考勤模板下载</el-link>
          </div>
        </el-tab-pane> -->


      </el-tabs>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import {
  findPage,
  getPayrollTemplate,
  uploadInfo,
  getById,
  exporPayroll,
  deletetPayroll,
  updateDeleted,
  queryPage,
  getDeptName} from "@/views/payroll/api";


export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      now: new Date(),
      tableLoading: false,
        total: 0,
        id: 0,
        drawTitle: "",
        drawForms: [],
        dataList: [],
        fileList: [],
        columns: [
          // {
          //   fixed: true,
          //   value: "orderNo",
          //   label: "序号"
           
          // },
          {
            fixed: true,
            value: "userName",
            label: "姓名",
          },
          {
            fixed: true,
            value: "years",
            label: "工资年-月"
          },
          {
            value: "deptName",
            label: "部门"
          },
          // {
          //   value: "droleName",
          //   label: "岗位"
          // },
          {
            fixed: true,
            width: 120,
            value: "type",
            label: "所属分公司",
            formatter(row) {
                return row.type == '总部' 
                ? "总部" 
                : row.type == '蚌埠' 
                ? "蚌埠" 
                : row.type == '合肥' 
                ? "合肥" 
                : row.type == '常州' 
                ? "常州" 
                : row.type == '无锡'
                ? "无锡"
                : "";
            }
          },
          {
            fixed: true,
            value: "isRegular",
            label: "员工状态",
          },
          {
            value: "levelCodePM",
            label: "职级"
          },
          {
            value: "salaryLevel",
            label: "薪档",
          },
          {
            value: "idNum",
            label: "身份证号码"
          },
          {
          value: "isRegular",
          label: "是否转正",
          // formatter(row) {
          //   return row.isRegular == 1 ? "是" : row.isRegular == 2 ? "否" : "";
          // }
        },
        {
            value: "entryTime",
            label: "入职日期",
            formatter(row) {
              return date.dateFormat(row.entryTime, "YYYY-MM-DD");
            }
          },
          {
          value: "disabled",
          label: "是否在职",
          formatter(row) {
            return row.disabled == 1 ? "离职" : row.disabled == 0 ? "在职" : "";
          }
        },
          {
            value: "attendanceDay",
            label: "应出勤天数"
          },
          {
            value: "actualDay",
            label: "实际出勤天数"
          },
          {
            value: "restDay",
            label: "调休天数"
          },
          {
            value: "sickDay",
            label: "病假天数"
          },
          // {
          //   value: "absenceDay",
          //   label: "病假天数"
          // },
          {
            value: "absenceDay",
            label: "缺勤天数"
          },
          {
            value: "notworkDay",
            label: "旷工天数"
          },
          {
            value: "sickRate",
            label: "病假系数"
          },
          {
            value: "sickAdd",
            label: "病假增补"
          },
          {
            value: "notworkRate",
            label: "旷工系数"
          },
          {
            value: "notworkCut",
            label: "旷工扣减"
          },
          {
            value: "levelSalary",
            label: "基础工资"
          },
          {
            value: "postSalary",
            label: "岗位工资"
          },
          {
            value: "actualSalary",
            label: "实发"
          },
          
          {
            value: "performanceBase",
            label: "绩效基数"
          },
          {
            value: "scoreResult",
            label: "评分结果（分）"
          },
          {
            value: "valuationPerformanceActual",
            label: "评价绩效实发"
          },
          {
            value: "addPercentage",
            label: "新增提成"
          },
          {
            value: "renewPercentage",
            label: "续费提成"
          },
          {
            value: "otherPercentage",
            label: "其他提成"
          },
          {
            value: "otherPercentageDesc",
            label: "其他提成说明"
          },
          {
            value: "actualPercentage",
            label: "提成实发"
          },
          {
            value: "telephoneSalary",
            label: "电话补贴"
          },
          {
            value: "trafficSalary",
            label: "交通补贴"
          },
          {
            value: "mealSalary",
            label: "餐补"
          },
          {
            value: "otherActual",
            label: "其他实发"
          },
          {
            value: "otherDesc",
            label: "其他项说明"
          },
          {
            value: "subsidyActual",
            label: "补贴实发"
          },
          {
            value: "fullSalary",
            label: "满勤奖"
          },
          {
            value: "workAge",
            label: "工龄工资"
          },
          {
            value: "certificateSubsidy",
            label: "证书补贴"
          },
          {
            value: "otherSalary",
            label: "其他奖金"
          },
          {
            value: "otherSalaryDesc",
            label: "其他奖金说明"
          },
          {
            value: "incentiveBonusSum",
            label: "激励奖金合计"
          },
          {
            value: "otherCut",
            label: "其他扣减（-）"
          },
          {
            value: "otherCutDesc",
            label: "其他扣减说明"
          },          
          {
            value: "socialSecurityCut",
            label: "社保扣减（-）"
          },
          {
            value: "personalTaxCut",
            label: "个税扣减(-)"
          },
          {
            value: "bankNoName",
            label: "收款人姓名"
          },

          {
            value: "bankNo",
            label: "工资卡号"
          },

          {
            value: "bank",
            label: "开户行"
          },
          {
            value: "allPreTaxSum",
            label: "所有应发税前总额（+）"
          },

          {
            value: "socialTaxCut",
            label: "社保、个税扣减合计（-）"
          },

          {
            value: "afterTaxSalary",
            label: "税后应发金额"
          },
          {
            value: "createdAt",
            label: "创建时间",
            width: 180,
            formatter(row) {
              return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          {
            label: "操作",
            fixed: "right",
            width: 180,
            operType: "button",
            operations: [
              {
                label: "编辑",
                type: "warning",
                func: this.handleEdit,
              },
              {
                label: "删除",
                type: "danger",
                func: this.handleDelete,
              },
              // {
              //   label: "文件下载",
              //   type: "primary",
              //   isHidden(row) {
              //     return (
              //       row.attachmentId == null || row.attachmentId == ''
              //     );
              //  },
              //   func: this.handleDownload,
                
              // }
            ]
          }

        ],

        //工资单文件列表
        dataList1: [],
        tableLoading: true,
        columns1: [
          {
            value: "fileName",
            label: "文件名称"
          },
          {
            value: "fileSize",
            label: "文件大小(KB)"
          },
          {
            value: "userName",
            label: "上传人"
          },
          {
            value: "createdAt",
            label: "上传时间",
            formatter(row) {
              return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          {
            label: "操作",
            fixed: "right",
            width: 180,
            operType: "button",
            operations: [
              {
                label: "下载文件",
                type: "primary",
                func: this.handleDownload,
              },
              {
                label: "删除",
                type: "danger",
                func: this.handleFileDelete,
              }
            ]
          }

        ],
        //----------工资单文件列表 end------------------

        
        //----------考勤文件列表 start------------------
        // dataList2: [],
        // columns2: [
        //   {
        //     value: "fileName",
        //     label: "文件名称"
        //   },
        //   {
        //     value: "fileSize",
        //     label: "文件大小(KB)"
        //   },
        //   {
        //     value: "userName",
        //     label: "上传人"
        //   },
        //   {
        //     value: "createdAt",
        //     label: "上传时间",
        //     formatter(row) {
        //       return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
        //     }
        //   },
        //   {
        //     label: "操作",
        //     fixed: "right",
        //     width: 180,
        //     operType: "button",
        //     operations: [
        //       {
        //         label: "下载文件",
        //         type: "primary",
        //         func: this.handleDownload,
        //       }
        //     ]
        //   }

        // ],
         //----------考勤文件列表 end ------------------
         

        //----------工资单文件列表 start------------------
        searchList1: [
        {
          label: "文件名称",
          labelWidth: "80px",
          type: "input",
          prop: "fileName"
        },
        {
          label: "上传人",
          labelWidth: "100px",
          type: "input",
          prop: "userName"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch1,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            }
          ]
        }
      ],
      //----------工资单文件列表 end------------------


      
        //----------考勤文件列表 start------------------
      //   searchList2: [
      //   {
      //     label: "文件名称",
      //     labelWidth: "80px",
      //     type: "input",
      //     prop: "fileName"
      //   },
      //   {
      //     label: "上传人",
      //     labelWidth: "100px",
      //     type: "input",
      //     prop: "userName"
      //   },
      //   {
      //     label: "",
      //     type: "btn",
      //     list: [
      //       {
      //         func: this.handleFormSearch2,
      //         style: "margin-left: 16px;",
      //         value: "查询",
      //         color: "primary"
      //       }
      //     ]
      //   }
      // ],
      //----------考勤文件列表 end------------------


      searchList: [
      {
          label: "工资单创建时间",
          labelWidth: "120px",
          type: "date",
          valueFormat: "yyyy-MM",
          dateType: "month",
          prop: "createdAt"
        },
      {
          type: "select",
          label: "所属公司",
          labelWidth: "80px",
          prop: "type",
          opts: [
            {
              label: "总部",
              value: '总部'
            },
            {
              label: "蚌埠",
              value: '蚌埠'
            },
            {
              label: "合肥",
              value: '合肥'
            },
            {
              label: "常州",
              value: '常州'
            },
            {
              label: "无锡",
              value: '无锡'
            }
          ]
        },
        {
          label: "姓名",
          labelWidth: "80px",
          type: "input",
          prop: "userName"
        },
        {
          label: "工资年-月",
          labelWidth: "80px",
          type: "date",
          valueFormat: "yyyy-MM",
          dateType: "month",
          placeholder: "请选择年月",
          prop: "years"
        },
        {
          label: "部门",
          labelWidth: "60px",
          type: "select",
          diyLabel: "name",
          diyValue: "id",
          isSelect: true,
          prop: "groupId",
          dataOrigin: "deptName1",
          opts: [],
       
        },
        {
          type: "select",
          label: "员工状态",
          labelWidth: "100px",
          prop: "isRegular",
          opts: [
            {
              label: "已转正",
              value: '已转正'
            },
            {
              label: "试用期",
              value: '试用期'
            },
            {
              label: "自离",
              value: '自离'
            },
            {
              label: "调岗",
              value: '调岗'
            },
            {
              label: "淘汰",
              value: '淘汰'
            },
            {
              label: "试岗期",
              value: '试岗期'
            }
          ]
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
           
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "导出",
              icon: "el-icon-download",
              func: this.handleExport
            }
          ]
        },
      ],
    };

  },
  watch: {},
  mounted() {
    this.initOpts();
    this.handleSearch();
  },
  methods: {

    initOpts() {
    getDeptName().then(res => {
        if (res.code == 200) {
          for (let i = 0; i < this.searchList.length; i++) {
            if (this.searchList[i].dataOrigin == "deptName1") {
              this.searchList[i].opts = res.data;
              break;
            }
          }
        }
      });
    },



     // 下载文件
     handleDownload(row)  {
     getById({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            const fileUrl = res.data.fullPathOriginal;
            window.location.href = fileUrl;
            // const link = document.createElement('a');
            // link.href = fileUrl;
            // link.target='_blank';
            // link.download=res.data.fileName;
            // document.body.appendChild(link);
            // link.click();
            // document.body.removeChild(link);
           
          }
          
        })
        .catch(err => {
          console.log(err);
      
        });
  },


  handleDelete(row) {
      this.$confirm(`确定要删除该记录吗?`, "提示", {
        type: "warning"
      })
        .then(() => {
          this.tableLoading = true;
          deletetPayroll({ id: row.id })
            .then(res => {
              if (res.code == 200) {
                this.$message.success("删除成功");
                this.handleSearch();
              } else {
                this.tableLoading = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },



  //编辑
  handleEdit(row) {
    // 按照创建时间来，非当月的创建工资单不可再编辑
    const year = this.now.getFullYear();
    const months = this.now.getMonth()+1;
    const month = months < 10 ?'0'+months:months;
    if(row.year != year){
      if(row.month != month){
        this.$message.warning("非当月创建工资单，不可编辑");
        return;
      }
    }else{
      if(row.month != month){
        this.$message.warning("非当月创建工资单，不可编辑");
        return;
      }
    }

      this.$router.push({
        name: "addSalary",
        query: { id: row.id }
      });
    },


    
  
    close() {
        this.initForm = {};
        this.$refs.drawForm.close();
      },

      handleFormSearch(form) {
        this.pageIndex = 1;
        this.handleSearch(form);
      },

      tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

    
      handleSearch(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;
        this.tableLoading = true;
        findPage(params).then(res => {
              if (res.code == 200) {
              this.total = res.data.total;
              this.dataList = res.data.list;
              if(res.data.list.length > 0){
              this.dataList.afterTaxSalarySum  = this.dataList[this.dataList.length-1].afterTaxSalarySum;
            }
          }
          this.tableLoading = false;
        });
        
      },



      
      
      //--------------- 工资单文件查询  start -------------------
      handleFormSearch1(form) {
        this.pageIndex = 1;
        this.handleSearch1(form);
      },

      handleSearch1(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;
        this.tableLoading = true;
        queryPage(params).then(res => {
          if (res.code == 200) {
            this.total = res.data.total;
            this.dataList1 = res.data.list;
          }
          this.tableLoading = false;
        });
      },

      handleFileDelete(row) {
        this.$confirm(`确定要删除该记录吗?`, "提示", {
        type: "warning"
        })
        .then(() => {
          this.tableLoading = true;
          updateDeleted({ id: row.id,deleted: row.deleted='1' })
            .then(res => {
              if (res.code == 200) {
                this.$message.success("删除成功");
                this.handleSearch1();
              } else {
                this.tableLoading = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },

      //--------------- 工资单文件查询  end -------------------


       //--------------- 考勤文件查询  start -------------------
      //  handleFormSearch2(form) {
      //   this.pageIndex = 1;
      //   this.handleSearch2(form);
      // },

      // handleSearch2(form) {
      //   const params = Object.assign(this.$refs.form.form, form);
      //   params.page = this.pageIndex;
      //   params.pageSize = this.pageSize;
      //   this.tableLoading = true;
      //   selPage(params).then(res => {
      //     if (res.code == 200) {
      //       this.total = res.data.total;
      //       this.dataList = res.data.list;
      //     }
      //     this.tableLoading = false;
      //   });
      // },


      getTemplateData1() {
      getAttendanceTemplate()
        .then(res => {
          const blob = new Blob([res]);
          const elink = document.createElement("a");
          elink.download = "import_kq" + ".xlsx";
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        })
        .catch(error => {
          console.log(error);
        });
    },

      //--------------- 考勤文件查询  end -------------------



      getTemplateData() {
        getPayrollTemplate()
        .then(res => {
          const blob = new Blob([res]);
          const elink = document.createElement("a");
          elink.download = "import_payroll" + ".xlsx";
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        })
        .catch(error => {
          console.log(error);
        });
    },


      
     //文件发生改变就会触发的事件
     uploadByJsqd(file) {
      //判断是否符合beforeAvatarUpload方法中的条件
      if (this.beforeAvatarUpload(file)) {
        this.fileList.name = file.name;
        this.fileList.url = "";
        var formdata = new FormData();
        formdata.append("file", file.raw);
        formdata.append("type", '1');
        //importDevice：请求接口 formdata：传递参数
        uploadInfo(formdata).then(res => {
          if (res.code == 200) {
            this.$message.success("上传成功");
            this.importFile = "";
            this.fileList = [];
          }
        });
      }
    },

       //文件校验方法
       beforeAvatarUpload(file) {
      // 通过split方法和fileArr方法获取到文件的后缀名
      let fileArr = file.name.split(".");
      let suffix = fileArr[fileArr.length - 1];
      //只能导入.xls和.xlsx文件
      if (!/(xls|xlsx)/i.test(suffix)) {
        this.$message("文件格式不正确");
        return false;
      }
      //不能导入大小超过2Mb的文件
      if (file.size > 11 * 1024 * 1024) {
        this.$message("文件过大，请上传小于10MB的文件〜");
        return false;
      }
      return true;
    },



    //---------------考勤导入 start----------------------
    // 文件发生改变就会触发的事件
    // uploadByJsqd1(file) {
    //   //判断是否符合beforeAvatarUpload方法中的条件
    //   if (this.beforeAvatarUpload1(file)) {
    //     this.fileList.name = file.name;
    //     this.fileList.url = "";
    //     var formdata = new FormData();
    //     formdata.append("file", file.raw);
    //     formdata.append("type", '2');
    //     //importDevice：请求接口 formdata：传递参数
    //     uploadInfo(formdata).then(res => {
    //       if (res.code == 200) {
    //         this.$message.success("上传成功");
    //         this.importFile = "";
    //         this.fileList = [];
    //       }
    //     });
    //   }
    // },


    //文件校验方法
    beforeAvatarUpload1(file) {
      // 通过split方法和fileArr方法获取到文件的后缀名
      let fileArr = file.name.split(".");
      let suffix = fileArr[fileArr.length - 1];
      //只能导入.xls和.xlsx文件
      if (!/(xls|xlsx)/i.test(suffix)) {
        this.$message("文件格式不正确");
        return false;
      }
      //不能导入大小超过2Mb的文件
      if (file.size > 11 * 1024 * 1024) {
        this.$message("文件过大，请上传小于10MB的文件〜");
        return false;
      }
      return true;
    },

     //---------------考勤导入 end----------------------


    handleExport() {
      let params = Object.assign({}, this.$refs.form.form);
      exporPayroll(params)
        .then(res => {
          const blob = new Blob([res]);
          const elink = document.createElement("a");
          elink.download = "工资表_" + date.dateFormat() + ".xlsx";
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        })
        .catch(error => {
          console.log(error);
        });
    },


  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>
<style>
.el-table-ck th {
    background-color: #f7f8fa !important;
    -webkit-box-shadow: 0px 1px 0px 0px #ebedf0;
    box-shadow: 0px 1px 0px 0px #ebedf0;
    color: #303133;
    padding: 0 0;
    height: 100px !important;
}

</style>