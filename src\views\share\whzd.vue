<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
    <el-tabs>
      <el-tab-pane :label="$route.query.id ? '修改文化制度' : '文化制度列表'">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self 
           :columns="columns" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>
     </el-tab-pane>

      <el-tab-pane label="文化制度上传" name="second">
        <span style="color:red">*</span>文件请上传ppt、pptx、pdf格式
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              :on-change="uploadByJsqd"
              :file-list="fileList">
              <el-button type="plain" style="margin-left:10px">选择文件</el-button>
            </el-upload>
          </div>
        </el-tab-pane>

      </el-tabs>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import {getById,qryPage,uploadInfo,updateDeleted } from "./api";

export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
        total: 0,
        id: 0,
        drawTitle: "",
        fileList: [],
        dataList: [],
        columns: [
          {
            value: "fileName",
            label: "文件名称"
          },
          {
            value: "fileSize",
            label: "文件大小(KB)"

          },
          // {
          //   value: "userName",
          //   label: "上传者"
          // },
          {
            value: "createdAt",
            label: "上传时间",
            formatter(row) {
              return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          
         
          {
            label: "操作",
            fixed: "right",
            width: 200,
            operType: "button",
            operations: [
              {
                label: "文件预览",
                type: "primary",
                func: this.handlePreView,
              },
               {
                label: "删除文件",
                type: "danger",
                func: this.handleDelete,
              }
            ]
          }
        ],

      searchList: [
        {
          label: "文件名称",
          labelWidth: "80px",
          type: "input",
          prop: "fileName"
        },
        // {
        //   label: "上传者",
        //   labelWidth: "100px",
        //   type: "input",
        //   prop: "userName"
        // },
        {
          type: "date",
          dateType: "daterange",
          valueFormat: "yyyy-MM-dd",
          labelWidth: "80px",
          class: "date-range",
          seniorSelect: true,
          label: "上传日期",
          prop: "createdAt"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
            
          ]
        }
      ]
    };

  },
  watch: {},
  mounted() {
    this.handleSearch();
  },
  methods: {

    handleDelete(row) {
      const data = {
        id: row.id,
        deleted:row.deleted == 0 ? 1: null,
      };
      updateDeleted(data).then((res) => {
        if (res.code == 200) {
          this.$message.success("删除成功！");
          this.handleSearch();
        }
      });
    },





      //文件校验方法
      beforeAvatarUpload(file) {
      // 通过split方法和fileArr方法获取到文件的后缀名
      let fileArr = file.name.split(".");
      let suffix = fileArr[fileArr.length - 1];
      //只能导入.ppt和.pptx文件
      if (!/(ppt|pptx|pdf|mp4)/i.test(suffix)) {
        this.$message("文件格式不正确");
        return false;
      }
      //不能导入大小超过100Mb的文件
      if (file.size > 100 * 1024 * 1024) {
        this.$message("文件过大，请上传小于100MB的文件〜");
        return false;
      }
      return true;
    },


     //文件发生改变就会触发的事件
     uploadByJsqd(file) {
      //判断是否符合beforeAvatarUpload方法中的条件
      if (this.beforeAvatarUpload(file)) {
        this.fileList.name = file.name;
        this.fileList.url = "";
        var formdata = new FormData();
        formdata.append("file", file.raw);
        formdata.append("type",'4');
        //importDevice：请求接口 formdata：传递参数
        uploadInfo(formdata).then(res => {
          if (res.code == 200) {
            this.$message.success("上传成功");
            this.importFile = "";
            this.fileList = [];
            this.tableLoading = true;
          }
        });
      }
    },




  
  handlePreView(row)  {
     getById({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            const url = row.fullPathOriginal;
            window.open('http://121.41.20.233:8012/onlinePreview?url='+encodeURIComponent(url));
          }
          
        })
        .catch(err => {
          console.log(err);
      
        });
      },



    close() {
        this.initForm = {};
        this.$refs.drawForm.close();
      },

      handleFormSearch(form) {
        this.pageIndex = 1;
        this.handleSearch(form);
      },

      tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

      handleSearch(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;

        if (params.createdAt && params.createdAt.length > 0) {
        params.startTime = params.createdAt[0];
        params.endTime = params.createdAt[1];
        // delete params.createdAt;
      }else{
        params.startTime = null;
        params.endTime = null;
      }

        this.tableLoading = true;
        qryPage(params).then(res => {
          if (res.code == 200) {
            this.total = res.data.total;
            this.dataList = res.data.list;
          }
          this.tableLoading = false;
        });
      }
  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>