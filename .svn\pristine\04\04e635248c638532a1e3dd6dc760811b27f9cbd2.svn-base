import {
  asyncRoutes,
  constantRoutes
} from '@/router'
/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = {
      ...route
    }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: constantRoutes,
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.routes = routes
  }
}

const actions = {
  generateRoutes({
    commit
  }, obj) {
    return new Promise(resolve => {
      let role = obj.positionType
      // constantRoutes.forEach(item => {
      //   if (item.children) {
      //     item.children.forEach(currentItem => {
      //       if (currentItem.meta) {
      //         if (currentItem.meta.roles.indexOf(role) == -1) {
      //           currentItem.hidden = true
      //         }else{
      //           currentItem.hidden = false
      //         }
      //       }
      //     });
      //   }
      // });
      // if (role == 'admin') {
      commit('SET_ROUTES', constantRoutes)
      // } 
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
