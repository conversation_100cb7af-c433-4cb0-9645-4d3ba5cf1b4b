<template>
  <el-row class="page-container">
    <el-row class="page-main" v-loading="loading">
      <el-tabs>
       <el-tab-pane :label="$route.query.id ? '修改合同' : '新建合同'">
      <el-form ref="form" :model="form" label-width="232px" :inline="true">
        <el-divider content-position="left">合同信息</el-divider>
        <template v-for="(item, index) in formList">
          <el-form-item
            :key="index"
            :style="item.style"
            :class="item.itemClass ? item.itemClass : ''"
            :label="item.label ? item.label + (item.hiddenColon ? '' : '：') : ''"
            :prop="item.prop"
            :rules="item.rules"
          >
            <template v-if="item.type === 'input'">
              <!-- 输入框 -->
              <el-input
                  :class="item.class ? item.class : 'el-input--small-8'"
                  :id="item.prop ? item.prop : ''"
                  :value="form[item.prop]"
                  :minlength="item.minlength"
                  :maxlength="item.maxlength"
                  :readonly="item.readonly"
                  :disabled="item.disabled"
                  :placeholder="item.placeholder"
                  autocomplete="off"
                  v-model.trim="form[item.prop]"
                  @focus="item.focusFunc ? item.focusFunc($event) : {}"
                  @change="item.func ? item.func($event) : {}"
                  @blur="item.blurFunc ? item.blurFunc($event) : {}"
                 
                >
                  <span v-if="item.count" slot="suffix">{{
                    (form[item.prop] ? form[item.prop].length : 0) +
                      "/" +
                      item.maxlength
                  }}</span>
                  <span
                    v-if="item.unit"
                    :slot="item.slot ? item.slot : 'append'"
                    >{{ item.unit }}</span
                  >
                </el-input>

              <div
                v-if="item.note"
                :class="[
                  item.class ? item.class : 'el-input--small-8',
                  'ele-note'
                ]"
                v-html="item.note"
              ></div>
            </template>

            <!-- <template v-if="item.type === 'workId'">
              {{ form.companyName ? form.companyName : "未选择" }}
              <el-button @click="selectCompany">选择</el-button>
            </template> -->
            <template v-if="item.type === 'textarea'">
              <el-input
                :class="item.class"
                :style="item.itemStyle"
                @change="item.func ? item.func($event) : {}"
                :resize="item.resize ? item.resize : 'none'"
                v-model.trim="form[item.prop]"
                type="textarea"
                :disabled="item.disabled"
                :placeholder="item.placeholder"
                :rows="item.rows"
                :autosize="item.autosize"
                :minlength="item.minlength"
                :maxlength="item.maxlength"
                :show-word-limit="item.showLimit"
              />

              <div
                v-if="item.note"
                :class="[
                  item.class ? item.class : 'el-input--small-8',
                  'ele-note'
                ]"
                v-html="item.note"
              ></div>
            </template>

            <!-- 日期 -->
            <el-date-picker
              v-else-if="item.type === 'date'"
              v-model="form[item.prop]"
              :type="item.dateType ? item.dateType : 'date'"
              :picker-options="item.options"
              :format="item.format"
              :value-format="item.valueFormat"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />

            <!-- 下拉框 -->
             <template v-else-if="item.type === 'select'">
              <el-select
                v-model="form[item.prop]"
                :class="item.class ? item.class : 'el-input--small-8'"
                :clearable="item.clearable || true"
                :multiple="item.multiple || false"
                :filterable="item.filterable || true"
                :disabled="item.disabled"
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                @change="item.func ? item.func($event) : {}"
                @remove-tag="item.removeFunc ? item.removeFunc($event) : {}"
              >
                <el-option
                  v-for="(opt, optIndex) in item.opts"
                  :key="optIndex"
                  :disabled="opt.disabled"
                  :label="item.isSelect ? opt[item.diyLabel] : opt.label"
                  :value="item.isSelect ? opt[item.diyValue] : opt.value"
                />
              </el-select>
              <div
                v-if="item.note"
                :class="[
                  item.class ? item.class : 'el-input--small-8',
                  'ele-note'
                ]"
                v-html="item.note"
              ></div>
            </template>

          </el-form-item>
        </template>

         <el-divider content-position="left">合同文件上传</el-divider>
          <el-row>
            <el-form-item label="">
              <div class="import-container">
                <el-upload
                  action=""
                  :auto-upload="false"
                  :multiple="false"
                  class="upload-demo"
                  :limit="10"
                  :on-change="uploadFileData"
                  :file-list="form.fileList"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                    合同附件建议上传pdf格式，且单个文件上传大小不超过50M
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-row> 

        <el-row class="btn-operate">
          <el-form-item>
            <el-button type="primary" @click="save">保存</el-button>
            <el-button @click="goback">返回</el-button>
          </el-form-item>
        </el-row>
      </el-form>
       </el-tab-pane>

    </el-tabs>
    </el-row>
  </el-row>
</template>

<script>
import { addContract,updateContract, getContractById,uploadInfo,getUserName,uploadFile} from "./api";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters(["userInfo"])
  },
  data() {
    return {
      dialogTableVisible: false,
      gridData: [],

      importFile: "",
      fileList: [], //上传的文件列表
      loading: false,
      activeName: "first",
      form: {
        cname: "",
        uname: "",
        fileList: [],
      },
      formList: [
        {
          label: "员工姓名",
          type: "select",
          dataOrigin: "name",
          diyLabel: "name",
          diyValue: "id",
          isSelect: true,
          seniorSelect: true,
          prop: "userId",
          opts: [],
          rules: [
            {
              required: true,
              message: "请填写员工姓名",
              trigger: "blur"
            }
          ]
        },
        {
          label: "合同编号",
          placeholder: "请输入合同编号",
          type: "input",
          prop: "contractNo",
          rules: [
            {
              required: true,
              message: "请输入合同编号",
              trigger: "blur"
            }
          ]
        },
        {
          label: "合同主体",
          placeholder: "请输入合同主体",
          type: "input",
          prop: "contractName",
          rules: [
            {
              required: true,
              message: "请输入合同主体",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          prop: "isRenewal",
          opts: [
            {
              label: "是",
              value: '是'
            },
            {
              label: "否",
              value: '否'
            }
          ],
          label: "合同是否已续签订",
          rules: [
            {
              required: true,
              message: "请输入合同是否已续签订",
              trigger: "blur"
            }
          ]
        },
        {
          label: "合同签订日期",
          placeholder: "请输入合同签订日期",
          type: "date",
          valueFormat: "yyyy-MM-dd",
          prop: "signDate",
          rules: [
            {
              required: true,
              message: "请输入合同签订日期",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          prop: "oneTimes",
          opts: [
            {
              label: "是",
              value: '是'
            },
            {
              label: "否",
              value: '否'
            }
          ],
          label: "是否为首次签",
          rules: [
            {
              required: true,
              message: "请输入是否为首次签",
              trigger: "blur"
            }
          ]
        },
        {
          label: "合同签订次数",
          placeholder: "请输入合同签订次数",
          type: "input",
          prop: "times",
          rules: [
            {
              required: true,
              message: "请输入合同签订次数",
              trigger: "blur"
            }
          ]
        },
        {
          label: "合同起始日期",
          placeholder: "请输入合同起始日期",
          type: "date",
          valueFormat: "yyyy-MM-dd",
          prop: "startDate",
          rules: [
            {
              required: true,
              message: "请输入合同起始日期",
              trigger: "blur"
            }
          ]
        },
        {
          label: "合同到期日期",
          placeholder: "请输入合同到期日期",
          type: "date",
          valueFormat: "yyyy-MM-dd",
          prop: "endDate",
          rules: [
            {
              required: true,
              message: "请输入合同到期日期",
              trigger: "blur"
            }
          ]
        },
        {
          label: "合同类型",
          placeholder: "请输入合同类型",
          type: "select",
          prop: "contractType",
          opts: [
            {
              label: "固定期限",
              value: '固定期限'
            },
            {
              label: "无固定期限",
              value: '无固定期限'
            },
            {
              label: "实习期合同",
              value: '实习期合同'
            }
          ],
          rules: [
            {
              required: true,
              message: "请输入合同类型",
              trigger: "blur"
            }
          ]
        },
        {
          label: "合同年限（年）",
          placeholder: "请输入合同年限",
          type: "input",
          prop: "contractLimit",
          rules: [
            {
              required: true,
              message: "请输入合同年限",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          label: "所属公司",
          labelWidth: "80px",
          prop: "type",
          opts: [
            {
              label: "总部",
              value: '"总部"'
            },
            {
              label: "蚌埠",
              value: '蚌埠'
            },
            {
              label: "合肥",
              value: '合肥'
            },
            {
              label: "常州",
              value: '常州'
            },
            {
              label: "无锡",
              value: '无锡'
            }
          ],
          rules: [
            {
              required: true,
              message: "请填写所属公司",
              trigger: "blur"
            }
          ]
        },
      ]
    };
  },
  
  async mounted() {
    await this.initOpts();
    if (this.$route.query.id) {
      getContractById({ id: this.$route.query.id }).then(res => {
      if (res.code == 200) {
        res.data.contract = res.data.contract? res.data.contract : [];
        res.data = Object.assign(res.data, res.data.contract);
        for (let i = 0; i < res.data.fileList.length; i++) {
          res.data.fileList[i].name = res.data.fileList[i].fileName;
        }
        this.form = Object.assign({}, res.data);
      }
    });
  }
  },



  methods: {  
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.attachmentId = "";
          for (let i = 0; i < this.form.fileList.length; i++) {
            if (i == 0) {
              this.form.attachmentId = this.form.fileList[i].id;
            } else {
              this.form.attachmentId =
                this.form.attachmentId + "," + this.form.fileList[i].id;
            }
          }
          let params = Object.assign({}, this.form);
          if(params.startDate > params.endDate){
             this.$message.warning("合同起始日期不能大于到期日期");
             return;
         }

          if (this.$route.query.id) {
            updateContract(params).then(res => {
              if (res.code == 200) {
                this.$message.success("编辑成功");
                this.$router.go(-1);
              }
            });
          } else {
            addContract(params).then(res => {
              if (res.code == 200) {
                this.$message.success("保存成功");
                this.$router.push({ name: "contract" });
                this.loading = false;
              }
            });
          }
        } else {
          // this.loading = false;
        }
      });
  },





    uploadFileData(file) {
      if (!this.form.fileList) {
      this.form.fileList = [];
    }

    if (file.size > 50 * 1024 * 1024) {
      this.$message("文件过大，请上传小于50MB的文件〜");
      return false;
    }
    var formdata = new FormData();
    formdata.append("files", file.raw);
    //importDevice：请求接口 formdata：传递参数
    uploadFile(formdata).then(res => {
      if (res.code == 200) {
        for (let i = 0; i < res.data.length; i++) {
          res.data[i].name = res.data[i].fileName;
        }
        this.form.fileList = this.form.fileList.concat(res.data);
        this.$message.success("上传成功");
      }
    });
  },


  //   handleRemove(file) {
  //   for (let i = 0; i < this.form.fileList.length; i++) {
  //     if (file.id == this.form.fileList[i].id) {
  //       this.form.fileList.splice(i, 1);
  //       break;
  //     }
  //   }
  // },



    goback() {
    this.$router.go(-1);
  },


    initOpts() {
      getUserName().then(res => {
        if (res.code == 200) {
          for (let i = 0; i < this.formList.length; i++) {
            if (this.formList[i].dataOrigin == "name") {
              this.formList[i].opts = res.data;
            }
          }
        }
      });
  },



  }
};
</script>

<style lang="scss" scoped>
.import-container {
  display: inline-block;
}
.el-date-editor.el-input {
  width: 200px !important;
}
.el-cascader {
  width: 520px !important;
}
.btn-operate {
  margin-top: 15px;
  margin-left: 230px;
  .el-button {
    padding: 12px 35px;
  }
}
.el-divider {
  background-color: #009688;
}
.el-divider__text {
  color: #009688;
}
</style>
