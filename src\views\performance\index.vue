<template>
  <div class="performance-query">
    <!-- 搜索条件和操作按钮 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="年月份">
          <el-date-picker
            v-model="searchForm.years"
            type="month"
            placeholder="选择年月"
            value-format="yyyy-MM"
            format="yyyy年MM月">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="所属分公司">
          <el-select v-model="searchForm.type" placeholder="请选择分公司" clearable>
            <el-option
              v-for="item in branchOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item class="action-buttons">
          <el-button type="primary" @click="showImportDialog = true">
            <i class="el-icon-upload"></i>
            目标达成表导入
          </el-button>
          <el-button type="success" @click="handleAdd" v-if="isAdmin">
            <i class="el-icon-plus"></i>
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
        
        <el-table-column prop="years" label="年月份" width="120" align="center">
          <template slot-scope="scope">
            {{ formatYearMonth(scope.row.years) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="所属分公司" width="120" align="center"></el-table-column>
        
        <el-table-column prop="adds" label="新增" width="120" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.adds) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="renew" label="续费" width="120" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.renew) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="total" label="共计" width="120" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.total) }}
          </template>
        </el-table-column>

        <el-table-column prop="mgtExpense" label="管理费" width="120" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.mgtExpense) }}
          </template>
        </el-table-column>

        <el-table-column prop="profit" label="利润" width="120" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.profit) }}
          </template>
        </el-table-column>

        <el-table-column prop="num" label="在册人数" width="120" align="center"></el-table-column>
        
        <el-table-column prop="monthSalary" label="当月工资总额" width="140" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.monthSalary) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="preTaxSalary" label="工资税前" width="120" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.preTaxSalary) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="avgValue" label="人均产值" width="120" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.avgValue) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="currentMonthRatio" label="当月费效比" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.currentMonthRatio }}%
          </template>
        </el-table-column>
        
        <el-table-column prop="humanRatio" label="人效" width="100" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.humanRatio) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="preTaxRatio" label="税前费效比" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.preTaxRatio }}%
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="updateTime" label="更新时间" width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" align="center" fixed="right" v-if="isAdmin">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="handleEdit(scope.row)">
              修改
            </el-button>
            <el-button type="danger" size="mini" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 目标达成表导入对话框 -->
    <el-dialog
      title="目标达成表导入"
      :visible.sync="showImportDialog"
      width="600px"
      :close-on-click-modal="false">
      
      <div class="import-content">
        <div class="import-tips">
          <span style="color:red">*</span>文件请上传Excel格式，仅限上传单个文件且上传大小不超过50M
        </div>
        
        <div class="import-section">
          <el-upload
            action=""
            :auto-upload="false"
            :show-file-list="true"
            :on-change="handleTargetFileChange"
            :file-list="targetFileList"
            :before-remove="handleTargetFileRemove"
            accept=".xlsx,.xls">
            <el-button type="primary">选择文件</el-button>
          </el-upload>
        </div>
        
        <div class="import-actions" style="margin-top:15px">
          <el-button 
            type="primary" 
            @click="uploadTargetFile" 
            :loading="targetUploading"
            :disabled="targetFileList.length === 0">
            {{ targetUploading ? '上传中...' : '上传' }}
          </el-button>
          
          <el-link
            type="primary"
            @click="downloadTargetTemplate"
            style="margin-left:20px;font-size: 16px;">
            目标达成表模板下载
          </el-link>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showImportDialog = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="showFormDialog"
      width="800px"
      :close-on-click-modal="false">
      
      <el-form :model="formData" :rules="formRules" ref="performanceForm" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="年月份" prop="years">
              <el-date-picker
                v-model="formData.years"
                type="month"
                placeholder="选择年月"
                value-format="yyyy-MM"
                format="yyyy年MM月"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属分公司" prop="type">
              <el-select v-model="formData.type" placeholder="请选择分公司" style="width: 100%">
                <el-option
                  v-for="item in branchOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="新增" prop="adds">
              <el-input v-model.number="formData.adds" placeholder="请输入新增金额">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="续费" prop="renew">
              <el-input v-model.number="formData.renew" placeholder="请输入续费金额">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="共计" prop="total">
              <el-input v-model.number="formData.total" placeholder="请输入共计金额">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="管理费" prop="mgtExpense">
              <el-input v-model.number="formData.mgtExpense" placeholder="请输入管理费">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="利润" prop="profit">
              <el-input v-model.number="formData.profit" placeholder="请输入利润">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="在册人数" prop="num">
              <el-input v-model="formData.num" placeholder="请输入在册人数">
                <template slot="append">人</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="当月工资总额" prop="monthSalary">
              <el-input v-model.number="formData.monthSalary" placeholder="请输入当月工资总额">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当月工资总额" prop="monthSalary">
              <el-input v-model.number="formData.monthSalary" placeholder="请输入当月工资总额">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工资税前" prop="preTaxSalary">
              <el-input v-model.number="formData.preTaxSalary" placeholder="请输入工资税前">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人均产值" prop="avgValue">
              <el-input v-model.number="formData.avgValue" placeholder="请输入人均产值">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="当月费效比" prop="currentMonthRatio">
              <el-input v-model.number="formData.currentMonthRatio" placeholder="请输入当月费效比">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="人效" prop="humanRatio">
              <el-input v-model.number="formData.humanRatio" placeholder="请输入人效">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="税前费效比" prop="preTaxRatio">
              <el-input v-model.number="formData.preTaxRatio" placeholder="请输入税前费效比">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showFormDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPerformanceList, addPerformance, updatePerformance, deletePerformance, uploadTargetFile, getTargetTemplate } from './api'

export default {
  name: 'PerformanceQuery',
  data() {
    return {
      loading: false,
      submitting: false,
      showImportDialog: false,
      showFormDialog: false,
      isEdit: false,
      
      // 搜索表单
      searchForm: {
        years: '',
        type: ''
      },
      
      // 分公司选项
      branchOptions: [
        { label: '合肥', value: '合肥' },
        { label: '蚌埠', value: '蚌埠' },
        { label: '常州', value: '常州' },
        { label: '芜湖', value: '芜湖' },
        { label: '马鞍山', value: '马鞍山' },
        { label: '滁州', value: '滁州' },
        { label: '阜阳', value: '阜阳' },
        { label: '六安', value: '六安' },
        { label: '安庆', value: '安庆' },
        { label: '宣城', value: '宣城' },
        { label: '淮南', value: '淮南' },
        { label: '淮北', value: '淮北' },
        { label: '铜陵', value: '铜陵' },
        { label: '池州', value: '池州' },
        { label: '黄山', value: '黄山' },
        { label: '亳州', value: '亳州' },
        { label: '宿州', value: '宿州' }
      ],
      
      // 表格数据
      tableData: [],
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      
      // 表单数据
      formData: {
        id: null,
        years: '',
        type: '',
        adds: 0,
        renew: 0,
        total: 0,
        mgtExpense: 0,
        profit: 0,
        num: '',
        monthSalary: 0,
        preTaxSalary: 0,
        avgValue: 0,
        currentMonthRatio: 0,
        humanRatio: 0,
        preTaxRatio: 0
      },
      
      // 表单验证规则
      formRules: {
        years: [
          { required: true, message: '请选择年月份', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择所属分公司', trigger: 'change' }
        ],
        adds: [
          { required: true, message: '请输入新增金额', trigger: 'blur' },
          { type: 'number', message: '新增金额必须为数字值', trigger: 'blur' }
        ],
        renew: [
          { required: true, message: '请输入续费金额', trigger: 'blur' },
          { type: 'number', message: '续费金额必须为数字值', trigger: 'blur' }
        ],
        total: [
          { required: true, message: '请输入共计金额', trigger: 'blur' },
          { type: 'number', message: '共计金额必须为数字值', trigger: 'blur' }
        ],
        num: [
          { required: true, message: '请输入在册人数', trigger: 'blur' }
        ]
      },
      
      // 目标达成表导入相关
      targetFileList: [],
      targetUploading: false
    }
  },
  
  computed: {
    // 是否为管理员
    isAdmin() {
      const userInfo = this.$store.getters.userInfo || {}
      return userInfo.role === 'admin' || userInfo.isAdmin
    },
    
    // 对话框标题
    dialogTitle() {
      return this.isEdit ? '编辑业绩数据' : '新增业绩数据'
    }
  },
  
  mounted() {
    this.loadData()
  },
  
  methods: {
    // 加载数据
    loadData() {
      this.loading = true
      
      const params = {
        ...this.searchForm,
        page: this.pagination.currentPage,
        pageSize: this.pagination.pageSize
      }
      
      getPerformanceList(params).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.list || []
          this.pagination.total = res.data.total || 0
        } else {
          this.$message.error(res.message || '数据加载失败')
        }
      }).catch(error => {
        console.error('数据加载失败:', error)
        this.$message.error('数据加载失败')
      }).finally(() => {
        this.loading = false
      })
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        years: '',
        type: ''
      }
      this.pagination.currentPage = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },

    // 新增
    handleAdd() {
      this.isEdit = false
      this.formData = {
        id: null,
        years: '',
        type: '',
        adds: 0,
        renew: 0,
        total: 0,
        mgtExpense: 0,
        profit: 0,
        num: '',
        monthSalary: 0,
        preTaxSalary: 0,
        avgValue: 0,
        currentMonthRatio: 0,
        humanRatio: 0,
        preTaxRatio: 0
      }
      this.showFormDialog = true
    },

    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.formData = { ...row }
      this.showFormDialog = true
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该条业绩数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePerformance(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.loadData()
          } else {
            this.$message.error(res.message || '删除失败')
          }
        }).catch(error => {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        })
      })
    },

    // 提交表单
    handleSubmit() {
      this.$refs.performanceForm.validate((valid) => {
        if (valid) {
          this.submitting = true

          const apiMethod = this.isEdit ? updatePerformance : addPerformance
          const successMessage = this.isEdit ? '修改成功' : '新增成功'

          apiMethod(this.formData).then(res => {
            if (res.code === 200) {
              this.$message.success(successMessage)
              this.showFormDialog = false
              this.loadData()
            } else {
              this.$message.error(res.message || '操作失败')
            }
          }).catch(error => {
            console.error('操作失败:', error)
            this.$message.error('操作失败')
          }).finally(() => {
            this.submitting = false
          })
        }
      })
    },

    // 目标达成表文件选择
    handleTargetFileChange(file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
      this.targetFileList = fileList
    },

    // 目标达成表文件移除
    handleTargetFileRemove(file, fileList) {
      this.targetFileList = fileList
      return true
    },

    // 上传目标达成表
    async uploadTargetFile() {
      if (this.targetFileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      const file = this.targetFileList[0]

      if (file.size > 50 * 1024 * 1024) {
        this.$message.error('文件过大，请上传小于50MB的文件')
        return
      }

      const fileName = file.name.toLowerCase()
      if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
        this.$message.error('请上传Excel格式的文件')
        return
      }

      this.targetUploading = true

      try {
        const formData = new FormData()
        formData.append('file', file.raw)
        formData.append('skipDuplicates', true)

        const response = await uploadTargetFile(formData)

        if (response.code === 200) {
          this.$message.success('目标达成表上传成功')
          this.targetFileList = []
          this.showImportDialog = false
          this.loadData() // 重新加载数据
        } else {
          this.$message.error(response.message || '上传失败')
        }
      } catch (error) {
        console.error('目标达成表上传失败:', error)
        this.$message.error('上传失败，请重试')
      } finally {
        this.targetUploading = false
      }
    },

    // 下载目标达成表模板
    downloadTargetTemplate() {
      getTargetTemplate()
        .then(res => {
          const blob = new Blob([res]);
          const elink = document.createElement("a");
          elink.download = "目标达成表模板" + ".xlsx";
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        })
        .catch(error => {
          console.error('模板下载失败:', error);
          this.$message.error('模板下载失败，请重试');
        });
    },

    // 格式化数字
    formatNumber(value) {
      if (!value && value !== 0) return '-'
      return Number(value).toLocaleString()
    },

    // 格式化年月
    formatYearMonth(value) {
      if (!value) return '-'
      const [year, month] = value.split('-')
      return `${year}年${month}月`
    },

    // 格式化日期时间
    formatDateTime(value) {
      if (!value) return '-'
      return new Date(value).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.performance-query {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);



  .search-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    margin-top: 10px;

    .demo-form-inline {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 15px;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 0;
      }

      // 操作按钮区域
      .action-buttons {
        margin-left: auto;

        .el-button {
          margin-left: 10px;

          &:first-child {
            margin-left: 0;
          }
        }
      }
    }
  }

  .table-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;

    .el-table {
      .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: #f5f7fa !important;
          }
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  // 导入对话框样式
  .import-content {
    .import-tips {
      margin-bottom: 15px;
      font-size: 14px;
      color: #666;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
    }

    .import-section {
      margin-bottom: 15px;
    }

    .import-actions {
      display: flex;
      align-items: center;
      gap: 20px;
    }
  }

  // 表单对话框样式
  .dialog-footer {
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .performance-query {
    padding: 10px;

    .search-form {
      .demo-form-inline {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;

        .el-form-item {
          display: block;
          margin-bottom: 0;

          .el-form-item__content {
            margin-left: 0 !important;
          }
        }

        .action-buttons {
          margin-left: 0;
          text-align: center;

          .el-button {
            margin: 0 5px;
          }
        }
      }
    }

    .table-container {
      overflow-x: auto;
    }
  }
}
</style>
