.container {
  flex: 1;
  height: 100%;
  width:40%;
  background-color: rgb(241, 242, 247);
  display: flex;
  flex-direction: column;
  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #333333;
    }

    .desc {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #333333;

    }
  }

  .content {
    height: 264px;
    display: flex;
    margin-top: 20px;

    .today {
      margin-top: 32px;
      flex: 1;

      >h2 {

        font-size: 14px;
        font-family: PingFangSC-Regular;
        color: #0486FE;
        margin: 0;
        margin-bottom: 10px;
      }

      .card {
        border: 1px solid #E4E7ED;
        background: #F5F6F7;
        width: 140px;
        height: 75px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        >span {
          margin-bottom: 5px;
          font-size: 22px;
          color: #333333;
        }

        .status {
          display: flex;
          align-items: center;

          img {
            width: 14px;
            height: 14px;
            margin-right: 8px;
            position: relative;
            top: -1px;
          }
        }



        &+.card {
          margin-top: 20px;
        }
      }
    }

    .calendar {
      margin-left: 40px;
      height: 100%;
      width: 321px;

      :global {
        .el-calendar {
          .el-calendar__header {
            display: none;
          }

          .el-calendar__body {
            padding: 10px 20px 0;

            border: 1px solid #E4E7ED;

            thead th {

              color: #333333;
              font-size: 14px;
              font-weight: 400;
              border-bottom: 1px solid #D8DCE6;
            }

            td {
              border: 0;

              &.is-selected {

                background: #F5F7FA;

                span {
                  color: rgba(6, 134, 254, 1) !important;
                }
              }

              &.current {
                span {
                  color: #333333;
                }
              }

              .el-calendar-day {
                width: 40px;
                height: 37px;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;

                span {
                  font-size: 14px;
                  font-weight: 400;
                }
              }
            }
          }
        }
      }
    }
  }
}

.success {
  position: relative;
  &::after{
    position: absolute;
    display: block;
    content: ' ';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #409EFF;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.fail {
  position: relative;
  &::after{
    position: absolute;
    display: block;
    content: ' ';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: orange;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
  }
}