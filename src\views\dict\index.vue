<template>
  <el-row class="page-container">
    <el-row class="page-main">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self 
           :columns="columns" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>

      <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true"
        @handleConfirm="handleConfirm" :label-width="'90'" :title="drawTitle"></draw-form>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import {queryPage,addDict,deletetDict,updateDict,getDictById} from "./api";

export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
        total: 0,
        id: 0,
        drawTitle: "",
        drawForms: [
          {
              type: "input",
              label: "字典类型",
              prop: "dictType",
              placeholder: "请填写字典类型",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写字典类型",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              label: "字典类型名称",
              prop: "dictTypeName",
              placeholder: "请填写字典类型名称",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写字典类型名称",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              label: "字典编码",
              prop: "dictCode",
              placeholder: "请填写字典编码",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写字典编码",
                  trigger: "blur"
                }
              ],
            },
            {
              type: "input",
              label: "字典值",
              prop: "dictValue",
              placeholder: "请填写字典值",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写字典值",
                  trigger: "blur"
                }
              ],
            },
            {
              type: "input",
              label: "字典中文名称",
              prop: "dictName",
              placeholder: "请填写字典中文名称",
              class: "inputMore9",          
            },
        ],
        dataList: [],
        columns: [
          {
            value: "dictType",
            label: "字典类型",
            width: 120
          },
          {
            value: "dictTypeName",
            label: "字典类型名称"
          },
          {
            value: "dictCode",
            label: "字典编码"
          },
          {
            value: "dictName",
            label: "字典中文名称"
          },
          {
            value: "dictValue",
            label: "字典值"
          },
          {
            value: "userName",
            label: "创建人"
          },
          {
            value: "createdAt",
            label: "创建时间",
            width: 180,
            formatter(row) {
              return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          {
            value: "updateUserName",
            label: "更新人"
          },
          {
            value: "updatedAt",
            label: "更新时间",
            width: 180,
            formatter(row) {
              return date.dateFormat(row.updatedAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
         
         
          {
            label: "操作",
            fixed: "right",
            width: 180,
            operType: "button",
            operations: [
              {
                label: "编辑",
                type: "primary",
                func: this.handleEdit,
              },
              {
                label: "删除",
                type: "danger",
                func: this.handleDelete,
                
              }
            ]
          }

        ],

      searchList: [
        {
          label: "字典类型",
          labelWidth: "80px",
          type: "input",
          prop: "dictType"
        },
        {
          label: "字典中文名称",
          labelWidth: "100px",
          type: "input",
          prop: "dictName"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "新增",
              icon: "el-icon-plus",
              func: this.handleAdd
            }
          ]
        }
      ]
    };

  },
  watch: {},
  mounted() {
    this.handleSearch();
  },
  methods: {

    handleAdd() {
      this.drawTitle = "新增数据字典";
      this.$refs.drawForm.open();
      this.initForm = { };
      this.$refs.drawForm.initforms(this.initForm);
    },


    handleDelete(row) {
      this.$confirm(`确定要删除该记录吗?`, "提示", {
        type: "warning"
      })
        .then(() => {
          this.tableLoading = true;
          deletetDict({ id: row.id })
            .then(res => {
              if (res.code == 200) {
                this.$message.success("删除成功");
                this.handleSearch();
              } else {
                this.tableLoading = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },


    handleEdit(row) {
      this.drawTitle = "编辑数据字典";
      this.$refs.drawForm.open();
      this.$refs.drawForm.showLoading();
      getDictById({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            this.$refs.drawForm.initforms(res.data);
            this.$nextTick(function() {
              this.initForm = Object.assign({}, this.$refs.drawForm.form);
            });
          }
          this.$refs.drawForm.hideLoading();
        })
        .catch(err => {
          console.log(err);
          this.$refs.drawForm.hideLoading();
        });
    },


    handleConfirm(obj) {
      this.$refs.drawForm.showLoading();
      const params = Object.assign({}, obj);
      if (obj.id) {
        updateDict(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("更新成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      } else {
        addDict(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("保存成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      }
    },
    

    close() {
        this.initForm = {};
        this.$refs.drawForm.close();
      },

      handleFormSearch(form) {
        this.pageIndex = 1;
        this.handleSearch(form);
      },

      tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

      handleSearch(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;
        this.tableLoading = true;
        queryPage(params).then(res => {
          if (res.code == 200) {
            this.total = res.data.total;
            this.dataList = res.data.list;
          }
          this.tableLoading = false;
        });
      }
  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>