<template>
  <el-row class="organization-structure">
    <el-button type="plain" icon="el-icon-arrow-left" @click="back"
      >返回</el-button
    >
    <el-row>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="操作权限" name="first">
          <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item
              v-for="(item, index) in perList"
              :key="index"
              :name="item.value"
            >
              <template slot="title">
                <el-divider content-position="left">{{
                  item.label
                }}</el-divider>
              </template>

              <template v-for="(item2, index2) in item.list">
                <div :key="index2">
                  <div
                    class="classify"
                    :style="index2 != 0 ? ' margin-top: 20px;' : ''"
                  >
                    <span style="display: inline-block; margin-right: 6px"
                      >{{ item2.label }}
                    </span>
                    <el-checkbox
                      :indeterminate="
                        computeIndeterminate(item2.list).indeterminate
                      "
                      v-model="computeIndeterminate(item2.list).checkAll"
                      @change="val => handleCheckAllChange(val, item2.list)"
                      >全选</el-checkbox
                    >
                  </div>
                  <div style="margin: 15px 0"></div>
                  <el-checkbox-group v-model="checkData">
                    <el-checkbox
                      v-for="item3 in item2.list"
                      :label="item3.value"
                      :key="item3.value"
                      >{{ item3.label }}</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </template>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
        <el-tab-pane label="数据权限" name="second">
          <el-radio-group v-model="deptObj.level">
            <el-radio :label="0">员工（仅自己的）</el-radio>
            <el-radio :label="1">部门及其子部门的员工</el-radio>
            <el-radio :label="2">全公司所有的员工</el-radio>
          </el-radio-group>
        </el-tab-pane>
        <el-tab-pane label="首页显示" name="third">角色管理</el-tab-pane>
      </el-tabs>
      <el-button
        type="primary"
        @click="save"
        style="margin-top: 15px; padding: 9px 36px"
        >保存</el-button
      >
    </el-row>
  </el-row>
</template>
<script>
import crmRouter from "@/router/modules/crm";
import { perList } from "../store";
import { getPermission, setPermission, updateRoles } from "../api";
const cityOptions = [
  {
    label: "开通线索",
    value: "1"
  },
  {
    label: "查企业",
    value: "2"
  },
  {
    label: "行业模板",
    value: "3"
  },
  {
    label: "找线索",
    value: "4"
  }
];
export default {
  data() {
    return {
      perList: perList,
      activeName: "first",
      activeNames: [1, 2, 3, 4, 5],
      checkedCities: [],
      cities: cityOptions,
      crmRouter: crmRouter,
      isIndeterminate: false,
      checkData: [],
      deptObj: {}
    };
  },
  mounted() {},
  methods: {
    back() {
      this.$emit("back");
    },

    initData(dept) {
      getPermission({
        id: dept.roleId
      }).then(res => {
        if (res.code == 200) {
          if (res.data) {
            this.deptObj = res.data;
            if (res.data.access) {
              this.checkData = res.data.access.split(",");
            }
          }
        }
      });
    },

    // 保存权限
    save() {
      if (this.activeName == "first") {
        let data = Object.assign(this.deptObj, {
          access: this.checkData.join(",")
        });
        setPermission(data).then(res => {
          if (res.code == 200) {
            if (res.data) {
              this.$message.success("保存成功");
            }
          }
        });
      } else if (this.activeName == "second") {
        updateRoles(this.deptObj).then(res => {
          if (res.code == 200) {
            if (res.data) {
              this.$message.success("保存成功");
            }
          }
        });
      }
    },

    handleClick(tab, event) {
      console.log(tab, event);
    },

    handleChange(val) {
      console.log(val);
    },

    handleCheckAllChange(val, opts) {
      if (val) {
        for (let i = 0; i < opts.length; i++) {
          let j = this.checkData.indexOf(opts[i].value);
          if (j == -1) {
            this.checkData.push(opts[i].value);
          }
        }
      } else {
        for (let i = 0; i < opts.length; i++) {
          let j = this.checkData.indexOf(opts[i].value);
          if (j > -1) {
            this.checkData.splice(j, 1);
          }
        }
      }
    },

    computeIndeterminate(opts) {
      let arr = this.checkData;
      let checkAll = opts.every(itemB => {
        return this.checkData.some(itemA => itemA == itemB.value);
      });

      const count = arr.filter(arr => opts.some(a => a.value == arr)).length;
      return {
        indeterminate: count > 0 && count < opts.length,
        checkAll: checkAll
      };
    }
  }
};
</script>

<style lang="scss">
.organization-structure {
  .el-tab-pane {
    .el-collapse-item__header {
      height: 64px;
      font-weight: 400;
      line-height: 64px;
      .el-divider__text {
        font-size: 20px;
        padding: 0 10px;
        color: #1c77f8;
        font-weight: 400;
      }
      .el-divider {
        background-color: #1c77f8;
      }
    }
    .el-collapse-item__content {
      .classify {
        display: block;
        padding: 9px 15px;
        font-weight: 400;
        line-height: 20px;
        display: inline-block;
        width: 100%;
        border-bottom: 1px solid #eee;
        background: #f7f7f7;
        text-align: left;
        box-sizing: border-box;
      }
      .el-checkbox-group {
        padding-left: 16px;
        .el-checkbox {
          width: 17%;
          margin-top: 10px;
        }
      }
    }
  }
}
</style>
