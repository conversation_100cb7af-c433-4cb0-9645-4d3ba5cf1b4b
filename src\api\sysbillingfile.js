import {
  fetch,
  post,
  deleteHttp
} from '@/utils/request'

// 云账单
export const getSysbillingfileList = (data) => {
  return fetch(`/billing-file/page/${data.pageNo}/${data.pageSize}`, data)
}

// 资源消费
export const getResourceConsumeList = (data) => {
  return fetch(`/resource-consume/page/${data.pageNo}/${data.pageSize}`, data)
}

// 流水账单
export const getFlowDetailList = (data) => {
  return fetch(`/flow-detail/page/${data.pageNo}/${data.pageSize}`, data)
}

// 资源详单
export const getResourceDetailList = (data) => {
  return fetch(`/resource-detail/page/${data.pageNo}/${data.pageSize}`, data)
}

