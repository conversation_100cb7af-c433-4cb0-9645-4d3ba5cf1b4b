<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
    <el-tabs>
      <el-tab-pane :label="'合同管理列表'">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self :columns="columns" :current-page="pageIndex" :list-loading="tableLoading" :table-data="dataList"
          :total-count="total" :page-sizes="pageSizes" :page-size="pageSize" @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>

      <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true"
        @handleConfirm="handleConfirm" :label-width="'90'" :title="drawTitle"></draw-form>


        <add ref="add" :title="drawTitle" @refresh="handleSearch"></add>


      </el-tab-pane>
        <el-tab-pane label="合同附件上传" name="second">
          <span style="color:red">*</span>合同附件请上传pdf、png、jpeg、jpg格式
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              :on-change="uploadByJsqd"
              :file-list="fileList">
              <el-button type="plain" style="margin-left:10px">选择文件</el-button>
            </el-upload>
          </div>  
        </el-tab-pane>

       <el-tab-pane :label="'合同附件'">
        <el-row>
          <direct-search ref="form" :label-position="'right'"
            :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
            :forms="searchList1"
            @handleSearch="handleFormSearch1" />
        </el-row>
        <el-col style="padding: 15px 0" >
          <el-table-self
            :columns="columns1" 
            :current-page="pageIndex" 
            :list-loading="tableLoading" 
            :table-data="dataList1"
            :total-count="total" 
            :page-sizes="pageSizes" 
            :page-size="pageSize" 
            @pageSizeChange="handleSizeChange"
            @currentPageChange="handleCurrentChange" show-overflow-tooltip/>
        </el-col>

      </el-tab-pane>

    </el-tabs>
    </el-row>
  </el-row>

</template>




<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import { getUsers, queryPage, getContractById, getUserName,addContract,updateContract,uploadInfo,htPage} from "./api";
import add from "./add";
export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm,
    add
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
      total: 0,
      id: 0,
      drawTitle: "",
      dataList: [],
      drawForms: [
        {
          label: "员工姓名",
          type: "select",
          diyLabel: "name",
          diyValue: "id",
          isSelect: true,
          popperClass: "select-user-name",
          prop: "userId",
          dataOrigin: "userName",
          opts: []
        },
        {
          type: "input",
          label: "合同编号",
          prop: "contractNo",
          placeholder: "请填写合同编号",
          class: "inputMore9",
          note:
            "<div>合同编号填写规则：省份首字母/城市首字母/年份+4位顺序号</div>",
          rules: [
            {
              required: true,
              message: "请填写合同编号",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "合同主体",
          prop: "contractName",
          placeholder: `请填写合同主体`,
          class: "inputMore9",
          note:
            "<div>合同主体：填写公司名称，写全称</div>",
          rules: [
            {
              required: true,
              message: "请填写合同主体",
              trigger: "blur"
            }
          ]
        },
        {
          type: "radio",
          prop: "isRenewal",
          maxlength: 100,
          opts: [
            {
              label: "是",
              value: 1
            },
            {
              label: "否",
              value: 2
            }
          ],
          label: "合同是否已续签订"
        },
        {
          type: "date",
          class: "inputMore9",
          label: "合同签订日期",
          prop: "signDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择合同签订日期`,
          rules: [
            {
              required: true,
              message: "请选择合同签订日期",
              trigger: "blur"
            }
          ]
        },
        {
          type: "radio",
          prop: "oneTimes",
          maxlength: 100,
          opts: [
            {
              label: "是",
              value: 1
            },
            {
              label: "否",
              value: 2
            }
          ],
          label: "是否为首次签"
        },
        {
          type: "input",
          class: "inputMore9",
          label: "合同签订次数",
          prop: "times",
          placeholder: `请输入合同签订次数`,
          rules: [
            {
              required: true,
              message: "请输入合同签订次数",
              trigger: "blur"
            }
          ]
        },
        {
          type: "date",
          class: "inputMore9",
          label: "合同起始日期",
          prop: "startDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择合同起始日期`,
          rules: [
            {
              required: true,
              message: "请选择合同起始日期",
              trigger: "blur"
            }
          ]
        },
        {
          type: "date",
          class: "inputMore9",
          label: "合同到期日期",
          prop: "endDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择合同到期日期`,
          rules: [
            {
              required: true,
              message: "请选择合同到期日期",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          class: "inputMore9",
          label: "合同类型",
          prop: "contractType",
          placeholder: `请选择合同类型`,
          opts: [
            {
              label: "固定期限",
              value: 1
            },
            {
              label: "无固定期限",
              value: 2
            },
            {
              label: "实习期合同",
              value: 3
            },
          ],
          rules: [
            {
              required: true,
              message: "请选择合同类型",
              trigger: "change"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "合同年限（年）",
          prop: "contractLimit",
          placeholder: `请填写合同年限`,
          rules: [
            {
              required: true,
              message: "请填写合同年限",
              trigger: "blur"
            }
          ]
        },
        // {
        //   type: "uploadImg",
        //   label: "附件上传",
        //   prop: "picList",
        //   class: "inputMore9"
        // },
        
       
      ],
      dataList: [],
      fileList: [],
      columns: [
        {
          label: "员工",
          value: "userName"
        },
        {
          label: "合同编号",
          value: "contractNo",
          copy: true,
          // hidden: true,
          width: 140
        },
        {
          label: "合同主体",
          value: "contractName"
        },

        {
          label: "合同是否已续签订",
          value: "isRenewal",
          formatter(row) {
            return row.isRenewal == '1' ? "是" : row.isRenewal == '2' ? "否" : "";
          }
        },
        {
          label: "合同签订日期",
          value: "signDate",
          formatter(row) {
            return date.dateFormat(row.signDate, "YYYY-MM-DD");
          }
        },

        {
          label: "是否为首次签",
          value: "oneTimes",
          formatter(row) {
            return row.oneTimes == '1' ? "是" : row.oneTimes == '2' ? "否" : "";
          }
        },
        {
          label: "合同签订次数",
          value: "times"
        },

        {
          label: "合同起始日期",
          width: 110,
          value: "startDate",
          formatter(row) {
            return date.dateFormat(row.startDate, "YYYY-MM-DD");
          }
        },
        {
          label: "合同到期日期",
          value: "endDate",
          formatter(row) {
            return date.dateFormat(row.endDate, "YYYY-MM-DD");
          }
        },
        {
          label: "合同类型",
          value: "contractType",
          formatter(row) {
            return row.contractType == '1'
              ? "固定期限"
              : row.contractType == '2'
                ? "无固定期限"
                : row.contractType == '3'
                  ? "实习期合同"
                  : "";
          }
        },
        {
          label: "合同年限",
          value: "contractLimit",
          width: 110,
        },

        {
          value: "createdAt",
          label: "创建时间",
          width: 180,
          formatter(row) {
            return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "操作",
          width: 200,
          operType: "button",
          operations: [
            {
              label: "编辑",
              type: "primary",
              func: this.handleEdit
            }
            // {
            //   label: "上传",
            //   type: "warning",
            //   func: this.handleUpload,

            // }
          ]
        }

      ],


      //----------合同附件列表 start------------------
      dataList1: [],
      tableLoading: true,
      columns1: [
        {
          value: "fileName",
          label: "文件名称"
        },
        {
          value: "fileSize",
          label: "文件大小(KB)"
        },
        {
          value: "userName",
          label: "上传人"
        },
        {
          value: "createdAt",
          label: "上传时间",
          formatter(row) {
            return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "操作",
          fixed: "right",
          width: 180,
          operType: "button",
          operations: [
            {
              label: "下载文件",
              type: "primary",
              func: this.handleDownload,
            }
          ]
        }

      ],
      //----------合同附件列表 end------------------




      searchList: [
        {
          label: "员工姓名",
          labelWidth: "80px",
          type: "input",
          prop: "userName"
        },
        {
          label: "合同主体",
          labelWidth: "80px",
          type: "input",
          prop: "contractName"
        },
        {
          type: "select",
          label: "所属公司",
          labelWidth: "80px",
          prop: "type",
          opts: [
            {
              label: "总部",
              value: 1
            },
            {
              label: "蚌埠分公司",
              value: 2
            },
            {
              label: "合肥分公司",
              value: 3
            },
            {
              label: "常州分公司",
              value: 4
            },
            {
              label: "无锡分公司",
              value: 5
            }
          ]
        },
        {
          type: "date",
          dateType: "daterange",
          valueFormat: "yyyy-MM-dd",
          labelWidth: "80px",
          class: "date-range",
          seniorSelect: true,
          label: "入职日期",
          prop: "entryTime"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "新增",
              icon: "el-icon-plus",
              func: this.handleAdd
            }
          ]
        }
      ],


      //----------合同附件列表查询条件 start------------------
      searchList1: [
        {
          label: "文件名称",
          labelWidth: "80px",
          type: "input",
          prop: "fileName"
        },
        {
          label: "上传人",
          labelWidth: "100px",
          type: "input",
          prop: "userName"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch1,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            }
          ]
        }
      ],
      //----------合同附件列表查询条件 end------------------


    };

  },
  watch: {},
  async mounted() {
    this.handleSearch();
    await this.initOpts();
  },
  methods: {
    handleAdd() {
      this.drawTitle = "新增合同";
      // this.$refs.add.open();
      this.$refs.drawForm.open();
      this.initForm = {isRenewal: 1,oneTimes:1};
      this.$refs.drawForm.initforms(this.initForm);
    },


    getUsersData() {
      getUsers({ name: "" }).then(res => {
        if (res.code == 200) {
          this.data = res.data;
        }
      });
    },



    handleRemove(file) {
      for (let i = 0; i < this.form.fileList.length; i++) {
        if (file.id == this.form.fileList[i].id) {
          this.form.fileList.splice(i, 1);
          break;
        }
      }
    },



       //文件发生改变就会触发的事件
      uploadByJsqd(file) {
      //判断是否符合beforeAvatarUpload方法中的条件
      if (this.beforeAvatarUpload(file)) {
        this.fileList.name = file.name;
        this.fileList.url = "";
        var formdata = new FormData();
        formdata.append("file", file.raw);
        formdata.append("type", '3');
        //importDevice：请求接口 formdata：传递参数
        uploadInfo(formdata).then(res => {
          if (res.code == 200) {
            this.$message.success("上传成功");
            this.importFile = "";
            this.fileList = [];
          }
        });
      }
    },


     //文件校验方法
     beforeAvatarUpload(file) {
      // 通过split方法和fileArr方法获取到文件的后缀名
      let fileArr = file.name.split(".");
      let suffix = fileArr[fileArr.length - 1];
      //只能导入.xls和.xlsx文件
      if (!/(pdf|png|jpeg|jpg)/i.test(suffix)) {
        this.$message("文件格式不正确");
        return false;
      }
      //不能导入大小超过2Mb的文件
      if (file.size > 10 * 1024 * 1024) {
        this.$message("文件过大，请上传小于10MB的文件〜");
        return false;
      }
      return true;
    },



    handleEdit(row) {
      this.drawTitle = "编辑合同";
      this.$refs.drawForm.open();
      this.$refs.drawForm.showLoading();
      getContractById({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            this.$refs.drawForm.initforms(res.data);
            this.$nextTick(function () {
              this.initForm = Object.assign({}, this.$refs.drawForm.form);
            });
          }
          this.$refs.drawForm.hideLoading();
        })
        .catch(err => {
          console.log(err);
          this.$refs.drawForm.hideLoading();
        });
    },




    handleConfirm(obj) {
      this.$refs.drawForm.showLoading();
      const params = Object.assign({}, obj);
      if (obj.id) {
        updateContract(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("更新成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      } else {
        addContract(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("保存成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      }
    },


    initOpts() {
      getUserName().then(res => {
        if (res.code == 200) {
          for (let i = 0; i < this.drawForms.length; i++) {
            if (this.drawForms[i].dataOrigin == "userName") {
              this.drawForms[i].opts = res.data;
              break;
            }
          }
        }
      });
    },


    close() {
      this.initForm = {};
      this.$refs.drawForm.close();
    },

    handleFormSearch(form) {
      this.pageIndex = 1;
      this.handleSearch(form);
    },

    tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

    handleSearch(form) {
      const params = Object.assign(this.$refs.form.form, form);
      params.page = this.pageIndex;
      params.pageSize = this.pageSize;
      this.tableLoading = true;

      if (params.entryTime && params.entryTime.length > 0) {
        params.startTime = params.entryTime[0];
        params.endTime = params.entryTime[1];
        // delete params.entryTime;
      }else{
        params.startTime = null;
        params.endTime = null;
      }

      queryPage(params).then(res => {
        if (res.code == 200) {
          this.total = res.data.total;
          this.dataList = res.data.list;
        }
        this.tableLoading = false;
      });
    },

      //--------------- 合同附件列表查询条件  start -------------------
      handleFormSearch1(form) {
        this.pageIndex = 1;
        this.handleSearch1(form);
      },

      handleSearch1(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;
        this.tableLoading = true;
        htPage(params).then(res => {
          if (res.code == 200) {
            this.total = res.data.total;
            this.dataList1 = res.data.list;
          }
          this.tableLoading = false;
        });
      },
      //--------------- 合同附件列表查询条件  end -------------------


  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>
<style>
.select-user-name {
  max-width: 700px !important;
}
</style>
