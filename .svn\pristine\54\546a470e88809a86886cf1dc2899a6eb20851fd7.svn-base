<template>
  <div class="salary-analysis">
    <!-- 页面标题和筛选条件 -->
    <div class="header">
      <h2>{{ years }} {{ type }}分司薪资分析（税前）</h2>
      <div class="filters">
        <el-date-picker v-model="years" type="month" placeholder="选择年月" value-format="yyyy-MM" format="yyyy年MM月"
          @change="loadData" />
        <el-select v-model="type" placeholder="分司" @change="loadData">
          <el-option
            v-for="option in branchOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value">
          </el-option>
        </el-select>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-cards" v-loading="loading">
      <div class="card card-primary">
        <div class="label">年度申报金额</div>
        <div class="value">{{ formatNumber(statsData.annualDeclarationAmount) }} <span class="unit">元</span></div>
      </div>
      <div class="card card-success">
        <div class="label">申报人次</div>
        <div class="value">{{ formatNumber(statsData.applicantCount) }} <span class="unit">人次</span></div>
      </div>
      <div class="card card-warning">
        <div class="label">人均工资</div>
        <div class="value">{{ formatNumber(statsData.averageSalary) }} <span class="unit">元</span></div>
      </div>
      <div class="card card-info">
        <div class="label">社保缴纳比例</div>
        <div class="value">{{ formatNumber(statsData.socialInsuranceRatio) }}%</div>
      </div>
      <div class="card card-primary">
        <div class="label">人均产值（在册人数/流水）</div>
        <div class="value">{{ formatNumber(statsData.averageProductivity) }} <span class="unit">元</span></div>
      </div>
      <div class="card card-danger">
        <div class="label">工资费销比（支出/流水）</div>
        <div class="value">{{ formatNumber(statsData.salaryCostRatio) }}%</div>
      </div>
      <!-- <div class="card">
        <div class="label">“财税顾问”人均工资</div>
        <div class="value">{{ formatNumber(statsData.averageSalaryTaxConsultant) }} <span class="unit">元</span></div>
      </div> -->
      <div class="card card-purple">
        <div class="label">全司综合提成占比</div>
        <div class="value">{{ formatNumber(statsData.commissionRatio) }}%</div>
      </div>
      <div class="card card-success">
        <div class="label">>6个月人均工资</div>
        <div class="value">{{ formatNumber(statsData.averageSalaryOver6Months) }} <span class="unit">元</span></div>
      </div>
      <div class="card card-warning">
        <div class="label">≤6个月工龄人均工资</div>
        <div class="value">{{ formatNumber(statsData.averageSalaryUnder6Months) }} <span class="unit">元</span></div>
      </div>
      <div class="card card-info">
        <div class="label">≤6个月工龄工资占比</div>
        <div class="value">{{ formatNumber(statsData.salaryRatioUnder6Months) }}%</div>
      </div>
    </div>

    <!-- 图表区域 -->
    <!-- 第一排：饼状图和层级统计分离显示 -->
    <div class="charts-row first-row">
      <div class="chart-item pie-chart-item">
        <h3>部门人数及薪资占比</h3>
        <div ref="pieChart" class="chart" v-loading="chartLoading"></div>
      </div>
      <div class="chart-item level-stats-item">
        <h3>层级占比统计</h3>
        <div class="level-stats-image" v-if="levelDisplayData && levelDisplayData.length > 0">
          <canvas ref="levelStatsCanvas" class="level-stats-canvas"></canvas>
        </div>
      </div>
    </div>

    <!-- 第二排：部门分布和销售提成并列显示 -->
    <div class="charts-row second-row">
      <div class="chart-item department-chart-item">
        <!-- <h3>部门分布</h3> -->
        <div ref="barChart" class="chart" v-loading="chartLoading"></div>

        <!-- 部门分布数据表格 -->
        <div class="department-table" v-if="departmentTableData && departmentTableData.length > 0">
          <table>
            <thead>
              <tr>
                <th v-for="department in departmentTableData" :key="department.departmentName">
                  {{ department.departmentName }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td v-for="department in departmentTableData" :key="department.departmentName + '_total'">
                  {{ formatNumber(department.totalSalary) }}
                </td>
              </tr>
              <tr>
                <td v-for="department in departmentTableData" :key="department.departmentName + '_avg'">
                  {{ formatNumber(department.averageSalary) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="chart-item commission-chart-item">
        <!-- <h3>销售提成（业绩）占比</h3> -->
        <div ref="lineChart" class="chart" v-loading="chartLoading"></div>

        <!-- 销售提成数据表格 - 图片格式显示 -->
        <div class="commission-table-image" v-if="commissionTableData && commissionTableData.length > 0">
          <canvas ref="commissionCanvas" class="commission-canvas"></canvas>
        </div>
      </div>
    </div>

    <!-- 第三排：年度薪资成本趋势 -->
    <div class="charts-row third-row">
      <div class="chart-item trend-chart-item">
        <!-- <h3>年度薪资成本趋势</h3> -->
        <div ref="trendChart" class="trend-chart" v-loading="chartLoading"></div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { getSalaryAnalysisData, getDepartmentData, getSalesData, getTrendData } from './api'

export default {
  name: 'SalaryAnalysis',
  data() {
    return {
      years: '2025-01',
      type: '蚌埠',
      loading: false,
      chartLoading: false,
      // 分司选项配置
      branchOptions: [],
      // 用户信息
      userInfo: null,
      statsData: {
        annualDeclarationAmount: 0,
        averageSalaryTaxConsultant: 0,
        applicantCount: 0,
        averageSalary: 0,
        socialInsuranceRatio: 0,
        avgOutput: 0,
        salaryExpenseRatio: 0,
        averageSalaryOver6Months: 0,
        commissionRatio: 0,
        averageSalaryUnder6Months: 0,
        salaryRatioUnder6Months: 0,
        averageProductivity: 0,
        salaryCostRatio: 0
      },
      pieChart: null,
      barChart: null,
      lineChart: null,
      trendChart: null,
      levelData: null,
      levelDisplayData: [],
      commissionTableData: [], // 销售提成表格数据
      departmentTableData: [], // 部门分布表格数据
      departmentFullData: [] // 部门完整数据，用于tooltip显示
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initUserInfo()
      this.initCharts()
      this.loadData()
    })

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    this.disposeCharts()
  },
  methods: {
    // 初始化用户信息和分司选项
    initUserInfo() {
      console.log('=== 开始初始化用户信息 ===')

      // 调试用户信息
      this.debugUserInfo()

      // 获取用户信息
      this.userInfo = this.$store.getters.userInfo || {}

      // 设置分司选项
      this.setBranchOptions()

      console.log('=== 用户信息初始化完成 ===')
    },

    // 设置分司选项
    setBranchOptions() {
      console.log('=== 开始设置分司选项 ===')

      const allBranches = [
        { label: '合肥', value: '合肥' },
        { label: '蚌埠', value: '蚌埠' },
        { label: '常州', value: '常州' },
        { label: '无锡', value: '无锡' },
        { label: '总部', value: '总部' }
      ]

      // 判断用户是否为管理员 (isAdmin = "1")
      const isAdmin = this.isUserAdmin()

      if (isAdmin) {
        // 管理员显示所有分司选项
        this.branchOptions = allBranches
        console.log('✅ 管理员用户，显示所有分司选项')
      } else {
        // 非管理员只显示当前用户所在分司
        const userBranch = this.getCurrentUserBranch()
        console.log('非管理员用户所在分司:', userBranch)

        if (userBranch && allBranches.find(b => b.value === userBranch)) {
          // 只显示用户所在分司
          this.branchOptions = [{ label: userBranch, value: userBranch }]
          this.type = userBranch
          console.log('✅ 非管理员用户，只显示所在分司:', userBranch)
        } else {
          // 如果无法确定用户分司，为安全起见，默认显示第一个分司
          this.branchOptions = [allBranches[0]]
          this.type = allBranches[0].value
          console.log('⚠️ 无法确定用户分司，默认显示:', allBranches[0].label)
        }
      }

      console.log('最终分司选项:', this.branchOptions.map(b => b.label))
      console.log('当前选中分司:', this.type)
      console.log('=== 分司选项设置完成 ===')
    },

    // 简化的管理员判断方法
    isUserAdmin() {
      const userInfo = this.userInfo || {}

      console.log('检查用户管理员权限...')
      console.log('用户信息中的isAdmin字段:', userInfo.isAdmin)
      console.log('isAdmin字段类型:', typeof userInfo.isAdmin)

      // 根据后端返回数据，isAdmin = "1" 为管理员，其他为非管理员
      if (userInfo.isAdmin === "1") {
        console.log('✅ 用户是管理员 (isAdmin = "1")')
        return true
      }

      console.log('❌ 用户不是管理员 (isAdmin ≠ "1")')
      return false
    },

    // 简化的获取用户分司方法
    getCurrentUserBranch() {
      const userInfo = this.userInfo || {}

      // 检查各种可能的分司字段
      const branchFields = [
        userInfo.type, userInfo.branch, userInfo.branchName,
        userInfo.department, userInfo.office, userInfo.company,
        userInfo.orgName, userInfo.deptName
      ]

      const validBranches = ['合肥', '蚌埠', '常州', '无锡', '总部']

      for (let branchField of branchFields) {
        if (branchField && validBranches.includes(branchField)) {
          console.log('找到用户分司:', branchField)
          return branchField
        }
      }

      console.log('未找到有效的用户分司信息')
      return null
    },

    // 调试方法：打印用户信息结构
    debugUserInfo() {
      console.log('=== 用户信息调试 ===')

      const storeUserInfo = this.$store.getters.userInfo
      console.log('Store userInfo:', storeUserInfo)

      // 重点检查isAdmin字段
      if (storeUserInfo) {
        console.log('🔍 关键字段检查:')
        console.log('  isAdmin:', storeUserInfo.isAdmin, '(类型:', typeof storeUserInfo.isAdmin, ')')
        console.log('  type:', storeUserInfo.type)
        console.log('  branch:', storeUserInfo.branch)
        console.log('  name:', storeUserInfo.name)
        console.log('  role:', storeUserInfo.role)

        // 打印所有字段
        console.log('📋 所有用户信息字段:')
        Object.keys(storeUserInfo).forEach(key => {
          console.log(`  ${key}:`, storeUserInfo[key])
        })
      } else {
        console.log('❌ 未找到用户信息')
      }

      console.log('=== 调试结束 ===')
    },

    loadData() {
      if (this.loading) return

      this.loading = true
      this.chartLoading = true

      try {
        const params = {
          years: this.years,
          type: this.type
        }

        // 获取薪资分析数据
        getSalaryAnalysisData(params).then(res => {
          console.log('薪资分析API返回:', res)
          if (res && res.code == 200) {
            this.statsData = {
              annualDeclarationAmount: res.data.annualDeclarationAmount || 0,
              applicantCount: res.data.applicantCount || 0,
              averageSalary: res.data.averageSalary || 0,
              socialInsuranceRatio: res.data.socialInsuranceRatio || 0,
              avgOutput: res.data.avgOutput || 0,
              salaryExpenseRatio: res.data.salaryExpenseRatio || 0,
              averageSalaryOver6Months: res.data.averageSalaryOver6Months || 0,
              commissionRatio: res.data.commissionRatio || 0,
              averageSalaryUnder6Months: res.data.averageSalaryUnder6Months || 0,
              salaryRatioUnder6Months: res.data.salaryRatioUnder6Months || 0,
              averageSalaryTaxConsultant:res.data.averageSalaryTaxConsultant || 0,
              averageProductivity:res.data.averageProductivity || 0,
              salaryCostRatio:res.data.salaryCostRatio || 0
            }

            // 调用层级显示方法，传入层级数据
            this.updateLevelDisplay(res.data.levelStatistics) // 或者 res.data，根据实际API返回结构

            console.log('赋值后的statsData:', this.statsData)
          }
          this.loading = false
        }).catch(error => {
          console.error('薪资分析数据获取失败:', error)
          this.loading = false
        })

        // 获取部门数据
        getDepartmentData(params).then(res => {
          console.log('部门数据API返回:', res)
          if (res && res.code == 200) {
            this.updatePieChart(res.data.distribution)
            this.updateBarChart(res.data.distribution)

          }
          this.chartLoading = false
        }).catch(error => {
          console.error('部门数据获取失败:', error)
          this.chartLoading = false
        })

        // 获取销售数据
        getSalesData(params).then(res => {
          console.log('销售数据API返回:', res)
          if (res && res.code == 200) {
            this.updateLineChart(res.data.commissionDistribution)
          }
        }).catch(error => {
          console.error('销售数据获取失败:', error)
        })

        // 更新趋势图
        this.updateTrendChart()

      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('数据加载失败，请重试')
        this.loading = false
        this.chartLoading = false
      }
    },

    initCharts() {
      console.log('initCharts 被调用')
      console.log('DOM refs:', {
        pieChart: this.$refs.pieChart,
        barChart: this.$refs.barChart,
        lineChart: this.$refs.lineChart,
        trendChart: this.$refs.trendChart
      })

      if (!this.$refs.pieChart || !this.$refs.barChart || !this.$refs.lineChart || !this.$refs.trendChart) {
        console.log('某些 DOM ref 不存在，退出初始化')
        return
      }

      this.pieChart = echarts.init(this.$refs.pieChart)
      this.barChart = echarts.init(this.$refs.barChart)
      this.lineChart = echarts.init(this.$refs.lineChart)

      console.log('趋势图表DOM元素:', this.$refs.trendChart)
      if (this.$refs.trendChart) {
        this.trendChart = echarts.init(this.$refs.trendChart)
        console.log('趋势图表实例创建成功:', this.trendChart)
      } else {
        console.error('趋势图表DOM元素未找到')
      }

      console.log('图表实例创建完成:', {
        pieChart: this.pieChart,
        barChart: this.barChart,
        lineChart: this.lineChart,
        trendChart: this.trendChart
      })

      // 初始化饼图
      this.pieChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: { fontSize: 12 }
        },
        series: [{
          name: '部门占比',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }]
      })

      // 初始化柱状图（仅展示，禁用交互）
      this.barChart.setOption({
        tooltip: {
          trigger: 'axis',
          show: false // 禁用提示框
        },
        legend: {
          data: ['总数', '人均工资'],
          selectedMode: false // 禁用图例点击
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            rotate: 45,
            fontSize: 10
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '总数',
            position: 'left'
          },
          {
            type: 'value',
            name: '人均工资',
            position: 'right'
          }
        ],
        series: [
          {
            name: '总数',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '人均工资',
            type: 'line',
            yAxisIndex: 1,
            data: [],
            itemStyle: {
              color: '#91cc75'
            }
          }
        ]
      })

      // 初始化折线图
      this.lineChart.setOption({
        title: {
          text: '销售提成（业绩）占比',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['浮动工资占比', '部门提成'],
          top: 30,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '80',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            fontSize: 12,
            interval: 0,
            rotate: 30
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '浮动工资占比',
            position: 'left',
            axisLabel: {
              formatter: '{value}%'
            }
          },
          {
            type: 'value',
            name: '部门提成',
            position: 'right',
            axisLabel: {
              formatter: function (value) {
                if (value >= 10000) {
                  return (value / 10000).toFixed(1) + '万'
                }
                return value
              }
            }
          }
        ],
        series: [
          {
            name: '浮动工资占比',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#409EFF' // 改为绿色，更容易识别
            }
          },
          {
            name: '部门提成',
            type: 'line',
            yAxisIndex: 1,
            data: [],
            itemStyle: {
              color: '#fac858'
            }
          }
        ]
      })

      // 初始化趋势图
      this.trendChart.setOption({
        title: {
          text: `${this.years.split('-')[0]}年度薪资业务趋势`,
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function (params) {
            let result = `${params[0].name}<br/>`
            params.forEach(param => {
              if (param.seriesName === '月实发人数') {
                result += `${param.seriesName}: ${param.value}<br/>`
              } else {
                result += `${param.seriesName}: ${param.value.toLocaleString()}<br/>`
              }
            })
            return result
          }
        },
        legend: {
          data: ['申报总额', '月实发人数'],
          top: 30,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '80',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false // 去除X轴虚线
          },
          axisLabel: {
            fontSize: 12
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '申报总额',
            position: 'left',
            min: 0,
            interval: 50000, // 设置间隔为50000
            axisLine: {
              show: true,
              lineStyle: {
                color: '#5470c6'
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true, // 显示Y轴对应的水平虚线
              lineStyle: {
                color: '#e8e8e8',
                type: 'dashed',
                width: 1
              }
            },
            axisLabel: {
              formatter: function (value) {
                return value.toLocaleString()
              },
              color: '#5470c6'
            }
          },
          {
            type: 'value',
            name: '月实发人数',
            position: 'right',
            min: 0,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#fac858'
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false // 去除Y轴虚线
            },
            axisLabel: {
              formatter: '{value}',
              color: '#fac858'
            }
          }
        ],
        series: [
          {
            name: '申报总额',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#5470c6'
            },
            label: {
              show: true,
              position: 'top',
              formatter: function (params) {
                if (params.value === 0) return ''
                return params.value.toLocaleString()
              },
              fontSize: 11,
              color: '#5470c6', // 与柱状图颜色保持一致
              offset: [0, -5] // 向上偏移5像素，避免与折线图标签重叠
            },
            barWidth: '40%'
          },
          {
            name: '月实发人数',
            type: 'line',
            yAxisIndex: 1,
            data: [],
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              color: '#fac858'
            },
            itemStyle: {
              color: '#fac858',
              borderColor: '#fac858',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}',
              fontSize: 11,
              color: '#333'
            }
          }
        ]
      })
    },

    updatePieChart(data) {
      if (!this.pieChart || !data) return

      // 计算总人数用于计算百分比
      const totalEmployees = data.reduce((sum, item) => sum + item.employeeCount, 0)

      // 处理数据并计算百分比
      const pieData = data.map(item => ({
        name: item.departmentName,
        value: item.employeeCount,
        totalSalary: item.totalSalary,
        averageSalary: item.averageSalary,
        percentage: Math.round((item.employeeCount / totalEmployees) * 100)
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `${params.data.name}<br/>人数: ${params.data.value}人<br/>占比: ${Math.round(params.percent)}%`
          }
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'center',
          textStyle: { fontSize: 12 },
          formatter: function (name) {
            // 根据部门名称找到对应的百分比数据
            const item = pieData.find(d => d.name === name)
            return item ? `${name} ${item.percentage}%` : name
          }
        },
        series: [{
          name: '部门占比',
          type: 'pie',
          radius: ['15%', '65%'],
          center: ['40%', '55%'], // 向左移动，为右侧图例留出空间
          avoidLabelOverlap: true,
          label: {
            show: true, // 显示饼图上的标签注释
            formatter: function (params) {
              return `${params.data.name}\n${params.data.value}人\n${Math.round(params.percent)}%`
            },
            fontSize: 11,
            color: '#333',
            position: 'outside'
          },
          labelLine: {
            show: true, // 显示标签线
            length: 10,
            length2: 8
          },
          emphasis: {
            label: {
              show: true,
              formatter: function (params) {
                return `${params.data.name}\n${params.data.value}人\n${Math.round(params.percent)}%`
              },
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          data: pieData
        }]
      }
      this.pieChart.setOption(option, true)
    },

    updateBarChart(data) {
      console.log('updateBarChart 被调用，数据:', data)
      console.log('barChart 实例:', this.barChart)

      if (!this.barChart || !data) {
        console.log('barChart 或 data 为空，退出')
        return
      }

      // 处理数据
      const departments = data.map(item => item.departmentName);
      const totalSalaries = data.map(item => item.totalSalary);
      const avgSalaries = data.map(item => item.averageSalary);

      // 保存完整数据用于tooltip显示
      this.departmentFullData = data;

      // 设置表格数据
      this.departmentTableData = data

      console.log('处理后的数据:', { departments, totalSalaries, avgSalaries })

      // 创建表格数据（用于显示在图表下方）
      const tableData = data.map(item => ({
        department: item.departmentName,
        totalSalary: item.totalSalary.toFixed(2),
        avgSalary: item.averageSalary.toFixed(0)
      }));

      const option = {
        title: {
          text: '部门分布',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: (params) => {
            const departmentName = params[0].name

            // 从完整数据中找到对应部门的详细信息
            const departmentInfo = this.departmentFullData.find(item => item.departmentName === departmentName)

            let result = `<div style="font-weight: bold; margin-bottom: 8px; font-size: 14px; color: #333; border-bottom: 1px solid #eee; padding-bottom: 6px;">${departmentName}</div>`

            // 如果找到了部门详细信息，显示员工数量
            if (departmentInfo) {
              result += `<div style="margin: 6px 0; color: #666; font-size: 11px;">
                <span style="display: inline-block; width: 8px; height: 8px; background-color: #909399; border-radius: 50%; margin-right: 6px;"></span>
                员工数量: <span style="font-weight: bold; color: #333;">${departmentInfo.employeeCount}人</span>
              </div>`
            }

            params.forEach(param => {
              const colorDot = `<span style="display: inline-block; width: 8px; height: 8px; background-color: ${param.color}; border-radius: 50%; margin-right: 6px;"></span>`

              if (param.seriesName === '金额') {
                const value = param.value >= 10000 ?
                  `${(param.value / 10000).toFixed(1)}万元` :
                  `${param.value.toLocaleString()}元`
                result += `<div style="margin: 6px 0; display: flex; align-items: center;">
                  ${colorDot}
                  <span style="color: #666;">总薪资: </span>
                  <span style="font-weight: bold; color: #333; margin-left: 4px;">${value}</span>
                </div>`
              } else if (param.seriesName === '人均工资') {
                const value = param.value >= 10000 ?
                  `${(param.value / 10000).toFixed(1)}万元` :
                  `${param.value.toLocaleString()}元`
                result += `<div style="margin: 6px 0; display: flex; align-items: center;">
                  ${colorDot}
                  <span style="color: #666;">人均工资: </span>
                  <span style="font-weight: bold; color: #333; margin-left: 4px;">${value}</span>
                </div>`
              }
            })
            return result
          },
          backgroundColor: 'rgba(255, 255, 255, 0.98)',
          borderColor: '#e8e8e8',
          borderWidth: 1,
          borderRadius: 6,
          padding: [10, 12],
          textStyle: {
            color: '#333',
            fontSize: 12,
            lineHeight: 20
          },
          extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); backdrop-filter: blur(10px);'
        },
        legend: {
          data: ['金额', '人均工资'],
          top: 30,
          selectedMode: 'multiple' // 启用图例点击交互，支持多选
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '70',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: departments,
          axisLabel: {
            interval: 0,
            rotate: 30,
            fontSize: 12
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '金额',
            position: 'left',
            min: 0,
            max: function (value) {
              // 固定最大值，向上取整到最近的100000倍数
              const maxVal = Math.ceil(value.max / 100000) * 100000
              return Math.max(maxVal, 500000) // 最小显示到500000
            },
            interval: 100000, // 设置Y轴间隔为100000
            axisLine: {
              show: true,
              lineStyle: {
                color: '#5470c6'
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true, // 显示网格线
              lineStyle: {
                color: '#e8e8e8',
                type: 'dashed',
                width: 1
              }
            },
            axisLabel: {
              formatter: function (value) {
                if (value >= 10000) {
                  return (value / 10000) + '万';
                }
                return value;
              },
              color: '#5470c6'
            }
          },
          {
            type: 'value',
            name: '人均工资',
            position: 'right',
            min: 0,
            max: function (value) {
              // 固定最大值，向上取整到最近的5000倍数
              const maxVal = Math.ceil(value.max / 5000) * 5000
              return Math.max(maxVal, 20000) // 最小显示到20000
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#91cc75'
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false // 右侧Y轴不显示网格线，只保留左侧的一条线
            },
            axisLabel: {
              color: '#91cc75'
            }
          }
        ],
        series: [
          {
            name: '金额',
            type: 'bar',
            data: totalSalaries,
            barWidth: '40%',
            itemStyle: {
              color: '#5470c6'
            },
            label: {
              show: true,
              position: 'top',
              formatter: function (params) {
                return params.value.toFixed(2);
              }
            }
          },
          {
            name: '人均工资',
            type: 'line',
            yAxisIndex: 1,
            data: avgSalaries,
            symbol: 'triangle',
            symbolSize: 10,
            lineStyle: {
              width: 2,
              color: '#fac858'
            },
            itemStyle: {
              color: '#fac858'
            },
            label: {
              show: true,
              position: 'top',
              formatter: function (params) {
                return params.value.toFixed(0);
              }
            }
          }
        ],
        // 添加表格数据展示
        dataset: {
          source: [
            ['部门', '金额', '人均工资'],
            ...tableData.map(item => [item.department, item.totalSalary, item.avgSalary])
          ]
        }
        // 移除 dataZoom 配置，禁用鼠标滚动缩放功能
      };

      console.log('设置 barChart option:', option)
      this.barChart.setOption(option, true);
      console.log('barChart setOption 完成')

      // 添加图例点击事件监听器
      this.barChart.off('legendselectchanged') // 先移除之前的监听器
      this.barChart.on('legendselectchanged', (params) => {
        console.log('图例点击事件:', params)
        this.handleBarChartLegendClick(params)
      })

      // 强制重新渲染
      this.$nextTick(() => {
        if (this.barChart) {
          this.barChart.resize()
          console.log('barChart resize 完成')
        }
      })

      // 如果需要在图表下方添加表格，可以通过DOM操作添加
      // 或者使用ECharts的dataset功能
    },

    // 处理部门分布图图例点击事件
    handleBarChartLegendClick(params) {
      console.log('处理图例点击:', params)

      // params.selected 包含所有图例的选中状态
      // 例如: { '金额': true, '人均工资': false }
      const selected = params.selected

      // 动态调整Y轴显示
      const showAmount = selected['金额'] !== false
      const showAvgSalary = selected['人均工资'] !== false

      // 构建新的配置选项
      const updateOption = {
        yAxis: [
          {
            // 左侧Y轴（金额）
            show: showAmount,
            axisLine: {
              show: showAmount,
              lineStyle: {
                color: '#5470c6'
              }
            },
            axisLabel: {
              show: showAmount,
              formatter: function (value) {
                if (value >= 10000) {
                  return (value / 10000) + '万';
                }
                return value;
              },
              color: '#5470c6'
            },
            splitLine: {
              show: showAmount, // 只有在显示金额时才显示网格线
              lineStyle: {
                color: '#e8e8e8',
                type: 'dashed',
                width: 1
              }
            }
          },
          {
            // 右侧Y轴（人均工资）
            show: showAvgSalary,
            axisLine: {
              show: showAvgSalary,
              lineStyle: {
                color: '#91cc75'
              }
            },
            axisLabel: {
              show: showAvgSalary,
              color: '#91cc75'
            },
            splitLine: {
              show: false // 右侧Y轴始终不显示网格线
            }
          }
        ]
      }

      // 应用新的配置
      this.barChart.setOption(updateOption, false) // 使用 merge 模式

      console.log('图例筛选完成，当前显示:', selected)

      // 提供用户反馈
      // const visibleItems = []
      // if (showAmount) visibleItems.push('金额')
      // if (showAvgSalary) visibleItems.push('人均工资')

      // if (visibleItems.length === 0) {
      //   this.$message.info('请至少选择一个数据系列进行显示')
      // } else {
      //   this.$message.success(`当前显示: ${visibleItems.join('、')}`)
      // }
    },

    updateLineChart(data) {
      console.log('updateLineChart 被调用，数据:', data)

      if (!this.lineChart || !data) {
        console.log('lineChart 或 data 为空，退出')
        return
      }

      // 处理数据
      const departments = data.map(item => item.departmentName)
      const commissionSums = data.map(item => item.commissionSum)
      const commissionRatios = data.map(item => (item.commissionRatio * 100).toFixed(2)) // 转换为百分比

      // 设置表格数据
      this.commissionTableData = data

      console.log('处理后的数据:', { departments, commissionSums, commissionRatios })

      // 生成表格图片
      this.$nextTick(() => {
        this.generateCommissionTableImage(data)
      })

      const option = {
        title: {
          text: '销售提成（业绩）占比',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function (params) {
            let result = `${params[0].name}<br/>`
            params.forEach(param => {
              if (param.seriesName === '浮动工资占比') {
                result += `${param.seriesName}: ${param.value}%<br/>`
              } else {
                result += `${param.seriesName}: ${param.value}<br/>`
              }
            })
            return result
          }
        },
        legend: {
          data: ['浮动工资占比', '部门提成'],
          top: 30,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '8%',
          right: '8%',
          bottom: '20%',
          top: '100',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: departments,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            interval: 0,
            rotate: 30,
            fontSize: 12
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '浮动工资占比',
            position: 'left',
            min: 0,
            max: 70, // 设置固定最大值为70%，增加显示空间
            interval: 10, // 设置间隔为10%，增加刻度密度
            axisLine: {
              lineStyle: {
                color: '#5470c6' // 柱状图颜色
              }
            },
            axisLabel: {
              formatter: '{value}%',
              color: '#5470c6' // 柱状图颜色
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#f0f0f0'
              }
            }
          },
          {
            type: 'value',
            name: '部门提成',
            position: 'right',
            axisLine: {
              lineStyle: {
                color: '#91cc75'
              }
            },
            axisLabel: {
              formatter: function (value) {
                if (value >= 10000) {
                  return (value / 10000).toFixed(1) + '万'
                }
                return value
              },
              color: '#91cc75'
            }
          }
        ],
        series: [
          {
            name: '浮动工资占比',
            type: 'bar',
            data: commissionRatios,
            itemStyle: {
              color: '#5470c6' // 改为与部门分布图一致的颜色
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%',
              fontSize: 11,
              color: '#5470c6', // 与柱状图颜色保持一致
              offset: [0, -5] // 向上偏移5像素，避免与折线图标签重叠
            },
            barWidth: '40%'
          },
          {
            name: '部门提成',
            type: 'line',
            yAxisIndex: 1,
            data: commissionSums,
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              color: '#fac858'
            },
            itemStyle: {
              color: '#fac858',
              borderColor: '#fac858',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'top',
              formatter: function (params) {
                if (params.value === 0) return ''
                return params.value.toLocaleString() // 显示具体数字，使用千分位分隔符
              },
              fontSize: 11,
              color: '#fac858', // 与折线图颜色保持一致
              offset: [0, -15] // 向上偏移15像素，避免与柱状图标签重叠
            }
          }
        ]
      }

      console.log('设置 lineChart option:', option)
      this.lineChart.setOption(option, true)
      console.log('lineChart setOption 完成')

      // 强制重新渲染
      this.$nextTick(() => {
        if (this.lineChart) {
          this.lineChart.resize()
          console.log('lineChart resize 完成')
        }
      })
    },

    updateTrendChart(data) {
      console.log('updateTrendChart 被调用，数据:', data)


      if (!this.trendChart) {
        console.log('trendChart 为空，退出')
        return
      }

      // 调用后端接口获取趋势数据
      const params = {
        years: this.years,
        type: this.type
      }

      getTrendData(params).then(res => {
        console.log('趋势数据API返回:', res)
        if (res && res.code == 200 && res.data) {
          this.renderTrendChart(res.data.monthlyStatistics)
        } else {
          console.error('趋势数据获取失败或数据为空')
        }
      }).catch(error => {
        console.error('趋势数据获取失败:', error)
      })
    },

    // 生成销售提成表格图片
    generateCommissionTableImage(data) {
      console.log('生成销售提成表格图片，数据:', data)

      if (!this.$refs.commissionCanvas || !data || data.length === 0) {
        console.log('Canvas元素不存在或数据为空')
        return
      }

      const canvas = this.$refs.commissionCanvas
      const ctx = canvas.getContext('2d')

      // 设置高DPI支持
      const dpr = window.devicePixelRatio || 1
      const rect = canvas.parentElement.getBoundingClientRect()
      const containerWidth = rect.width || 800
      const canvasWidth = Math.max(containerWidth, 400) // 确保最小宽度
      const canvasHeight = 120

      // 设置canvas实际尺寸
      canvas.width = canvasWidth * dpr
      canvas.height = canvasHeight * dpr
      canvas.style.width = canvasWidth + 'px'
      canvas.style.height = canvasHeight + 'px'

      // 缩放上下文以匹配设备像素比
      ctx.scale(dpr, dpr)

      // 清空画布
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, canvasWidth, canvasHeight)

      // 计算列宽和行高
      const colWidth = canvasWidth / data.length
      const rowHeight = 40

      // 设置字体和对齐
      ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'

      // 绘制表头背景
      ctx.fillStyle = '#f5f5f5'
      ctx.fillRect(0, 0, canvasWidth, rowHeight)

      // 设置边框样式
      ctx.strokeStyle = '#e8e8e8'
      ctx.lineWidth = 1

      // 绘制表头
      ctx.fillStyle = '#333333'
      data.forEach((item, index) => {
        const x = index * colWidth

        // 绘制表头边框
        ctx.strokeRect(x, 0, colWidth, rowHeight)

        // 绘制表头文字
        ctx.fillText(item.departmentName, x + colWidth / 2, rowHeight / 2)
      })

      // 绘制第一行数据（提成占比）
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, rowHeight, canvasWidth, rowHeight)

      ctx.fillStyle = '#1890ff' // 蓝色
      ctx.font = 'bold 14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'

      data.forEach((item, index) => {
        const x = index * colWidth

        // 绘制边框
        ctx.strokeRect(x, rowHeight, colWidth, rowHeight)

        // 绘制提成占比
        const ratio = (item.commissionRatio * 100).toFixed(2) + '%'
        ctx.fillText(ratio, x + colWidth / 2, rowHeight + rowHeight / 2)
      })

      // 绘制第二行数据（提成金额）
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, rowHeight * 2, canvasWidth, rowHeight)

      ctx.fillStyle = '#333333' // 深色
      ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'

      data.forEach((item, index) => {
        const x = index * colWidth

        // 绘制边框
        ctx.strokeRect(x, rowHeight * 2, colWidth, rowHeight)

        // 绘制提成金额
        const sum = this.formatCommissionSum(item.commissionSum)
        ctx.fillText(sum, x + colWidth / 2, rowHeight * 2 + rowHeight / 2)
      })

      console.log('表格图片生成完成')
    },

    // 生成层级占比统计图片
    generateLevelStatsImage(data) {
      console.log('生成层级占比统计图片，数据:', data)

      if (!this.$refs.levelStatsCanvas || !data || data.length === 0) {
        console.log('Canvas元素不存在或数据为空')
        return
      }

      const canvas = this.$refs.levelStatsCanvas
      const ctx = canvas.getContext('2d')

      // 设置高DPI支持
      const dpr = window.devicePixelRatio || 1

      // 获取准确的容器尺寸
      const container = canvas.parentElement
      const containerStyle = window.getComputedStyle(container)
      const containerWidth = container.offsetWidth - parseFloat(containerStyle.paddingLeft) - parseFloat(containerStyle.paddingRight)
      const canvasWidth = Math.max(containerWidth, 600) // 确保足够宽度显示所有内容
      const canvasHeight = 280

      // 设置canvas实际尺寸
      canvas.width = canvasWidth * dpr
      canvas.height = canvasHeight * dpr
      canvas.style.width = canvasWidth + 'px'
      canvas.style.height = canvasHeight + 'px'

      // 缩放上下文以匹配设备像素比
      ctx.scale(dpr, dpr)

      // 清空画布
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, canvasWidth, canvasHeight)

      // 设置基本参数，现在canvas只占左侧区域，右侧有独立的表格
      const leftMargin = 60 // 左边距用于层级名称
      const rightMargin = 20 // 右边距，因为右侧表格已经独立显示
      const topMargin = 30
      const bottomMargin = 60
      const chartWidth = canvasWidth - leftMargin - rightMargin
      const chartHeight = canvasHeight - topMargin - bottomMargin
      const rowHeight = (chartHeight - 30) / data.length
      const barHeight = 22
      const levelSpacing = 18

      // 设置字体
      ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      ctx.textAlign = 'left'
      ctx.textBaseline = 'middle'

      // 绘制每个层级的数据
      data.forEach((level, index) => {
        const y = topMargin + index * (rowHeight + levelSpacing) + (rowHeight - barHeight * 2) / 2

        // 绘制层级名称（缩短单位）
        ctx.fillStyle = '#333333'
        ctx.font = 'bold 13px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
        ctx.textAlign = 'right'
        const shortName = level.name.replace('占比', '').replace('层级', '层')
        ctx.fillText(shortName, leftMargin - 10, y + barHeight)

        // 绘制人数占比条形图
        const personBarWidth = (parseFloat(level.countPercent) / 100) * chartWidth
        ctx.fillStyle = '#ff6b35' // 橙色
        ctx.fillRect(leftMargin, y, personBarWidth, barHeight)

        // 绘制工资占比条形图
        const salaryBarWidth = (parseFloat(level.salaryPercent) / 100) * chartWidth
        ctx.fillStyle = '#4ecdc4' // 青色
        ctx.fillRect(leftMargin, y + barHeight + 5, salaryBarWidth, barHeight)

        // 绘制百分比文字在条形图后面（右侧），确保在画布范围内
        ctx.fillStyle = '#333333'
        ctx.font = 'bold 12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
        ctx.textAlign = 'left'

        // 计算百分比文字位置，确保在右边距范围内
        const maxTextX = canvasWidth - rightMargin - 10 // 在右边距内留出10px空间给文字
        const personTextX = Math.min(leftMargin + personBarWidth + 10, maxTextX)
        const salaryTextX = Math.min(leftMargin + salaryBarWidth + 10, maxTextX)

        // 人数占比百分比
        ctx.fillText(level.countPercent + '%', personTextX, y + barHeight / 2)

        // 工资占比百分比
        ctx.fillText(level.salaryPercent + '%', salaryTextX, y + barHeight + 5 + barHeight / 2)

        // 不绘制边框（去除虚线）
      })

      // 绘制图例在图的最后（底部中央）
      const legendY = canvasHeight - 30
      const legendItemWidth = 120
      const legendStartX = (canvasWidth - legendItemWidth * 2) / 2

      // 人数占比图例
      ctx.fillStyle = '#ff6b35'
      ctx.fillRect(legendStartX, legendY, 15, 15)
      ctx.fillStyle = '#333333'
      ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      ctx.textAlign = 'left'
      ctx.fillText('人数占比', legendStartX + 20, legendY + 12)

      // 工资占比图例
      ctx.fillStyle = '#4ecdc4'
      ctx.fillRect(legendStartX + legendItemWidth, legendY, 15, 15)
      ctx.fillStyle = '#333333'
      ctx.fillText('工资占比', legendStartX + legendItemWidth + 20, legendY + 12)

      console.log('层级统计图片生成完成')
    },

    renderTrendChart(data) {
      console.log('renderTrendChart 被调用，数据:', data)

      // 处理数据 - 根据后端返回的数据格式
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']

      // 初始化12个月的数据数组
      const monthlyIncome = new Array(12).fill(0)
      const accountOpenings = new Array(12).fill(0)

      // 如果data是数组格式（后端返回的格式）
      if (Array.isArray(data)) {
        data.forEach(item => {
          // 从 "2025-01" 格式中提取月份索引 (0-11)
          const monthIndex = parseInt(item.month.split('-')[1]) - 1
          if (monthIndex >= 0 && monthIndex < 12) {
            monthlyIncome[monthIndex] = item.totalAmount || 0
            accountOpenings[monthIndex] = item.totalCount || 0
          }
        })
      } else if (data && typeof data === 'object') {
        // 兼容旧格式（如果data是对象格式）
        const incomeData = data.monthlyIncome || []
        const openingData = data.accountOpenings || []
        for (let i = 0; i < 12; i++) {
          monthlyIncome[i] = incomeData[i] || 0
          accountOpenings[i] = openingData[i] || 0
        }
      }

      console.log('处理后的月收入数据:', monthlyIncome)
      console.log('处理后的开户数数据:', accountOpenings)

      const option = {
        title: {
          text: `${this.years.split('-')[0]}年度薪资成本趋势`,
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function (params) {
            let result = `${params[0].name}<br/>`
            params.forEach(param => {
              if (param.seriesName === '月实发人数') {
                result += `${param.seriesName}: ${param.value}<br/>`
              } else {
                result += `${param.seriesName}: ${param.value.toLocaleString()}<br/>`
              }
            })
            return result
          }
        },
        legend: {
          data: ['申报总额', '月实发人数'],
          top: 30,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '8%',
          right: '8%',
          bottom: '15%',
          top: '80',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: months,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false // 去除X轴虚线
          },
          axisLabel: {
            fontSize: 12
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '申报总额',
            position: 'left',
            min: 0,
            max: function (value) {
              // 动态设置最大值，向上取整到最近的50000倍数
              const maxVal = Math.ceil(value.max / 50000) * 50000
              return Math.max(maxVal, 500000) // 最小显示到500000
            },
            interval: 100000, // 增大Y轴申报总额间距到100000
            axisLine: {
              show: true,
              lineStyle: {
                color: '#5470c6'
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true, // 显示Y轴对应的水平虚线
              lineStyle: {
                color: '#e8e8e8',
                type: 'dashed',
                width: 1
              }
            },
            axisLabel: {
              formatter: function (value) {
                return value.toLocaleString()
              },
              color: '#5470c6'
            }
          },
          {
            type: 'value',
            name: '月实发人数',
            position: 'right',
            min: 0,
            max: function (value) {
              // 动态设置最大值
              return Math.ceil(value.max * 1.2)
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#67c23a' // 改为绿色
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false // 去除Y轴虚线
            },
            axisLabel: {
              formatter: '{value}',
              color: '#67c23a' // 改为绿色
            }
          }
        ],
        series: [
          {
            name: '申报总额',
            type: 'bar',
            data: monthlyIncome,
            itemStyle: {
              color: '#5470c6'
            },
            label: {
              show: true,
              position: 'top',
              formatter: function (params) {
                if (params.value === 0) return ''
                return params.value.toLocaleString()
              },
              fontSize: 14, // 增大数字显示字体
              color: '#5470c6', // 与柱状图颜色保持一致
              offset: [0, -5] // 向上偏移5像素，避免与折线图标签重叠
            },
            barWidth: '40%'
          },
          {
            name: '月实发人数',
            type: 'line',
            yAxisIndex: 1,
            data: accountOpenings,
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              color: '#67c23a' // 改为绿色
            },
            itemStyle: {
              color: '#67c23a', // 改为绿色
              borderColor: '#67c23a', // 改为绿色
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'top',
              formatter: function (params) {
                return params.value === 0 ? '' : params.value
              },
              fontSize: 14, // 增大数字显示字体
              color: '#67c23a', // 改为绿色
              offset: [0, -20] // 向上偏移20像素，避免与柱状图标签重叠
            }
          }
        ]
      }

      console.log('设置 trendChart option:', option)
      this.trendChart.setOption(option, true)
      console.log('trendChart setOption 完成')

      // 强制重新渲染
      this.$nextTick(() => {
        if (this.trendChart) {
          this.trendChart.resize()
          console.log('trendChart resize 完成')
        }
      })
    },

    handleResize() {
      this.$nextTick(() => {
        if (this.pieChart) this.pieChart.resize()
        if (this.barChart) this.barChart.resize()
        if (this.lineChart) this.lineChart.resize()
        if (this.trendChart) this.trendChart.resize()

        // 重新生成销售提成表格图片
        if (this.commissionTableData && this.commissionTableData.length > 0) {
          this.generateCommissionTableImage(this.commissionTableData)
        }

        // 重新生成层级统计图片
        if (this.levelDisplayData && this.levelDisplayData.length > 0) {
          this.generateLevelStatsImage(this.levelDisplayData)
        }
      })
    },

    disposeCharts() {
      if (this.pieChart) {
        this.pieChart.dispose()
        this.pieChart = null
      }
      if (this.barChart) {
        // 移除事件监听器
        this.barChart.off('legendselectchanged')
        this.barChart.dispose()
        this.barChart = null
      }
      if (this.lineChart) {
        this.lineChart.dispose()
        this.lineChart = null
      }
      if (this.trendChart) {
        this.trendChart.dispose()
        this.trendChart = null
      }
    },

    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0'
      }
      return Number(value).toLocaleString()
    },

    formatCommissionSum(value) {
      if (value === null || value === undefined || value === '' || value === 0) {
        return '0'
      }
      return Number(value).toLocaleString()
    },
    updateLevelDisplay(data) {
      if (!data) return

      this.levelData = data
      console.log('层级数据:', data) // 调试用

      // 处理显示数据
      const levels = [
        { key: 'seniorLevel', name: '高层占比' },
        { key: 'middleLevel', name: '中层占比' },
        { key: 'juniorLevel', name: '基层占比' }
      ]

      this.levelDisplayData = levels.map(level => {
        const levelInfo = data[level.key]
        if (!levelInfo) return null

        const countPercent = (levelInfo.countRatio * 100).toFixed(2)
        const salaryPercent = (levelInfo.salaryRatio * 100).toFixed(2)

        // 计算图标数量（基于百分比，调整显示效果）
        let personIcons, salaryIcons

        if (levelInfo.countRatio < 0.05) { // 小于5%
          personIcons = Math.max(1, Math.round(levelInfo.countRatio * 100)) // 每1%一个图标
        } else if (levelInfo.countRatio < 0.20) { // 5%-20%
          personIcons = Math.max(2, Math.round(levelInfo.countRatio * 50)) // 每2%一个图标
        } else { // 大于20%
          personIcons = Math.max(10, Math.round(levelInfo.countRatio * 100)) // 每1%一个图标，但最少10个
        }

        if (levelInfo.salaryRatio < 0.05) { // 小于5%
          salaryIcons = Math.max(1, Math.round(levelInfo.salaryRatio * 100))
        } else if (levelInfo.salaryRatio < 0.20) { // 5%-20%
          salaryIcons = Math.max(2, Math.round(levelInfo.salaryRatio * 50))
        } else { // 大于20%
          salaryIcons = Math.max(10, Math.round(levelInfo.salaryRatio * 100))
        }

        console.log(`${level.name}: 人数图标${personIcons}个, 工资图标${salaryIcons}个`) // 调试用

        return {
          name: level.name,
          countPercent,
          salaryPercent,
          personIcons,
          salaryIcons
        }
      }).filter(item => item !== null)

      console.log('处理后的显示数据:', this.levelDisplayData) // 调试用

      // 生成层级统计图片
      this.$nextTick(() => {
        this.generateLevelStatsImage(this.levelDisplayData)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.salary-analysis {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #333;
      font-size: 24px;
    }

    .filters {
      display: flex;
      gap: 15px;

      .el-select {
        width: 120px;
      }
    }
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;

    .card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        line-height: 1.2;

        .unit {
          font-size: 14px;
          color: #999;
          font-weight: normal;
          margin-left: 4px;
        }
      }

      // 不同颜色主题的卡片样式
      &.card-primary {
        border-left: 4px solid #409EFF;

        .label {
          color: #409EFF;
        }

        .value {
          color: #409EFF;
        }

        &:hover {
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }

      &.card-success {
        border-left: 4px solid #67C23A;

        .label {
          color: #67C23A;
        }

        .value {
          color: #67C23A;
        }

        &:hover {
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
        }
      }

      &.card-warning {
        border-left: 4px solid #E6A23C;

        .label {
          color: #E6A23C;
        }

        .value {
          color: #E6A23C;
        }

        &:hover {
          box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
        }
      }

      &.card-danger {
        border-left: 4px solid #F56C6C;

        .label {
          color: #F56C6C;
        }

        .value {
          color: #F56C6C;
        }

        &:hover {
          box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
        }
      }

      &.card-info {
        border-left: 4px solid #909399;

        .label {
          color: #909399;
        }

        .value {
          color: #909399;
        }

        &:hover {
          box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
        }
      }

      &.card-purple {
        border-left: 4px solid #8B5CF6;

        .label {
          color: #8B5CF6;
        }

        .value {
          color: #8B5CF6;
        }

        &:hover {
          box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }
      }
    }
  }

  // 新的三排布局样式
  .charts-row {
    display: flex;
    gap: 1.5vw;
    margin-bottom: 2vw;

    &.first-row {
      // 第一排：饼图和层级统计
      .chart-item {
        &.pie-chart-item {
          flex: 0 0 45%; // 饼图占45%
        }

        &.level-stats-item {
          flex: 0 0 55%; // 层级统计占55%
        }
      }
    }

    &.second-row {
      // 第二排：部门分布和销售提成
      .chart-item {
        flex: 1; // 平均分配
      }
    }

    &.third-row {
      // 第三排：年度薪资成本趋势
      .chart-item {
        width: 100%; // 占满整行
      }
    }

    // 响应式断点
    @media (max-width: 1024px) {
      flex-direction: column; // 小屏幕垂直布局
      gap: 1vw;

      &.first-row .chart-item {
        flex: 1 !important; // 小屏幕下都占满宽度
      }
    }

    @media (max-width: 768px) {
      gap: 2vw;
      margin-bottom: 3vw;
    }
  }

  // 保持原有的chart-item样式
  .chart-item {
    background: white;
    padding: 1vw; // 响应式内边距
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    h3 {
      margin: 0 0 8px 0; // 进一步缩小下边距
      color: #333;
      font-size: 15px; // 减小字体
      text-align: center;
    }

    // 响应式断点
    @media (max-width: 1440px) {
      padding: 0.8vw; // 缩小内边距
    }

    @media (max-width: 1024px) {
      padding: 1.5vw; // 缩小内边距
    }

    @media (max-width: 768px) {
      padding: 2vw; // 缩小内边距
    }

      .chart-wrapper {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .chart {
          flex: 0 0 60%; // 将饼图区域从flex: 1改为固定60%
          height: 350px; // 减小高度从350px到300px
          max-width: 400px; // 限制最大宽度
        }
      }

      // 为直接使用的 chart 元素添加样式
      >.chart {
        width: 100%;
        height: 400px;
        min-height: 300px;

        .level-stats {
          flex: 1; // 让层级统计区域占据剩余空间
          width: 100%; // 占据全部可用宽度
          max-width: none; // 移除最大宽度限制
          padding: 50px; // 增加内边距，给间距更多空间
          background: #f8f9fa;
          border-radius: 8px;
          margin-left: 0; // 移除左边距，让区域覆盖全面
          box-sizing: border-box; // 确保padding包含在总宽度内

          // 在level-stats区域内的dot显示为方块
          .person-icons .dot,
          .salary-icons .dot {
            border-radius: 0 !important; // 强制覆盖全局样式，显示为方块
          }

          .level-item {
            margin-bottom: 55px !important; // 强制增加层级之间的间距
            padding-bottom: 10px; // 额外增加内边距

            .level-content {
              display: flex;
              align-items: flex-start;

              .stats-section {
                flex: 1;

                .stat-line {
                  display: flex;
                  align-items: center;
                  margin-bottom: 5px;
                  min-height: 20px; // 确保每行有足够的高度

                  .level-name-inline {
                    width: 80px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #333;
                    text-align: left;
                    margin-right: 15px;
                    flex-shrink: 0;
                  }

                  .level-name-placeholder {
                    width: 80px;
                    margin-right: 15px;
                    flex-shrink: 0;
                  }

                  .person-icons,
                  .salary-icons {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    min-height: 16px;
                    flex: 1;

                    .dot {
                      width: 4px;
                      height: 4px;
                      margin: 0.5px;
                    }

                    .percentage {
                      font-size: 12px;
                      font-weight: bold;
                      margin-left: 8px; // 在小方块后面添加间距

                      &.person-percent {
                        color: #ff6b35; // 橙色，对应人数占比
                      }

                      &.salary-percent {
                        color: #4ecdc4; // 青色，对应工资占比
                      }
                    }
                  }
                }
              }
            }
          }

          .legend {
            margin-top: 15px; // 减小上边距
            padding-top: 12px; // 减小上内边距
            border-top: 1px solid #ddd;
            font-size: 11px; // 减小字体
            color: #666;
            text-align: center;

            .legend-item {
              margin: 0 12px; // 减小间距
              display: inline-flex;
              align-items: center;

              .legend-icon {
                margin-right: 4px; // 减小右边距
                font-size: 11px; // 减小字体
              }
            }
          }
        }
      }

      // 部门分布表格样式
      .department-table {
        margin-top: 10px;

        table {
          width: 100%;
          border-collapse: collapse;
          font-size: 15px;

          th,
          td {
            padding: 8px 12px;
            text-align: center;
            border: 1px solid #e8e8e8;
            background-color: #fafafa;
          }

          th {
            background-color: #f5f5f5;
            font-weight: 500;
            color: #333;
          }

          td {
            background-color: #fff;
            color: #666;
          }

          // 第一行（总额）样式
          tbody tr:first-child td {
            font-weight: 500;
            color: #1890ff;
          }

          // 第二行（人均工资）样式
          tbody tr:last-child td {
            color: #333;
          }
        }
      }
    }

    // 销售提成和趋势图表并排显示
    .commission-chart {
      grid-column: 1;

      .chart {
        height: 380px; // 销售提成图表高度
      }

      // 销售提成表格样式
      .commission-table {
        margin-top: 10px;

        table {
          width: 100%;
          border-collapse: collapse;
          font-size: 15px;

          th,
          td {
            padding: 8px 12px;
            text-align: center;
            border: 1px solid #e8e8e8;
            background-color: #fafafa;
          }

          th {
            background-color: #f5f5f5;
            font-weight: 500;
            color: #333;
          }

          td {
            background-color: #fff;
            color: #666;
          }

          // 第一行（百分比）样式
          tbody tr:first-child td {
            font-weight: 500;
            color: #1890ff;
          }

          // 第二行（金额）样式
          tbody tr:last-child td {
            color: #333;
          }
        }
      }

      // 销售提成表格图片样式
      .commission-table-image {
        margin-top: 10px;

        .commission-canvas {
          width: 100%;
          height: auto;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          background-color: #ffffff;
        }
      }

      // 层级统计图片样式
      .level-stats-image {
        margin-top: 10px;

        .level-stats-canvas {
          width: 100%;
          height: auto;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          background-color: #ffffff;
          display: block; // 确保Canvas正确显示
        }
      }




    }

    .trend-chart-item {
      grid-column: 2;

      .trend-chart {
        width: 100%;
        height: 380px; // 趋势图表高度
        min-height: 350px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .stats-cards {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 768px) {
    .salary-analysis {
      padding: 10px;

      .header {
        flex-direction: column;
        gap: 15px;

        .filters {
          width: 100%;
          justify-content: center;
        }
      }

      .stats-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;

        .card {
          padding: 15px;

          .value {
            font-size: 20px;
          }
        }
      }

      .charts-row {
        flex-direction: column;
        gap: 15px;

        .chart-item {
          padding: 15px;

          .chart {
            height: 300px;
          }
        }
      }
    }
  }


.dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 0; // 改为方块形状
  margin: 0.5px;

  &.person {
    background-color: #ff6b35; // 橙色，对应人数占比
  }

  &.salary {
    background-color: #4ecdc4; // 青色，对应工资占比
  }
}

// 专门为level-stats区域的dot设置方块样式
.level-stats .person-icons .dot,
.level-stats .salary-icons .dot {
  border-radius: 0 !important; // 方块形状
}
</style>
