/** When your routing table is too long, you can split it into small modules**/
import Layout from '@/layout'

const outbound = [{
    path: '/outboundHome',
    name: 'outboundHome',
    component: Layout,
    redirect: 'noRedirect',
    children: [{
      path: 'outboundHomeContent',
      name: 'outboundHomeContent',
      component: () => import('@/views/outbound/home/<USER>'),
      meta: {
        title: '外呼主页',
        icon: 'outboundHome',
        noCache: true
      }
    }]
  },





  {
    path: '/callLog',
    name: 'callLog',
    component: Layout,
    redirect: 'noRedirect',
    children: [{
        path: 'callLogContent',
        component: () => import('@/views/outbound/callLog/index'),
        name: 'callLogContent',
        meta: {
          title: '通话记录',
          icon: 'callLog',
          noCache: true
        }
      }

    ]
  },


  {
    path: '/outbound',
    name: 'outbound',
    component: Layout,
    redirect: 'noRedirect',
    children: [{
      path: 'phoneStatistic',
      name: 'phoneStatistic',
      component: () => import('@/views/outbound/phoneStatistic/index'),
      meta: {
        title: '通话目标完成统计',
        icon: 'phoneStatistic',
        noCache: true
      }
    }]
  },

  {
    path: '/outboundDayStatistic',
    name: 'outboundDayStatistic',
    component: Layout,
    redirect: 'noRedirect',
    children: [{
      path: 'dayStatistic',
      name: 'dayStatistic',
      component: () => import('@/views/outbound/dayStatistic/index'),
      meta: {
        title: '日通话统计分析',
        icon: 'dayStatistic',
        noCache: true
      }
    }]
  },

  {
    path: '/seats',
    name: 'seats',
    component: Layout,
    redirect: 'noRedirect',
    children: [{
        path: 'seatsContent',
        component: () => import('@/views/outbound/seats/index'),
        name: 'seatsContent',
        meta: {
          title: '坐席管理',
          icon: 'seats',
          noCache: true
        }
      }

    ]
  },

  {
    path: '/riskType',
    name: 'riskType',
    component: Layout,
    redirect: 'noRedirect',
    children: [{
        path: 'content',
        component: () => import('@/views/outbound/riskType/index'),
        name: 'content',
        meta: {
          title: '风险类型与处罚',
          icon: 'warning',
          noCache: true
        }
      }

    ]
  }

]

export default outbound
