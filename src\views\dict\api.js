import {
    fetch,
    post,
    put,
    deleteHttp
  } from '@/utils/request'
  
  // 查询列表
  export const queryPage = data => {
    return fetch(`/data/dict`, data)
  }

  // 添加
export const addDict = data => {
  return post(`/data/dict`, data)
}

// 更新
export const updateDict = data => {
  return put(`/data/dict`, data)
}

  
  // 查询明细
  export const getDictById = data => {
    return fetch(`/data/dict/${data.id}`)
  }

  
  // 删除
  export const deletetDict = data => {
    return deleteHttp(`/data/dict/${data.id}`)
  }
  