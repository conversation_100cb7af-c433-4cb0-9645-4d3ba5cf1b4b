import {
    fetch,
    post,
    put,
    deleteHttp
  } from '@/utils/request'
  
  // 查询列表
  export const qryPage = data => {
    return fetch(`/attachment/qryPage`, data)
  }



// 查询详情
   export const getById = data => {
    return fetch(`/attachment/${data.id}`, data)
  }
  

  // 文件预览
  export const selectOne = data => {
    return fetch(`/preview/onlinePreview`, data)
  }   
    
//文件上传
  export const uploadInfo = data => {
    return post(`/attachment/file/uploads`, data)
  }
  