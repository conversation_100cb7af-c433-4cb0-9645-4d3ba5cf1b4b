import Cookies from 'js-cookie'

const Token<PERSON>ey = 'Token'
const dbKey = 'dbKey'

export function getToken () {
  return Cookies.get(TokenKey)
}

export function setToken (token) {
  return Cookies.set(Token<PERSON><PERSON>, token)
}

export function removeToken () {
  return Cookies.remove(TokenKey)
}

export function getDbKey () {
  return Cookies.get(dbKey)
}

export function setDbKey (token) {
  return Cookies.set(dbKey, token)
}

export function removetDbKey () {
  return Cookies.remove(dbKey)
}
