<template>
  <div class="video-view">
    <div class="left-content">
      <el-select
        v-model="selectedValue"
        multiple
        filterable
        placeholder="最多选择四个视频"
        style="width:200px"
        @change="selectChange"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
    <div style="text-align:left;padding-left: 10px;overflow:hidden;" v-if="visibleFrame">
      <div
        v-for="item in videoPlayList"
        :key="item"
        :class="'height'+activeIndex"
        style="display:inline-block;margin-top:0;text-align:left;"
      >
        <!--  -->
        <iframe
          v-if="visibleFrame"
          :src="item.src"
          width="100%"
          height="100%"
          style="border:2px solid #0072D3;overflow: hidden;"
          ref="ysOpenDevice"
          allowfullscreen
        ></iframe>
      </div>

      <div class="bottom-content">
        <div style="width:48%;display:inline-block;margin-top:0;text-align:left;color:#fff;">
          <div style="display:inline-block;cursor:pointer">分屏：</div>
          <div
            :class="activeIndex == 1 ? 'active':''"
            style="display:inline-block;cursor:pointer;margin-left:20px;"
            @click="view(1)"
          >1</div>
          <div
            :class="activeIndex == 2 ? 'active':''"
            style="display:inline-block;margin-left:50px;cursor:pointer"
            @click="view(2)"
          >2</div>
          <div
            :class="activeIndex == 4 ? 'active':''"
            style="display:inline-block;margin-left:50px;cursor:pointer"
            @click="view(4)"
          >4</div>
        </div>
        <div style="width:50%;display:inline-block;margin-top:0;">
          <!-- <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, prev, pager, next, jumper"
            :total="400"
          ></el-pagination>-->
        </div>
      </div>
    </div>
    <div class="clear"></div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [],
      originList: [1, 2, 3, 4, 5, 6, 7, 8],
      videoPlayList: [],
      activeIndex: 0,
      currentPage4: 1,
      selectedValue: [],
      src: '',
      title: '',
      visibleFrame: true,
      pageSize: 10,
      totalPage: 0
    }
  },
  methods: {
    selectChange(val) {
      this.visibleFrame = false
      if (this.selectedValue.length > 4) {
        this.$message.warning('最多选择四个视频！')
        this.selectedValue = this.selectedValue.slice(0, 4)
        this.activeIndex = 4
      }
      if (this.selectedValue.length == 3) {
        this.activeIndex = 4
      }
      if (this.selectedValue.length == 2) {
        this.activeIndex = 2
      }
      if (this.selectedValue.length == 1) {
        this.activeIndex = 1
      }
      this.videoPlayList = []
      this.options.forEach(item => {
        if (this.selectedValue.indexOf(item.value) > -1) {
          this.videoPlayList.push(item)
        }
      })
      this.$nextTick(() => {
        this.visibleFrame = true
      })
    },
    view(count) {
      this.visibleFrame = false
      this.activeIndex = count

      if (count == 1) {
        this.videoPlayList = this.videoPlayList.slice(0, 1)
      } else if (count == 2) {
        this.videoPlayList =
          this.videoPlayList.length >= 2
            ? this.videoPlayList.slice(0, 2)
            : this.options.slice(0, 2)
      } else if (count == 4) {
        this.videoPlayList =
          this.videoPlayList.length >= 4
            ? this.videoPlayList.slice(0, 4)
            : this.options.slice(0, 4)
      }

      this.selectedValue = []
      this.videoPlayList.forEach(item => {
        this.selectedValue.push(item.value)
      })

      this.$nextTick(() => {
        this.visibleFrame = true
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
    },
    openVideo() {
      this.visibleFrame = true
    }
  }
}
</script>
<style lang="scss">
.iframe-video {
  height: 100vh;
  background-color: rgb(2, 16, 50) !important;
  .el-dialog__header,
  .el-dialog__body {
    padding: 15px 0;
    .el-dialog__title,
    .el-icon-close {
      color: #fff !important;
    }
    .active {
      color: red;
    }
  }
  .el-dialog__header {
    height: 50px;
    padding: 12px;
    background: url('../../assets/img/bigscreen_new/top_bg.png');
  }
  .el-dialog__headerbtn {
    top: 14px;
  }
  .bottom-content {
    position: fixed;
    background-color: rgb(3, 26, 70);
    bottom: 10px;
    height: 30px;
    width: 100%;
    line-height: 30px;
    padding-right: 30px;
  }
  .height1,
  .height2 {
    height: calc(100vh - 120px);
    width: calc(50% - 3.5px);
  }
  .height4 {
    height: calc((100vh - 120px) / 2);
    width: calc(50% - 3.5px);
  }
  .height1 {
    width: 100%;
  }
  .height1,
  .height2,
  .height4 {
    &:nth-child(even) {
      margin-left: 7px;
    }
  }
  .clear {
    clear: both;
  }
  .left-content {
    float: left;
    padding-top: 10px;
    text-align: center;
    height: calc(100vh - 80px);
    width: 230px;
    background-color: rgb(3, 26, 70);
  }
}
</style>