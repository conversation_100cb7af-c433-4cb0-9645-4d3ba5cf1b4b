<template>
  <div v-if="!item.hidden">
    <template
      v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow"
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{'submenu-title-noDropdown':!isNest}"
        >
          <item
            :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)"
            :title="onlyOneChild.meta.title"
          />
          <span
            v-if="onlyOneChild.meta.requestPath &&  onlyOneChild.meta.count>0 && flag"
            style="color:red;display:inline-block;margin-right:5px"
          >{{ updateCount(onlyOneChild) }}</span>
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.name)" popper-append-to-body>
      <template slot="title">
        <item v-if="item.meta" :icon="item.meta && item.meta.icon" :title="item.meta.title" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'
import { fetch, post, put, postToken, deleteHttp } from '@/utils/request'
export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {
      flag: false
    }
  },
  watch: {
    flag(val, oldVal) {
      setTimeout(() => {
        this.flag = oldVal
      }, 300000)
    }
  },
  mounted() {
    this.flag = true
  },
  methods: {
    updateCount(item) {
      if (item.meta.requestPath) {
        fetch(item.meta.requestPath, item.meta.params).then(res => {
          if (res && res.code == 200) {
            item.meta.count = res.count
          } else {
            console.log('错误')
            console.log(item.meta)
          }
        })
        return item.meta.count
      }
    },
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          this.flag = true
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          if (item.meta.requestPath && !item.meta.count) {
            fetch(item.meta.requestPath, item.meta.params).then(res => {
              if (res && res.code == 200) {
                item.meta.count = res.count
              } else {
                console.log('错误')
                console.log(item.meta)
              }
            })
          }
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
