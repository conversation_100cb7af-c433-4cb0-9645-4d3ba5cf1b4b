import {
  fetch,
  post,
  put,
  formDataPost,
  deleteHttp
} from '@/utils/request'

// 查询列表
export const getUsersList = data => {
  return fetch(`/users`, data)
}

// 保存客户
export const saveUsers = data => {
  return post(`/users`, data)
}

// 更新客户
export const putUsers = data => {
  return put(`/users`, data)
}

// 删除客户
export const deleteUsers = data => {
  return deleteHttp(`/users/${data.id}`)
}
