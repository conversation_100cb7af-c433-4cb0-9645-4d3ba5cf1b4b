import {
  fetch,
  post,
  put,
  postToken,
  deleteHttp
} from '@/utils/request'

import {
  getToken
} from '@/utils/auth'

// 查询列表
export const getStaffList = data => {
  return fetch(`/user/groups`)
}

// 添加
export const addStaff = data => {
  return post(`/user/groups`, data)
}

export const updateRoles = data => {
  return put(`/user/roles`, data)
}

// 添加岗位
export const addPosition = data => {
  return post(`/user/roles`, data)
}

// 更新
export const updateStaff = data => {
  return put(`/user/groups`, data)
}

// 更新
export const updateStaffRole = data => {
  return put(`/user/roles`, data)
}


// 启用禁用
export const updateStaffStatus = data => {
  return post(`/user/groups/setStatus`, data)
}

export const updateRoleStatus = data => {
  return post(`/user/roles/setStatus`, data)
}

// 删除
export const deleteStaff = data => {
  return deleteHttp(`/user/groups/${data.id}`)
}

export const deleteRole = data => {
  return deleteHttp(`/user/roles/${data.id}`)
}

// 获取员工权限菜单
export const getPermission = data => {
  return fetch(`/user/roles/${data.id}`)
}

// 添加/更新员工权限菜单
export const setPermission = data => {
  return post(`/user/roles/setPermission`, data)
}


// 获取部门下的岗位数据--包含子级部门下的岗位
export const findByParentId = data => {
  return fetch(`/user/groups/findByParentId`, data)
}

// 获取所有部门
export const findDeptTree = data => {
  return fetch(`/user/groups/findDeptTree`, data)
}

// 获取部门下的岗位 -- 不包含子级部门
export const findPostByDeptId = data => {
  return fetch(`/user/roles/findPostByDeptId`, data)
}
