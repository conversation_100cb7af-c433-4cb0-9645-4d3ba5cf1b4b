<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
    <el-tabs>
      <el-tab-pane :label="$route.query.id ? '修改员工信息' : '员工列表'">
      <el-row>
        <direct-search
          ref="form"
          :formDefaults="formDefaults"
          :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }"
          :forms="searchList"
          @handleSearch="handleFormSearch"
        />
      </el-row>
      <!-- :tab-type="'index'"
      :tab-label="'序号'"-->
      <el-col style="padding:15px 0">
        <el-table-self
          :columns="columns"
          :current-page="pageIndex"
          :list-loading="listLoading"
          :table-data="dataList"
          :total-count="total"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :tab-index="tabIndex"
          @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange"
        />
      </el-col>

      <el-dialog title="弹出框" 
   
    width="800px" 
    top="5vh" 
    :close-on-click-modal="false"
    append-to-body/>

      <draw-form
        ref="drawForm"
        :forms="drawForms"
        :inline-flag="true"
        @handleConfirm="handleConfirm"
        :label-width="'100'"
        :title="drawTitle"
      ></draw-form>

      <dialog-form
        ref="audit"
        title="重置密码"
        :form-data="auditData"
        :form-edit="auditEdit"
        @handleConfirm="handleSetPwd"
      />


     </el-tab-pane>
     <el-tab-pane
          label="批量导入"
          name="second"
        
        >
          <span style="color:red">*</span>批量导入
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              :on-change="uploadByJsqd"
              :file-list="fileList">
              <el-button type="plain" style="margin-left:10px">选择文件</el-button>
            </el-upload>
          </div>

          <div>
            <el-link
              type="primary"
              @click="getCustTemplateData"
              style="margin-top:10px;font-size: 16px;"
              >下载模板</el-link>
          </div>
        </el-tab-pane>

      </el-tabs>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import drawForm from "@/components/FormComponents/draw";
import dialogForm from "@/components/FormComponents/dialogForm";
import paginationMixin from "@/components/TabComponents/mixin";
import { getUsersList, 
         saveUsers, 
         deleteUsers, 
         putUsers,
         getUserTemplate,
         uploadInfo,
         updateStatus,
         getDeptName,
         getRankSalary,
         getRoleName,
         resetpwd
  } from "./api";
import date from "@/utils/date";


export default {
  components: {
    elTableSelf,
    drawForm,
    dialogForm,
    directSearch
  },
  mixins: [paginationMixin],
  data() {
    return {
      groupList: [],
      drawTitle: "",
      listLoading: false,
      total: 0,
      id: 0,
      auditData: [
        {
          type: "input",
          name: "新密码",
          field: "newPwd",
          inputType: "password",
          rules: [
            {
              pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/,
              required: true,
              message: "密码必须为包含大小写字母、数字，长度8~20位",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          name: "重复密码",
          inputType: "password",
          field: "retypePwd",
          rules: [
            {
              pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/,
              required: true,
              message: "密码必须为包含大小写字母、数字，长度8~20位",
              trigger: "blur"
            }
          ]
        }
      ],
      auditEdit: {
        id: "",
        newPwd: "",
        retypePwd: ""
      },
      // uploadObj: {},
      cacheForm: {},
      formDefaults: {},
      drawForms: [
      {
          type: "uploadImg",
          label: "头像",
          prop: "iconList",
          class: "inputMore9"
        },
        {
          type: "input",
          label: "姓名",
          prop: "name",
          placeholder: "请填写姓名",
          class: "inputMore9",
          rules: [
            {
              required: true,
              message: "请填写姓名",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "手机号码",
          prop: "phone",
          placeholder: "请填写手机号码",
          class: "inputMore9",
          maxlength: 11,
          rules: [
            {
              required: true,
              message: "请填写手机号码",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          class: "inputMore9",
          label: "所属公司",
          prop: "type",
          placeholder: `请选择所属公司`,
          opts: [
            {
              label: "总部",
              value: 1
            },
            {
              label: "蚌埠分公司",
              value: 2
            },
            {
              label: "合肥分公司",
              value: 3
            },
            {
              label: "常州分公司",
              value: 4
            },
            {
              label: "无锡分公司",
              value: 5
            }
          ],
          rules: [
            {
              required: true,
              message: "请选择所属公司",
              trigger: "change"
            }
          ]
        },
        {
          type: "radio",
          prop: "isRegular",
          maxlength: 100,
          opts: [
            {
              label: "是",
              value: 1
            },
            {
              label: "否",
              value: 2
            }
          ],
          label: "是否转正"
        },
      
        {
          type: "radio",
          prop: "sex",
          maxlength: 100,
          opts: [
            {
              label: "男",
              value: 1
            },
            {
              label: "女",
              value: 2
            }
          ],
          label: "性别"
        },
        {
          label: "部门",
          type: "select",
          diyLabel: "name",
          diyValue: "id",
          isSelect: true,
          prop: "groupId",
          dataOrigin: "deptName",
          opts: []
        },
        // {
        //   prop: "deptId",
        //   class: "inputMore9",
        //   dateOrigin: "deptId",
        //   showColon: true,
        //   label: "部门",
        //   type: "select",
        //   opts: [],
        //   selectParams: {
        //     multiple: false, // 是否多选
        //     clearable: false,
        //     collapseTags: true,
        //     placeholder: ""
        //   },
        //   treeParams: {
        //     clickParent: true,
        //     filterable: true,
        //     "check-strictly": false, // 父子不相互关联
        //     "default-expand-all": true, // 是否展开
        //     "expand-on-click-node": false, // 是否在点击节点的时候展开或者收缩节点
        //     data: [],
        //     // 配置项
        //     props: {
        //       children: "children", // 指定子树为节点对象的某个属性值
        //       label: "name", // 指定节点标签为节点对象的某个属性值
        //       value: "id"
        //     }
        //   },
        //   rules: [
        //     {
        //       required: true,
        //       message: "请选择部门",
        //       trigger: "change"
        //     }
        //   ]
        // },
        // {
        //   type: "select",
        //   prop: "roleId",
        //   class: "inputMore9",
        //   diyLabel: "name",
        //   diyValue: "id",
        //   isSelect: true,
        //   dateOrigin: "roleId",
        //   showColon: true,
        //   label: "岗位",
        //   opts: [],
        //   rules: [
        //     {
        //       required: true,
        //       message: "请选择岗位",
        //       trigger: "change"
        //     }
        //   ]
        // },
        {
          label: "岗位角色",
          type: "select",
          diyLabel: "name",
          diyValue: "id",
          isSelect: true,
          prop: "roleId",
          dataOrigin: "roleName",
          rules: [
            {
              required: true,
              message: "请填写岗位角色",
              trigger: "blur"
            }
          ],
          opts: []
        }, 
        {
          label: "薪酬职级",
          type: "select",
          diyLabel: "rankSalary",
          diyValue: "levelCodePm",
          isSelect: true,
          prop: "levelCodePm",
          dataOrigin: "rankSalary",
          rules: [
            {
              required: true,
              message: "请填写薪酬职级",
              trigger: "blur"
            }
          ],
          opts: []
        },
        {
          type: "date",
          class: "inputMore9",
          label: "入职时间",
          prop: "entryTime",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择入职时间`,
          rules: [
            {
              required: true,
              message: "请选择入职时间",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "试用期限（个月）",
          prop: "probationPeriod",
          placeholder: `请填写试用期限`,
          rules: [
            {
              required: true,
              message: "请填写试用期限",
              trigger: "blur"
            }
          ]
        },
        {
          type: "date",
          class: "inputMore9",
          label: "待转正日期",
          prop: "preWorkerDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择待转正日期`,
          rules: [
            {
              required: true,
              message: "请选择待转正日期",
              trigger: "blur"
            }
          ]
        },
        {
          type: "date",
          class: "inputMore9",
          label: "实际转正日期",
          prop: "actualWorkerDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择实际转正日期`,
        },
        {
          type: "date",
          class: "inputMore9",
          label: "离职日期",
          prop: "leaveTime",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择离职日期`,
        },
        {
          type: "select",
          class: "inputMore9",
          label: "离职原因",
          prop: "leaveReason",
          placeholder: `请输入离职原因`,
          opts: [
            {
              label: "自离",
              value: '自离'
            },
            {
              label: "淘汰",
              value: '淘汰'
            },
            {
              label: "调岗",
              value: '调岗'
            },
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "身份证",
          prop: "idNum",
          placeholder: `请输入身份证`,
          rules: [
            {
              required: true,
              message: "请输入身份证",
              trigger: "blur"
            }
          ]
        },
        {
          type: "date",
          class: "inputMore9",
          label: "出生日期",
          prop: "birthday",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择出生日期`
        },
        {
          type: "input",
          class: "inputMore9",
          label: "学历",
          prop: "education",
          placeholder: `请输入学历`,
          rules: [
            {
              required: true,
              message: "请输入学历",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "毕业院校",
          prop: "graduationInstitutions",
          placeholder: `请输入毕业院校`
        },
        {
          type: "input",
          class: "inputMore9",
          label: "所学专业",
          prop: "major",
          placeholder: `请输入所学专业`
        },
        {
          type: "date",
          class: "inputMore9",
          label: "毕业时间",
          prop: "graduationDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择毕业时间`,
          rules: [
            {
              required: true,
              message: "请选择毕业时间",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          class: "inputMore9",
          label: "职称等级",
          prop: "title",
          placeholder: `请选择职称等级`,
          opts: [
            {
              label: "初级",
              value: 1
            },
            {
              label: "中级",
              value: 2
            },
            {
              label: "高级",
              value: 3
            },
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "职称证书名称",
          prop: "titleName",
          placeholder: `请输入职称证书名称`
        },
        {
          type: "date",
          class: "inputMore9",
          label: "职称获取时间",
          prop: "titleDate",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择职称获取时间`
        },
        {
          type: "input",
          class: "inputMore9",
          label: "户籍/省份",
          prop: "province",
          placeholder: `请输入户籍/省份`,
          rules: [
            {
              required: true,
              message: "请输入户籍/省份",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "户籍/城市",
          prop: "city",
          placeholder: `请输入户籍/城市`,
          rules: [
            {
              required: true,
              message: "请输入户籍/城市",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "民族",
          prop: "nation",
          placeholder: `请输入民族`,
          rules: [
            {
              required: true,
              message: "请输入民族",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          class: "inputMore9",
          label: "婚姻状况",
          prop: "maritalStatus",
          placeholder: `请选择婚姻状况`,
          opts: [
            {
              label: "已婚",
              value: 1
            },
            {
              label: "未婚",
              value: 2
            },
            {
              label: "其他",
              value: 3
            },
          ],
          rules: [
            {
              required: true,
              message: "请选择婚姻状况",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "手机号码",
          prop: "phone",
          placeholder: "请填写手机号码",
          class: "inputMore9",
          maxlength: 11,
          rules: [
            {
              required: true,
              message: "请填写手机号码",
              trigger: "blur"
            }
          ]
        }, 
        {
          type: "input",
          class: "inputMore9",
          label: "个人邮箱",
          prop: "email",
          placeholder: `请选择个人邮箱`,
          rules: [
            {
              required: true,
              message: "请选择个人邮箱",
              trigger: "blur"
            }
          ]
        },
        {
          type: "textarea",
          label: "身份证登记地址",
          style: "width:100%",
          itemStyle: "width:520px",
          prop: "idNumAddress",
          placeholder: "身份证登记地址",
          resize: "auto",
          autosize: true
        },
        {
          type: "textarea",
          label: "现住址",
          style: "width:100%",
          itemStyle: "width:520px",
          prop: "nowAddress",
          placeholder: "现住址",
          resize: "auto",
          autosize: true
        },
        {
          type: "select",
          class: "inputMore9",
          label: "户口性质",
          prop: "resident",
          placeholder: `请选择户口性质`,
          opts: [
            {
              label: "农业",
              value: 1
            },
            {
              label: "非农业",
              value: 2
            },
          ],
          rules: [
            {
              required: true,
              message: "请选择户口性质",
              trigger: "blur"
            }
          ]
        },  
        {
          type: "input",
          class: "inputMore9",
          label: "紧急联系人",
          prop: "contact",
          placeholder: `请选择紧急联系人`,
          rules: [
            {
              required: true,
              message: "请选择紧急联系人",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "紧急联系人电话",
          prop: "contactPhone",
          placeholder: `请选择紧急联系人电话`,
          rules: [
            {
              required: true,
              message: "请选择紧急联系人电话",
              trigger: "blur"
            }
          ]
        },
        {
          type: "select",
          class: "inputMore9",
          label: "是否缴纳社保",
          prop: "social",
          placeholder: `请选择是否缴纳社保`,
          opts: [
            {
              label: "缴纳",
              value: 1
            },
            {
              label: "未缴纳",
              value: 0
            },
          ],
        }, 
        {
          type: "input",
          class: "inputMore9",
          label: "缴纳社保城市",
          prop: "socialAddress",
          placeholder: `请输入缴纳社保城市`,
        },
        {
          type: "date",
          class: "inputMore9",
          label: "开始缴纳社保月份",
          prop: "socialMonth",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择开始缴纳社保月份`
        },
        {
          type: "input",
          class: "inputMore9",
          label: "开户行",
          prop: "bank",
          placeholder: `请输入开户行`,
          rules: [
            {
              required: true,
              message: "请输入开户行",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "开户网点",
          prop: "bankBranch",
          placeholder: `请输入开户网点`,
          rules: [
            {
              required: true,
              message: "请输入开户网点",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "工资卡号",
          prop: "bankNo",
          placeholder: `请输入工资卡号`,
          rules: [
            {
              required: true,
              message: "请输入工资卡号",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "开户姓名",
          prop: "bankNoName",
          placeholder: `请输入开户姓名`,
          rules: [
            {
              required: true,
              message: "请输入工资卡号",
              trigger: "blur"
            }
          ]
        },
        {
          type: "title",
          title: "账号信息",
          style: "margin-bottom:20px"
        },

        {
          type: "input",
          label: "登录账号",
          prop: "loginName",
          placeholder: "长度不少于6位",
          class: "inputMore9",
          minlength: 6,
          rules: [
            {
              required: true,
              message: "请输入登录账号",
              trigger: "blur"
            }
          ]
        },

        {
          type: "input",
          label: "初始密码",
          prop: "password",
          placeholder: "必须为包含大小写字母、数字，长度8~20位",
          class: "inputMore9"
          // rules: [
          //   {
          //     pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/,
          //     required: true,
          //     message:
          //       "请输入密码必须为包含大小写字母、数字和特殊字符，长度8~20位",
          //     trigger: "blur"
          //   }
          // ]
        }
 
      ],
      fileList: [],
      dataList: [],
      columns: [      
        {
          value: "type",
          width: 120,
          label: "所属分公司",
          formatter(row) {
            return row.type == '0' 
            ? "全部" 
            : row.type == '1' 
            ? "总部" 
            : row.type == '2' 
            ? "蚌埠分公司" 
            : row.type == '3' 
            ? "合肥分公司" 
            : row.type == '4' 
            ? "常州分公司" 
            : row.type == '5' 
            ? "无锡分公司" 
            : "";
          }
        },{
          value: "loginName",
          width: 120,
          label: "账号"
        },{
          value: "name",
          width: 120,
          label: "姓名"
        },
        {
          value: "isRegular",
          width: 80,
          label: "是否转正",
          formatter(row) {
            return row.isRegular == 1 ? "是" : row.isRegular == 2 ? "否" : "";
          }
        },
        {
          value: "sex",
          width: 80,
          label: "性别",
          formatter(row) {
            return row.sex == 1 ? "男" : row.sex == 2 ? "女" : "";
          }
        },
        {
          value: "duty",
          width: 100,
          label: "职务"
        },
        {
          value: "entryTime",
          label: "入职日期"
        },
        {
          value: "actualWorkerDate",
          label: "实际转正日期"
        },
        {
          value: "phone",
          width: 150,
          label: "手机号码"
        },
        {
          value: "disabled",
          width: 100,
          label: "离职状态",
          formatter(row) {
            return row.disabled == 1 ? '离职' : row.disabled == 0 ? '在职' : null;
          }
        },
        {
          value: "leaveReason",
          width: 150,
          label: "离职原因"
        },  
        {
          label: "注册时间",
          value: "createdAt",
          formatter(row) {
            return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "更新时间",
          value: "updatedAt",
          formatter(row) {
            return date.dateFormat(row.updatedAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "操作",
          fixed: "right",
          width: 180,
          operType: "button",
          operations: [
          // {
          //     label: "详情",
          //     type: "detail",
          //     func: this.handleDetail
          //   },
            {
              label: "编辑",
              type: "primary",
              func: this.handleEdit
            },
            // {
            //   label: "删除",
            //   type: "danger",
            //   func: this.handleDelete
            // },
            {
              formatter(row) {
                return {
                  label:
                    row.disabled == 1
                      ? "离职"
                      : row.disabled == 0
                      ? "在职"
                      : "",
                  type: "warning",
                };
              },
              func: this.handleStatus,
            },
            // {
            //   label: "重置密码",
            //   type: "primary",
            //   func: this.handleReset,
            // },
          ]
        }
      ],
      searchList: [
      {
          label: "所属公司",
          labelWidth: "80px",
          type: "select",
          opts: [
          {
              label: "全部",
              value: null
            },
            // {
            //   label: "总部",
            //   value: 1
            // },
            {
              label: "蚌埠分公司",
              value: 2
            },
            {
              label: "合肥分公司",
              value: 3
            },
            {
              label: "常州分公司",
              value: 4
            },
            {
              label: "无锡分公司",
              value: 5
            }
          ],
          prop: "type"
        },
        {
          label: "账号",
          labelWidth: "60px",
          type: "input",
          prop: "loginName"
        },
        {
          label: "姓名",
          labelWidth: "60px",
          type: "input",
          prop: "name"
        },
        {
          label: "入职日期",
          labelWidth: "80px",
          type: "date",
          prop: "entryTime"
        },
        {
          label: "离职状态",
          labelWidth: "100px",
          type: "select",
          opts: [
            {
              label: "全部",
              value: ""
            },
            {
              label: "在职",
              value: 0
            },
            {
              label: "离职",
              value: 1
            }
          ],
          prop: "disabled"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "新增",
              icon: "el-icon-plus",
              func: this.handleAdd
            }
          ]
        }
      ]
    };
  },
  watch: {},
  mounted() {
    this.initOpts();
    // findDeptTree().then(res => {
    //   if (res.code == 200) {
    //     this.groupList = res.data;
    //     for (let i = 0; i < this.drawForms.length; i++) {
    //       if (this.drawForms[i].dateOrigin == "deptId") {
    //         this.drawForms[i].opts = res.data;
    //       }
    //     }
    //   }
    //   this.listLoading = false;
    // });
    this.handleSearch();
  },
  methods: {
    handleAdd() {
      this.drawTitle = "新增员工";
      this.$refs.drawForm.open();
      this.initForm = {disabled:0, password: "111111" ,sex: 1, isRegular:2};
      this.drawForms[this.drawForms.length - 1].disabled = true;
      this.$refs.drawForm.initforms(this.initForm);
    },


    handleStatus(row) {
      const data = {
        id: row.id,
        disabled:
          row.disabled == 0
            ? 1
            : row.disabled == 1
            ? 0
            : null,
      };
      updateStatus(data).then((res) => {
        if (res.code == 200) {
          this.$message.success("修改成功！");
          this.handleSearch();
        }
      });
    },



      // 重置密码
    // resetpwd
    handleReset(row) {
      this.auditEdit = {
        id: row.id,
        newPwd: "",
        retypePwd: ""
      };

      this.$refs.audit.open();
    },

    handleSetPwd(obj) {
      if (obj.newPwd != obj.retypePwd) {
        this.$message.warning("两次密码不一致！");
        this.$refs.audit.loading = false;
        return;
      }
      resetpwd(obj).then(res => {
        if (res.code == 200) {
          this.$message.success("修改成功！");
          this.$refs.audit.close();
        }
      });
    },



    handleDetail(row) {
      // this.drawTitle = "详情";
      // this.$refs.drawForm.open();
      // // this.$refs.drawForm.showLoading();
      // this.drawForms[this.drawForms.length - 1].hidden = true;
      // this.drawForms[this.drawForms.length - 2].hidden = true;
      // this.drawForms[this.drawForms.length - 3].hidden = true;
      // getDetail({ id: row.id })
      //   .then(res => {
      //     if (res.code == 200) {
      //       this.$refs.drawForm.initforms(res.data);
      //       this.$nextTick(function() {
      //         this.initForm = Object.assign({}, this.$refs.drawForm.form);
      //       });
      //     }
      //     this.$refs.drawForm.hideLoading();
      //   })
      //   .catch(err => {
      //     console.log(err);
      //     this.$refs.drawForm.hideLoading();
      //   });


      this.drawTitle = "详情";
      this.$refs.drawForm.open();
      this.drawForms[this.drawForms.length - 1].hidden = true;
      this.drawForms[this.drawForms.length - 2].hidden = true;
      this.drawForms[this.drawForms.length - 3].hidden = true;
      this.$refs.drawForm.initforms(row);
    },




    // initOpts() {
    //   getDeptName().then(res => {
    //     if (res.code == 200) {
    //       for (let i = 0; i < this.drawForms.length; i++) {
    //         if (this.drawForms[i].dataOrigin == "deptName") {
    //           this.drawForms[i].opts = res.data;
    //           break;
    //         }
    //       }
    //     }
    //   });
    // },





    handleEdit(row) {
      this.drawTitle = "编辑";
      this.$refs.drawForm.open();
      this.drawForms[this.drawForms.length - 1].hidden = true;
      this.drawForms[this.drawForms.length - 2].hidden = true;
      this.drawForms[this.drawForms.length - 3].hidden = true;
      // this.$refs.drawForm.disabled=false;
      this.$refs.drawForm.initforms(row);
    },

    handleDelete(row) {
      this.$confirm(`确定要删除${row.name}吗？`, "提示", {
        type: "warning"
      })
        .then(() => {
          this.tableLoading = true;
          deleteUsers({ id: row.id })
            .then(res => {
              if (res.code == 200) {
                this.$message.success("删除成功");
                this.handleSearch();
              } else {
                this.tableLoading = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },

    handleConfirm(row) {
      if (row.id) {
        putUsers(row).then(res => {
          if (res.code == 200) {
            this.$message.success("更新成功！");
            this.handleSearch();
            this.$refs.drawForm.close();
          }
        });
      } else {
        saveUsers(row).then(res => {
          if (res.code == 200) {
            this.$message.success("新增成功！");
            this.handleSearch();
            this.$refs.drawForm.close();
          }
        });
      }
    },



    getCustTemplateData() {
      getUserTemplate()
        .then(res => {
          const blob = new Blob([res]);
          const elink = document.createElement("a");
          elink.download = "import_user" + ".xlsx";
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        })
        .catch(error => {
          console.log(error);
        });
    },


    //文件校验方法
    beforeAvatarUpload(file) {
      // 通过split方法和fileArr方法获取到文件的后缀名
      let fileArr = file.name.split(".");
      let suffix = fileArr[fileArr.length - 1];
      //只能导入.xls和.xlsx文件
      if (!/(xls|xlsx)/i.test(suffix)) {
        this.$message("文件格式不正确");
        return false;
      }
      //不能导入大小超过2Mb的文件
      if (file.size > 2 * 1024 * 1024) {
        this.$message("文件过大，请上传小于2MB的文件〜");
        return false;
      }
      return true;
    },


     //文件发生改变就会触发的事件
     uploadByJsqd(file) {
      debugger
      //判断是否符合beforeAvatarUpload方法中的条件
      if (this.beforeAvatarUpload(file)) {
        this.fileList.name = file.name;
        this.fileList.url = "";
        var formdata = new FormData();
        formdata.append("file", file.raw);
        //importDevice：请求接口 formdata：传递参数
        uploadInfo(formdata).then(res => {
          if (res.code == 200) {
            this.$message.success("上传成功");
            this.importFile = "";
            this.fileList = [];
          }
        });
      }
    },



    handleFormSearch(form) {
      this.pageIndex = 1;
      this.handleSearch(form);
    },

    tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

    handleSearch(form) {
      this.cacheForm = this.cacheForm || form;
      const params = Object.assign(this.cacheForm, form);
      params.page = this.pageIndex;
      params.pageSize = this.pageSize;
      getUsersList(params).then(res => {
        if (res.code == 200) {
          this.total = res.data.total;
          this.dataList = res.data.records;
        }
        this.listLoading = false;
      });
    },

    close() {
      this.initForm = { disabled: 0 };
      this.$refs.drawForm.close();
    },

    initOpts() {
      // findDeptTree({ parentId: 0 }).then(res => {
      //   if (res.code == 200) {
      //     for (let j = 0; j < this.searchList.length; j++) {
      //       if (this.searchList[j].dateOrigin == "deptId") {
      //         this.searchList[j].treeParams.data = res.data;
      //         this.$refs.form.$refs.treeSelect[0].treeDataUpdateFun(res.data);
      //       }
      //     }

      //     for (let j = 0; j < this.drawForms.length; j++) {
      //       if (this.drawForms[j].dateOrigin == "deptId") {
      //         this.drawForms[j].treeParams.data = res.data;
      //       }
      //     }
      //   }
      // });

      getDeptName().then(res => {
        if (res.code == 200) {
          for (let i = 0; i < this.drawForms.length; i++) {
            if (this.drawForms[i].dataOrigin == "deptName") {
              this.drawForms[i].opts = res.data;
              break;
            }
          }
        }
      });


      getRankSalary().then(res => {
        if (res.code == 200) {
          for (let i = 0; i < this.drawForms.length; i++) {
            if (this.drawForms[i].dataOrigin == "rankSalary") {
              this.drawForms[i].opts = res.data;
              break;
            }
          }
        }
      });

      getRoleName().then(res => {
        if (res.code == 200) {
          for (let i = 0; i < this.drawForms.length; i++) {
            if (this.drawForms[i].dataOrigin == "roleName") {
              this.drawForms[i].opts = res.data;
              break;
            }
          }
        }

    });

    }
    
  }
};
</script>

<style lang="scss" scope>
.page-container .page-main {
  padding-top: 30px 25px;
}
.statistics {
  text-align: center;
  height: 120px;
  line-height: 120px;
}
</style>
