<template>
  <el-drawer
    :title="title"
    :custom-class="
      customClass ? 'drawer-form-ck ' + customClass : 'drawer-form-ck'
    "
    :size="drawerSize"
    :visible="drawer"
    v-if="drawer"
    :direction="direction"
    :wrapperClosable="true"
    :before-close="beforeClose"
    :destroy-on-close="true"
  >
    <div @click.stop="clickDrawerBody">
      <el-form
        v-if="drawer"
        ref="form"
        :model="form"
        :disabled="disabled"
        v-loading="formLoading"
        :label-position="labelPosition"
        :inline="inlineFlag"
        :label-width="labelWidth + 'px'"
        :size="size"
        class="el-form-self"
        :style="formStyle"
      >
        <template v-for="(item, index) in forms">
          <el-col
            v-if="!item.hidden"
            :key="index"
            :xs="item.xs ? item.xs : 24"
            :sm="item.sm ? item.sm : 24"
            :md="item.md ? item.md : 24"
            :lg="item.lg ? item.lg : 24"
            :xl="item.xl ? item.xl : 24"
          >
            <el-form-item
              :style="item.style"
              :class="item.itemClass ? item.itemClass : ''"
              :label="
                item.label ? item.label + (item.hiddenColon ? '' : '：') : ''
              "
              :prop="item.prop"
              :rules="item.rules"
            >
              <div
                v-if="item.tips"
                style="font-size: 12px; font-weight: 400; color: #bfc1c8"
              >
                {{ item.tips }}
              </div>
              <div
                :style="
                  totalWidth && labelWidth
                    ? 'max-width:' +
                      (parseInt(totalWidth) - parseInt(labelWidth)) +
                      'px'
                    : ''
                "
              >
                <div v-if="item.type == 'title'" class="part-title">
                  <span class="border"></span>
                  <span class="title">{{ item.title }}</span>
                </div>

                <template v-if="item.type === 'input'">
                  <!-- 输入框 -->
                  <el-input
                    :class="item.class ? item.class : 'el-input--small-8'"
                    :id="item.prop ? item.prop : ''"
                    :value="form[item.prop]"
                    :minlength="item.minlength"
                    :maxlength="item.maxlength"
                    :readonly="item.readonly"
                    :disabled="item.disabled"
                    :placeholder="item.placeholder"
                    autocomplete="off"
                    @focus="item.focusFunc ? item.focusFunc($event) : {}"
                    @change="item.func ? item.func($event) : {}"
                    @blur="item.blurFunc ? item.blurFunc($event) : {}"
                    @input="
                      item.inputFunc
                        ? item.inputFunc($event)
                        : (form[item.prop] = $event)
                    "
                    :showPassword="item.showPassword"
                  >
                    <span v-if="item.count" slot="suffix">{{
                      (form[item.prop] ? form[item.prop].length : 0) +
                        "/" +
                        item.maxlength
                    }}</span>
                    <span
                      v-if="item.unit"
                      :slot="item.slot ? item.slot : 'append'"
                      >{{ item.unit }}</span
                    >
                  </el-input>

                  <div
                    v-if="item.note"
                    :class="[
                      item.class ? item.class : 'el-input--small-8',
                      'ele-note'
                    ]"
                    v-html="item.note"
                  ></div>
                </template>

                <!-- 输入框2输入框  -->
                <template v-if="item.type === 'input2input'">
                  <input2input
                    @changeVal1="v => updateForm(v, item.prop1, index)"
                    @changeVal2="v => updateForm(v, item.prop2, index)"
                    :value1="form[item.prop1]"
                    :value2="form[item.prop2]"
                    @input1="item.inputFunc1 ? item.inputFunc1($event) : {}"
                    @input2="item.inputFunc2 ? item.inputFunc2($event) : {}"
                  ></input2input>
                </template>

                <!-- 密码输入框 -->
                <el-input
                  class="inputMore9 passward-input"
                  v-if="item.type === 'password'"
                  :id="item.prop ? item.prop : ''"
                  v-model.trim="form[item.prop]"
                  :minlength="item.minlength"
                  :maxlength="item.maxlength"
                  :readonly="item.readonly"
                  :disabled="item.disabled"
                  :placeholder="item.placeholder"
                  :show-password="item.showPassword"
                  auto-complete="new-password"
                  @focus="item.focusFunc ? item.focusFunc($event) : {}"
                  @blur="item.blurFunc ? item.blurFunc($event) : {}"
                  @change="item.func ? item.func($event) : {}"
                  @input="item.inputFunc ? item.inputFunc($event) : {}"
                >
                  <span v-if="item.count" slot="suffix">{{
                    form[item.prop].length + "/" + item.maxlength
                  }}</span>
                  <span
                    v-if="item.unit"
                    :slot="item.slot ? item.slot : 'append'"
                    >{{ item.unit }}</span
                  >
                </el-input>

                <template v-if="item.type === 'textarea'">
                  <el-input
                    :class="item.class"
                    @change="item.func ? item.func($event) : {}"
                    :resize="item.resize ? item.resize : 'none'"
                    v-model.trim="form[item.prop]"
                    type="textarea"
                    :disabled="item.disabled"
                    :placeholder="item.placeholder"
                    :rows="item.rows"
                    :autosize="item.autosize"
                    :minlength="item.minlength"
                    :maxlength="item.maxlength"
                    :show-word-limit="item.showLimit"
                  />

                  <div
                    v-if="item.note"
                    :class="[
                      item.class ? item.class : 'el-input--small-8',
                      'ele-note'
                    ]"
                    v-html="item.note"
                  ></div>
                </template>

                <!-- 多选框 -->
                <el-checkbox-group
                  :class="item.class ? item.class : 'el-input--small-8'"
                  v-else-if="item.type === 'checkbox'"
                  v-model="form[item.prop]"
                >
                  <el-checkbox
                    v-for="(opt, optIndex) in item.opts"
                    :disabled="opt.disabled"
                    v-if="!opt.hidden"
                    :label="opt.value"
                    :key="optIndex"
                    @change="
                      item.changeFun ? item.changeFun($event, opt.value) : {}
                    "
                    >{{ opt.label }}</el-checkbox
                  >
                </el-checkbox-group>

                <!-- 模糊查询输入 -->
                <el-autocomplete
                  v-else-if="item.type === 'autocomplete'"
                  v-model="form[item.prop]"
                  :class="item.class ? item.class : 'el-input--small-8'"
                  :popper-class="item.popperClass"
                  style="width: 100%"
                  :fetch-suggestions="item.func"
                  :placeholder="item.placeholder"
                  clearable
                  @select="item.selectfun ? item.selectfun($event) : {}"
                >
                  <template slot-scope="{ item }">
                    <div class="name">{{ item.value }}</div>
                    <span class="description">{{ item.description }}</span>
                  </template>
                </el-autocomplete>

                <!-- 模糊查询选择 -->
                <el-select
                  v-else-if="item.type === 'remote'"
                  v-model="form[item.prop]"
                  :class="item.class ? item.class : 'el-input--small-8'"
                  filterable
                  clearable
                  remote
                  ref="configSelect"
                  reserve-keyword
                  :placeholder="item.placeholder"
                  :remote-method="item.remoteFunc"
                >
                  <el-option
                    v-for="(opt, optIndex) in item.opts"
                    :key="optIndex"
                    :label="opt.label"
                    :value="item.isSelect ? opt.selectValue : opt.value"
                  />
                </el-select>

                <!-- 分页下拉框 -->
                <el-select
                  v-else-if="item.type === 'pageSelect' && !item.isHidden"
                  v-model="form[item.prop]"
                  clearable
                  :class="
                    item.class
                      ? item.class
                      : 'el-input--small-8 page-select-list'
                  "
                  :placeholder="item.placeholder ? item.placeholder : '请选择'"
                  :style="item.style"
                  filterable
                  @visible-change="item.visibleChange"
                  v-el-select-loadmore="item.loadmore"
                  remote
                  reserve-keyword
                  :remote-method="item.queryList"
                  :multiple="item.multiple"
                  collapse-tags
                  @change="item.func ? item.func($event) : {}"
                >
                  <!-- @change="item.func?item.func($event):handleSearch()" -->
                  <el-option
                    v-for="(opt, optIndex) in item.opts"
                    :key="optIndex"
                    :label="item.isSelect ? opt[item.diyLabel] : opt.label"
                    :value="item.isSelect ? opt[item.diyValue] : opt.value"
                  />
                </el-select>

                <el-tree-select
                  v-else-if="item.type === 'treeSelect'"
                  :class="
                    item.class
                      ? item.class
                      : 'el-input--small-8 page-select-list'
                  "
                  v-model="form[item.prop]"
                  :selectParams="item.selectParams"
                  @node-click="
                    treeSelect(item.treeParams, item.prop, item.selectParams)
                  "
                  @select-clear="
                    treeSelect(item.treeParams, item.prop, item.selectParams)
                  "
                  @check="
                    treeSelect(item.treeParams, item.prop, item.selectParams)
                  "
                  :treeParams="item.treeParams"
                  :treeRenderFun="renderFun"
                  ref="treeSelect"
                />

                <!-- 日期区间 -->
                <el-date-picker
                  v-else-if="item.type === 'daterange'"
                  v-model="form[item.prop]"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="item.options"
                  :format="item.format"
                  :value-format="item.valueFormat"
                />

                <!-- 日期 -->
                <el-date-picker
                  v-else-if="item.type === 'date'"
                  v-model="form[item.prop]"
                  :type="item.dateType ? item.dateType : 'date'"
                  :picker-options="item.options"
                  :format="item.format"
                  :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />

                <!-- 输入框-输入框 -->
                <template v-else-if="item.type === 'input-input'">
                  <div>
                    <el-input-number
                      v-if="item.type1 === 'number'"
                      v-model="form[item.prop1]"
                      :controls="false"
                      style="width: 200px"
                      :min="item.min1"
                      :max="item.max1"
                      :precision="item.precision1"
                    />
                    <el-input
                      v-else
                      :placeholder="
                        item.placeholder1 ? item.placeholder1 : '请输入内容'
                      "
                      v-model="form[item.prop1]"
                      style="width: 200px"
                    ></el-input>

                    <el-input-number
                      v-if="item.type2 === 'number'"
                      style="width: 200px; margin-left: 20px"
                      v-model="form[item.prop2]"
                      :controls="false"
                      :max="item.max2"
                      :min="item.min1"
                      :precision="item.precision2"
                    />
                    <el-input
                      v-else
                      :placeholder="
                        item.placeholder2 ? item.placeholder2 : '请输入内容'
                      "
                      v-model="form[item.prop2]"
                      style="width: 200px; margin-left: 20px"
                    ></el-input>
                  </div>
                </template>

                <el-time-picker
                  :class="item.class ? item.class : ''"
                  :editable="false"
                  :popper-class="
                    item.popperClass ? item.popperClass : 'time-spinner'
                  "
                  v-else-if="item.type === 'time'"
                  is-range
                  value-format="HH:mm"
                  format="HH:mm"
                  v-model="form[item.prop]"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="选择时间范围"
                ></el-time-picker>

                <!-- 下拉框 -->
                <template v-else-if="item.type === 'select'">
                  <el-select
                    v-model="form[item.prop]"
                    :class="item.class ? item.class : 'el-input--small-8'"
                    :clearable="item.clearable || true"
                    :multiple="item.multiple || false"
                    :filterable="item.filterable || false"
                    :disabled="item.disabled"
                    :placeholder="
                      item.placeholder ? item.placeholder : '请选择'
                    "
                    @change="item.func ? item.func($event) : {}"
                    @remove-tag="item.removeFunc ? item.removeFunc($event) : {}"
                  >
                    <el-option
                      v-for="(opt, optIndex) in item.opts"
                      :key="optIndex"
                      :disabled="opt.disabled"
                      :label="item.isSelect ? opt[item.diyLabel] : opt.label"
                      :value="item.isSelect ? opt[item.diyValue] : opt.value"
                    />
                  </el-select>
                  <div
                    v-if="item.note"
                    :class="[
                      item.class ? item.class : 'el-input--small-8',
                      'ele-note'
                    ]"
                    v-html="item.note"
                  ></div>
                </template>

                <!-- 单选框 -->
                <template v-else-if="item.type === 'radio'">
                  <el-radio
                    v-for="(opt, optIndex) in item.opts"
                    :key="optIndex"
                    :disabled="item.disabled"
                    v-model="form[item.prop]"
                    :class="item.class"
                    :label="opt.value"
                    @change="item.func ? item.func($event) : {}"
                    >{{ opt.label }}</el-radio
                  >

                  <div
                    v-if="item.note"
                    :class="[
                      item.class ? item.class : 'el-input--small-8',
                      'ele-note'
                    ]"
                    :style="item.noteStyle"
                    v-html="item.note"
                  ></div>
                </template>
                <!--开关-->
                <el-switch
                  v-else-if="item.type === 'switch'"
                  v-model="form[item.prop]"
                  :active-value="1"
                  :width="44"
                  active-color="#3366FF"
                  inactive-color="#bfc1c8"
                ></el-switch>

                <!-- 级联 -->
                <el-cascader
                  v-else-if="item.type === 'cascader'"
                  v-model="form[item.prop]"
                  :class="item.class ? item.class : 'el-input--small-8'"
                  :style="'width:' + item.width"
                  :clearable="item.clearable || true"
                  :options="item.list"
                  :popper-class="item.popperClass ? item.popperClass : ''"
                  :props="item.props"
                  :show-all-levels="item.showLevel"
                />

                <!-- 文字 -->
                <span v-else-if="item.type === 'text'">{{
                  item.text ? item.text : form[item.prop]
                }}</span>

                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="item.type === 'number'"
                  :class="item.class ? item.class : 'el-input--small-8'"
                  v-model.trim="form[item.prop]"
                  :controls="false"
                  :min="item.min || 0"
                  :max="item.max"
                  :precision="item.precision"
                  :disabled="item.disabled || disabled"
                />

                <!-- 图标 -->
                <span v-else-if="item.type === 'svg'">
                  <svg-icon
                    :style="item.style"
                    :icon-class="item.iconClass"
                    class="form-svg"
                    @click.native="clickSvg(item.func)"
                  />
                </span>

                <!-- 图片 -->
                <template v-else-if="item.type === 'uploadImg'">
                  <upload-img
                    @updateList="v => updateForm(v, item.prop, index)"
                    :file-list="form[item.prop]"
                    @showLoading="showLoading"
                    @hideLoading="hideLoading"
                    :file-size="item.fileSize"
                    :message="item.message"
                    :limit="item.limit"
                  />
                </template>

                <!--富文本-->
                <template v-else-if="item.type === 'editor'">
                  <editor
                    :editorData="form[item.prop]"
                    :height="item.height ? item.height : 500"
                    @handleSave="v => getEditorData(v, item.prop)"
                  ></editor>
                </template>
              </div>
            </el-form-item>
          </el-col>
        </template>

        <el-col v-if="lists" :xs="24" :sm="24" :lg="24" :style="btnStyle">
          <el-form-item>
            <el-button
              v-for="(btn, index) in lists"
              :key="index"
              :type="btn.type"
              :size="btn.size ? btn.size : 'medium'"
              :icon="btn.icon"
              :style="btn.style"
              @click="btn.func"
              >{{ btn.btnText }}</el-button
            >
          </el-form-item>
        </el-col>
      </el-form>
    </div>
    <div class="drawer-footer form-footer" v-if="disabled">
      <el-button size="medium" @click="cancel">关 闭</el-button>
    </div>

    <div class="drawer-footer form-footer" v-else>
      <el-button size="medium" @click="cancel">取 消</el-button>
      <el-button
        size="medium"
        :loading="formLoading"
        :type="!customerBookFlag ? 'primary' : ''"
        @click="handleConfirm"
        >保 存</el-button
      >
      <!-- 仅增加一项，再有其它需求，单独拆成父组件传btnList -->
      <el-button
        size="medium"
        v-if="customerBookFlag"
        :loading="formLoading"
        type="primary"
        @click="handleAddConfirm"
        >保存并预约</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
import uploadImg from "./small/uploadImg";
import editor from "../editor";

export default {
  name: "form_components",
  props: {
    size: { type: String, default: "small" },
    customClass: { type: String, default: "" },
    labelPosition: { type: String, default: "right" },
    direction: { type: String, default: "rtl" },
    drawerSize: { type: String, default: "610px" },
    labelWidth: { type: String },
    title: { type: String },
    totalWidth: { type: String },
    formStyle: { type: Object },
    customerBookFlag: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    inlineFlag: { type: Boolean, default: false },
    forms: { type: Array }, // 表单组,
    lists: { type: Array }, // 按钮组
    btnStyle: { type: String, default: "margin-top:30px;text-align:center" }
  },

  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },

  components: {
    uploadImg,
    editor
  },
  data() {
    const form = {};
    const { forms } = this.$props;
    forms.forEach(item => {
      if (!item.prop || item.hidden) return false;
      if (
        item.type === "daterange" ||
        item.type === "checkboxList" ||
        item.type === "checkbox" ||
        item.type === "uploadImg" ||
        item.type === "cascader" ||
        (item.type === "select" && item.multiple)
      ) {
        form[item.prop] = [];
      } else {
        form[item.prop] = "";
      }
    });
    return {
      form,
      formLoading: false,
      drawer: false
      // imgOrVideoFlag: true
      // loading: false,
    };
  },

  mounted() {},

  methods: {
    cancel() {
      // this.$emit("beforeClose");
      this.close();
    },

    beforeClose(e, v) {
      // this.$emit("beforeClose");
      this.close();
    },

    open() {
      this.drawer = true;
    },

    clickDrawerBody() {
      this.$emit("clickDrawer");
    },

    close() {
      this.drawer = false;
      this.handleReset();
    },

    showLoading() {
      this.formLoading = true;
    },

    hideLoading() {
      this.formLoading = false;
    },

    // 重置
    handleReset() {
      this.$refs.form.resetFields();
    },

    // 自定义render
    renderFun(h, { node, data, store }) {
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
        </span>
      );
    },

    treeSelect(obj, prop, params) {
      const ids = this.$refs.treeSelect[0].ids.concat([]);
      const arr = obj.data.concat([]);

      // for (let i = ids.length - 1; i >= 0; i--) {
      //   const temp = arr.filter((item) => item.id == ids[i]);
      //   if (temp.length > 0) {
      //     ids.splice(i, 1);
      //   }
      // }

      this.$refs.treeSelect[0].ids = ids;
      this.form[prop] = params.multiple ? ids : ids[0];

      this.$emit("changeTreeSelect", prop, this.form[prop]);
    },

    // 文件上传
    updateForm(list, prop, index) {
      this.$set(this.form, prop, list);
      // if (list.length > 0) {
      //   this.$refs.form.fields[index - 1].validateState = '';
      //   this.$refs.form.fields[index - 1].validateMessage = '';
      // }
      // this.$emit('changVal', list, prop, index);
    },

    // 设置分类
    getCheckList(list, prop) {
      this.$set(this.form, prop, list);
    },

    getEditorData(val, prop) {
      this.$set(this.form, prop, val);
      // console.log('富文本内容' + typeof val);
    },

    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit("handleConfirm", this.form);
        } else {
          this.$message.error("表单中存在部分字段不符合规则，请修改后重新提交");
        }
      });
    },

    handleAddConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit("handleConfirm", this.form, true);
        } else {
          this.$message.error("表单中存在部分字段不符合规则，请修改后重新提交");
        }
      });
    },

    // 表单赋值
    initforms(formEdit) {
      this.$nextTick(() => {
        const form = {};
        this.forms.forEach(item => {
          if (!item.prop || item.hidden) return false;

          if (item.type === "select-input" || item.type === "input-input") {
            form[item.prop1] = "";
            form[item.prop2] = "";
          }

          if (
            item.type === "daterange" ||
            item.type === "checkboxList" ||
            item.type === "checkbox" ||
            item.type === "uploadImg" ||
            item.type === "cascader" ||
            (item.type === "select" && item.multiple)
          ) {
            form[item.prop] = [];
          } else {
            form[item.prop] = "";
          }
        });
        if (formEdit) {
          this.form = Object.assign(form, formEdit);
        } else {
          this.form = Object.assign({}, form);
        }
        // this.loading = false;

        this.$nextTick(() => {
          if (this.$refs.form && this.$refs.form.clearValidate) {
            this.$refs.form.clearValidate();
            const drawerArr = document.getElementsByClassName("drawer-form-ck");
            const footer = document.getElementsByClassName("form-footer");
            footer[0].style.width = drawerArr[0].style.width;
          }
        });
      });
    },

    // 绑定部分值(此时表单已渲染)
    initfields(obj) {
      this.form = Object.assign(this.form, obj);
    },

    // 点击图标
    clickSvg(str) {
      this.$emit(str);
    }
  }
};
</script>

<style lang="scss">
.drawer-form-ck {
  .el-drawer__body {
    .el-form-self {
      margin: 0;
      overflow: hidden;
      zoom: 1;

      .el-input,
      .el-select,
      .el-cascader,
      .el-date-editor {
        width: 100%;
      }

      .form-svg {
        cursor: pointer;
      }

      .editorText {
        margin-top: -14px;
      }

      .ele-note {
        background: #f6f6f6;
        margin-top: 10px;
        padding: 8px 12px;
        line-height: 26px;
        color: #999999;
        font-size: 13px;
      }

      .el-form-item__label {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        text-align: right;
      }
    }

    .el-form-item--small .el-form-item__content,
    .el-form-item--small .el-form-item__label {
      line-height: 32px;
    }

    .el-form-item__label {
      padding: 0 6px 0 0 !important;
    }

    .el-date-editor {
      width: 420px !important;
    }

    .el-input-number {
      input {
        text-align: left;
      }
    }

    .part-title {
      width: 542px;
      height: 30px;
      background: rgba(51, 102, 255, 0.05);
      color: #303133;
      font-weight: 600;
      padding-left: 8px;

      .border {
        display: inline-block;
        width: 3px;
        margin-top: 8px;
        height: 14px;
        border-left: 3px solid #3366ff;
      }

      .title {
        display: inline-block;
        vertical-align: top;
        margin-left: 6px;
        height: 30px;
      }
    }
  }
}
</style>

<style lang="scss">
.drawer-form-ck {
  .el-drawer__header {
    background: #f8f9fb;
    padding: 0 16px;
    margin: 16px;
    height: 50px;
    font-family: PingFangSC-Medium;
    font-weight: 600;
    font-size: 18px;
    color: #303133;
    line-height: 50px;
  }

  .el-drawer__body {
    padding: 0px 24px 64px 36px;
  }

  .drawer-footer {
    text-align: right;
    z-index: 2001;
    padding: 16px;
    background-color: #fff;
    box-shadow: 0 -2px 8px 0 rgba(200, 201, 204, 0.3);
    bottom: 0;
    position: fixed;
    right: 0;
  }

  .el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: 1px solid #dfe4ed;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  .el-tabs__item {
    border: 0 !important;
  }

  .el-tabs__nav .el-tabs__item.is-active {
    border-bottom: 0 !important;
  }
}

.time-spinner {
  .el-time-spinner__list::before {
    height: 76px;
  }
}
</style>
