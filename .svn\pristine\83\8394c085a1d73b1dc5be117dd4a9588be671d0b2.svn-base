<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
      <el-row>
        <!-- <direct-search
          ref="form"
          :label-position="'right'"
          :form-style="{'text-align':'left','margin-bottom':'10px'}"
          :forms="searchList"
          @handleSearch="handleFormSearch"
        />-->
      </el-row>
      <!-- :tab-type="'index'"
      :tab-label="'序号'"-->
      <el-col style="padding:15px 0">
        <el-table-self
          :columns="columns"
          :current-page="pageIndex"
          :list-loading="listLoading"
          :table-data="dataList"
          :total-count="total"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :tab-index="tabIndex"
          @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange"
        />
      </el-col>

      <dialog-form
        ref="audit"
        :width="'560px'"
        :title="formTitle"
        :form-data="auditData"
        :form-edit="auditEdit"
        :footer-btn="footerBtn"
        @handleConfirm="handleConfirm"
      />
    </el-row>

    <el-dialog title="封禁" :visible.sync="dialogFormVisible" width="550px">
      <el-form :model="form" :rules="rules" ref="ruleForm" label-width="80px">
        <el-form-item label="封禁类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择封禁类型">
            <el-option label="账号" :value="1"></el-option>
            <el-option label="设备" :value="2"></el-option>
            <el-option label="ip" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="封禁理由" prop="reason">
          <!-- <el-input type="textarea" :rows="3" v-model="form.reason" autocomplete="off"></el-input> -->
          <el-select v-model="form.reason" multiple placeholder="请选择封禁理由">
            <el-option label="行为违规" :value="1"></el-option>
            <el-option label="头像违规" :value="2"></el-option>
            <el-option label="相册违规" :value="3"></el-option>
            <el-option label="动态违规" :value="4"></el-option>
            <el-option label="实锤违规" :value="5"></el-option>
            <el-option label="消息违规" :value="6"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirmStop()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="用户详情"
      :visible.sync="detailVisible"
      width="650px"
      top="3%"
      style="text-align:center"
      class="user-detail"
    >
      <el-row>
        <el-row v-for="(item,index) in detailList" :key="index" style="margin-top:25px">
          <el-col
            :span="item.span"
            v-for="itemObj in item.list"
            :key="itemObj.value"
            style="text-align:left"
          >
            <div class="grid-content bg-purple">
              <span>{{itemObj.label}}:</span>
              <!-- {{ itemObj.formatter?itemObj.formatter( detailObj[itemObj.value]):detailObj[itemObj.value] }} -->
              <span>{{ itemObj.formatter?itemObj.formatter(detailObj[itemObj.value]):detailObj[itemObj.value]?detailObj[itemObj.value]:'-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row style="text-align:left;margin-top:25px">
          <el-col style="margin-bottom:10px">照片资料</el-col>
          <el-col>
            <div style="display:inline-block">
              <span style="vertical-align: top;">头像：</span>
              <img-preview
                size="mini"
                :removeEnabled="false"
                style="display:inline-block"
                :uploadEnabled="false"
                :fileList="detailObj.avatar"
              ></img-preview>
            </div>
            <div
              v-if="detailObj.authPic && detailObj.authPic.length>0 && detailObj.authPic[0]"
              style="display:inline-block;margin-left:80px"
            >
              <span style="vertical-align: top;">认证：</span>
              <img-preview
                size="mini"
                style="display:inline-block"
                :removeEnabled="false"
                :uploadEnabled="false"
                :fileList="detailObj.authPic"
              ></img-preview>
            </div>
          </el-col>
        </el-row>

        <el-row style="text-align:left;margin-top:25px">
          <el-col style="margin-bottom:10px">照片墙</el-col>
          <el-col>
            <img-preview
              size="mini"
              :removeEnabled="false"
              style="display:inline-block"
              :uploadEnabled="false"
              :fileList="detailObj.photoList"
            ></img-preview>
          </el-col>
        </el-row>

        <!-- <el-row style="text-align:left;margin-top:25px">
          <el-col style="margin-bottom:10px">动态相关</el-col>
          <el-col>
            <img-preview
              size="mini"
              :removeEnabled="false"
              style="display:inline-block"
              :uploadEnabled="false"
              :fileList="detailObj.avatar"
            ></img-preview>
            <img-preview
              size="mini"
              style="display:inline-block"
              :removeEnabled="false"
              :uploadEnabled="false"
              :fileList="detailObj.authPic"
            ></img-preview>
          </el-col>
        </el-row>-->
      </el-row>
    </el-dialog>
  </el-row>
</template>

<script>
import dialogForm from '@/components/DialogComponents/Form'
import imgPreview from '@/components/UploadExcel/imgPreview'
import elTableSelf from '@/components/TabComponents/index'
import directSearch from '@/components/FormComponents/directSearch'
import paginationMixin from '@/components/TabComponents/mixin'
import {
  getReportList,
  userViolations,
  joinBlack,
  getUserPhotoList,
  updateReportStatus
} from '@/api/user'
import date from '@/utils/date'

export default {
  components: {
    elTableSelf,
    imgPreview,
    dialogForm,
    directSearch
  },
  mixins: [paginationMixin],
  data() {
    return {
      detailObj: {},
      detailList: [
        {
          span: 8,
          list: [
            {
              label: '昵称',
              value: 'name'
            },
            {
              label: '性别',
              value: 'sex'
            },
            {
              label: '职业',
              value: 'occupation'
            }
          ]
        },
        {
          span: 8,
          list: [
            {
              label: '手机号',
              value: 'mobile'
            },
            {
              label: 'ID号',
              value: 'id'
            },
            {
              label: '居住地',
              value: 'liveAddress'
            }
          ]
        },
        {
          span: 12,
          list: [
            {
              label: '注册时间',
              value: 'registerTime',
              formatter(val) {
                return date.dateFormat(val, 'YYYY-MM-DD hh:mm:ss')
              }
            },
            {
              label: '最后登录时间',
              value: 'lastUpdateTime',
              formatter(val) {
                return date.dateFormat(val, 'YYYY-MM-DD hh:mm:ss')
              }
            }
          ]
        },
        {
          span: 24,
          list: [
            {
              label: '个人介绍',
              value: 'introduction'
            }
          ]
        }
      ],
      rules: {
        type: [
          { required: true, message: '请选择封禁类型', trigger: 'change' }
        ],
        reason: [{ required: true, message: '请输入理由', trigger: 'blur' }]
      },
      formTitle: '',
      detailVisible: false,
      dialogFormVisible: false,
      listLoading: false,
      total: 0,
      id: 0,
      form: {
        type: '',
        reason: ''
      },
      uploadObj: {},
      auditEdit: null,
      cacheForm: {},
      auditData: [
        {
          type: 'input',
          name: '用户id',
          field: 'id',
          disabled: true
        },

        {
          type: 'input',
          name: '用户昵称',
          field: 'name',
          disabled: true
        },
        {
          type: 'input',
          name: '手机号',
          field: 'mobile',
          disabled: true
        },

        {
          type: 'input',
          name: '职业',
          field: 'occupation',
          disabled: true
        },
        {
          type: 'input',
          name: '居住地',
          field: 'liveAddress',
          disabled: true
        },
        {
          type: 'input',
          name: '介绍',
          field: 'introduction',
          disabled: true
        },
        {
          type: 'input',
          name: '注册时间',
          field: 'registerTime',
          disabled: true
        },
        {
          name: '头像',
          type: 'img',
          field: 'avatar',
          disabled: true
        },
        {
          name: '认证图片',
          type: 'img',
          field: 'authPic',
          disabled: true
        },
        {
          name: '照片墙',
          type: 'img',
          field: 'photoList',
          disabled: true
        },
        {
          type: 'input',
          name: '性别',
          field: 'sex',
          disabled: true
        }
      ],
      footerBtn: [
        {
          value: 1,
          name: '昵称违规'
        },
        {
          value: 2,
          name: '头像违规'
        },
        {
          value: 3,
          name: '个性签名违规'
        }
      ],
      fileList: [],
      dataList: [],
      columns: [
        {
          value: 'typeId',
          width: 110,
          label: '类型id'
        },
        {
          value: 'type',
          width: 90,
          label: '类型',
          // 1 动态 2 评论 3 活动 4 场景 5 用户信息 6 群 7 聊天
          formatter(row) {
            return row.type == '1'
              ? '动态'
              : row.type == '2'
              ? '评论'
              : row.type == '3'
              ? '活动'
              : row.type == '4'
              ? '场景'
              : row.type == '5'
              ? '用户信息'
              : row.type == '6'
              ? '群'
              : row.type == '7'
              ? '聊天'
              : ''
          }
        },
        {
          value: 'status',
          label: '状态',
          // 0 未受理 1 受理
          formatter(row) {
            return row.type == '0' ? '未受理' : row.type == '1' ? '受理' : ''
          }
        },
        {
          value: 'userId',
          width: 100,
          className: 'openUserInfo',
          func: this.openUserInfo,
          label: '举报人id'
        },
        {
          value: 'textDesc',
          label: '举报内容'
        },

        {
          value: 'reportType',
          label: '举报类型',
          // 举报类型 0 色情暴力 1 人身攻击 2 广告骚扰 3 违法内容 4 侵犯著作权 5 其他问题
          formatter(row) {
            return row.reportType == '1'
              ? '人身攻击'
              : row.reportType == '2'
              ? '广告骚扰'
              : row.reportType == '3'
              ? '违法内容'
              : row.reportType == '4'
              ? '侵犯著作权'
              : row.reportType == '5'
              ? '其他问题'
              : row.reportType == '0'
              ? '色情暴力'
              : ''
          }
        },

        {
          value: 'pic',
          type: 'img',
          label: '图片集合'
        },
        {
          value: 'createTime',
          label: '举报时间',
          width: 170,
          formatter(row) {
            return date.dateFormat(row.createTime, 'YYYY-MM-DD hh:mm:ss')
          }
        },
        {
          value: 'lastUpdateTime',
          label: '最后登录时间',
          formatter(row) {
            return row.lastUpdateTime
              ? date.dateFormat(row.lastUpdateTime, 'YYYY-MM-DD hh:mm:ss')
              : ''
          },
          width: 170
        },
        {
          value: 'reportUserName',
          label: '被举报人昵称'
        },
        {
          value: 'name',
          label: '举报人昵称'
        },
        {
          label: '操作',
          fixed: 'right',
          width: 100,
          operType: 'button',
          operations: [
            {
              label: '处理',
              type: 'primary',
              func: this.handleUpdate
            }
          ]
        }
        // {
        //   label: '操作',
        //   fixed: 'right',
        //   width: 160,
        //   operType: 'button',
        //   operations: [
        //     {
        //       label: '详情',
        //       type: 'primary',
        //       func: this.handleView
        //     },
        //     {
        //       label: '封禁',
        //       type: 'danger',
        //       func: this.handleDelete
        //     }
        //   ]
        // }
      ],
      searchList: [
        {
          label: 'ID',
          labelWidth: '120px',
          type: 'input',
          prop: 'id'
        },
        {
          label: '昵称',
          labelWidth: '120px',
          type: 'input',
          prop: 'name'
        },
        {
          label: '手机号',
          labelWidth: '120px',
          type: 'input',
          prop: 'mobile'
        },

        {
          label: '来源渠道',
          labelWidth: '120px',
          type: 'select',
          opts: [
            {
              label: 'ios',
              value: '1'
            },
            {
              label: '华为',
              value: '2'
            },
            {
              label: 'oppo',
              value: '3'
            },
            {
              label: 'vivo',
              value: '4'
            },
            {
              label: '小米',
              value: '5'
            },
            {
              label: '其他',
              value: '6'
            }
          ],
          prop: 'originType'
        },
        {
          label: '性别',
          labelWidth: '120px',
          type: 'select',
          opts: [
            {
              label: '男',
              value: '1'
            },
            {
              label: '女',
              value: '2'
            }
          ],
          prop: 'sex'
        },
        {
          label: '会员状态',
          labelWidth: '120px',
          type: 'select',
          opts: [
            {
              label: '是',
              value: '1'
            },
            {
              label: '否',
              value: '0'
            }
          ],
          prop: 'vipFlag'
        },
        {
          label: '真人认证',
          labelWidth: '120px',
          type: 'select',
          opts: [
            {
              label: '未认证',
              value: '0'
            },
            {
              label: '认证',
              value: '1'
            }
          ],
          prop: 'authFlag'
        },
        {
          label: '账号状态',
          labelWidth: '120px',
          type: 'select',
          opts: [
            {
              label: '正常',
              value: '1'
            },
            {
              label: '异常',
              value: '2'
            }
          ],
          prop: 'black'
        },
        {
          label: '渠道码',
          labelWidth: '120px',
          type: 'input',
          prop: 'channelCode'
        },
        {
          label: '时间',
          labelWidth: '120px',
          type: 'daterange',
          options: {
            shortcuts: [
              {
                text: '最近一周',
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                  picker.$emit('pick', [start, end])
                }
              },
              {
                text: '最近一个月',
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                  picker.$emit('pick', [start, end])
                }
              },
              {
                text: '最近三个月',
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                  picker.$emit('pick', [start, end])
                }
              }
            ]
          },
          valueFormat: 'yyyy-MM-dd',
          prop: 'timeArr'
        },
        {
          label: '',
          labelWidth: '65px',
          type: 'button',
          func: this.handleFormSearch,
          value: '查询',
          color: 'primary'
        }
      ]
    }
  },
  watch: {},
  mounted() {
    this.handleSearch()
  },
  methods: {
    openUserInfo(row) {
      this.$store.dispatch('user/setUserInfoId', row.userId)
      this.$store.dispatch('user/setDetailVisible', true)
    },
    handleConfirmStop() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          joinBlack(this.form).then(res => {
            if (res.code == 200) {
              this.$message.success('操作成功！')
              this.dialogFormVisible = false
              this.handleSearch()
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleConfirm(row, val) {
      if (val) {
        let data = {
          userId: this.auditEdit.id,
          type: val
        }
        userViolations(data).then(res => {
          if (res.code == 200) {
            this.$message.success('修改成功！')
            this.handleSearch()
          }
        })
      }
    },
    handleUpdate(row) {
      this.$confirm(`确认已处理【${row.name}】的举报？`, '提示', {
        type: 'warning'
      })
        .then(() => {
          updateReportStatus().then(res => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.handleSearch()
            }
          })
        })
        .catch(() => {})
    },
    handleView(row) {
      this.formTitle = '详情'
      let data = { userId: row.id }
      this.auditEdit = {}
      let arr = []
      getUserPhotoList(data).then(res => {
        if (res.code == 200) {
          let d = res.data
          if (d.length > 0) {
            d.forEach(currentItem => {
              arr.push(currentItem.imgUrl)
            })
          }
        }
        this.auditEdit = Object.assign({}, row)
        this.detailObj = Object.assign({ photoList: arr }, this.auditEdit)
        console.log(this.detailObj)
        this.detailVisible = true
      })

      // this.$refs.audit.open()
    },
    handleFormSearch(form) {
      this.pageIndex = 1
      this.handleSearch(form)
    },

    tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize
    },

    handleSearch(form) {
      this.cacheForm = this.cacheForm || form
      const params = Object.assign(this.cacheForm, form)
      params.page = this.pageIndex
      params.limit = this.pageSize
      params.status = 1
      // params.name = '昵称22'
      if (form && form.timeArr.length == 2) {
        params.startTime = form.timeArr[0]
        params.endTime = form.timeArr[1]
      }
      getReportList(params).then(res => {
        if (res.code == 200) {
          this.total = res.count
          this.dataList = res.data
          if (this.dataList.length > 0) {
            this.dataList.forEach(item => {
              console.log(item.authPic)
              item.avatar = item.avatar ? [].push(item.avatar) : []
              item.authPic = item.authPic ? [].push(item.authPic) : []
            })

            console.log(this.dataList)
          }
        }
        this.listLoading = false
      })
    },

    // 删除
    handleDelete(row) {
      this.dialogFormVisible = true
      this.form.userId = row.id
      this.form.type = ''
      this.form.reason = []
      // this.$confirm(`确认将【${row.profName}】拉黑？`, '提示', {
      //   type: 'warning'
      // })
      //   .then(() => {
      //     const data = { id: row.id }
      //     deleteProf(data).then(res => {
      //       if (res.code === 1) {
      //         this.$message.success(res.msg)
      //         this.handleSearch()
      //       }
      //     })
      //   })
      //   .catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.page-container .page-main {
  padding-top: 30px 25px;
}
.el-select {
  display: inline-block;
  position: relative;
  width: 100%;
}
.user-detail {
  .el-dialog__body {
    padding-top: 0px;
  }
}
.openUserInfo {
  color: #1890ff;
  cursor: pointer;
  width: 170px !important;
}
</style>
