import {
    fetch,
    post,
    put,
    deleteHttp
  } from '@/utils/request'
  


  // 查询部门
  export const getDeptName = data => {
    return fetch(`/user/groups/findDeptList`,data)
  }

    // 根据员工姓名模糊查询员工信息
    export const getUserName = data => {
      return fetch(`/users/userName`,data)
    }

    
      // 添加
  export const addPayroll = data => {
    return post(`/payroll`, data)
  }


  //上传文件
  export const uploadFile = data => {
    return post(`/attachment/file/uploads`, data)
  }



   //合同附件查询列表
   export const htPage = data => {
    return fetch(`/attachment`, data)
  }

  
    // 查询详情
    export const getById = data => {
      return fetch(`/payroll/${data.id}`, data)
    }

    // 更新
  export const updatePayroll = data => {
    return put(`/payroll`, data)
  }

    //获取应出勤天数
    export const getAttendanceDay = data => {
      return fetch(`/data/dict/getAttendanceDay`,data)
    }

    // 目标达成表上传
    export const uploadTargetFile = data => {
      return post(`/target/file/upload`, data)
    }

    // 目标达成表下载模板
    export const getTargetTemplate = data => {
      return fetch(`/target/file/downExcel`, data, 'blob')
    }