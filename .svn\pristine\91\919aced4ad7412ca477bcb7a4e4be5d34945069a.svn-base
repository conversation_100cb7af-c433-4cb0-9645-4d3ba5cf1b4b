<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
    <el-tabs>
      <el-tab-pane :label="$route.query.id ? '修改工资单' : '工资单列表'">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self 
           :columns="columns" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>

      <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true"
        :label-width="'90'" :title="drawTitle"></draw-form>
      </el-tab-pane>

      <el-tab-pane
          label="批量导入"
          name="second"
        
        >
          <span style="color:red">*</span>批量导入
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              :on-change="uploadByJsqd"
              :file-list="fileList">
              <el-button type="plain" style="margin-left:10px">选择文件</el-button>
            </el-upload>
          </div>

          <div>
            <el-link
              type="primary"
              @click="getTemplateData"
              style="margin-top:10px;font-size: 16px;"
              >下载模板</el-link>
          </div>
      </el-tab-pane>

      </el-tabs>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import {findPage,getPayrollTemplate,uploadInfo,exporPayroll} from "@/views/payroll/api";


export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
        total: 0,
        id: 0,
        drawTitle: "",
        drawForms: [],
        dataList: [],
        fileList: [],
        columns: [
          {
            value: "orderNo",
            label: "序号"
          },
          {
            value: "userName",
            label: "姓名"
          },
          {
            value: "years",
            label: "年-月"
          },
          {
            value: "deptName",
            label: "部门"
          },
          // {
          //   value: "droleName",
          //   label: "岗位"
          // },
          {
            value: "type",
            label: "所属分公司",
            formatter(row) {
                return row.type == '0' 
                ? "全部" 
                : row.type == '1' 
                ? "总部" 
                : row.type == '2' 
                ? "蚌埠分公司" 
                : row.type == '3' 
                ? "合肥分公司" 
                : row.type == '4' 
                ? "常州分公司" 
                : "";
            }
          },
          {
            value: "levelCodePM",
            label: "职级"
          },
          {
            value: "salaryLevel",
            label: "薪档"
          },
          {
            value: "idNum",
            label: "身份证号码"
          },
          {
          value: "isRegular",
          label: "是否转正",
          formatter(row) {
            return row.isRegular == 1 ? "是" : row.isRegular == 2 ? "否" : "";
          }
        },
        {
            value: "entryTime",
            label: "入职日期",
            formatter(row) {
              return date.dateFormat(row.entryTime, "YYYY-MM-DD");
            }
          },
          {
          value: "disabled",
          label: "是否在职",
          formatter(row) {
            return row.disabled == 1 ? "离职" : row.disabled == 0 ? "在职" : "";
          }
        },
          {
            value: "attendanceDay",
            label: "应出勤天数"
          },
          {
            value: "actualDay",
            label: "实际出勤天数"
          },
          {
            value: "restDay",
            label: "调休天数"
          },
          {
            value: "sickDay",
            label: "病假天数"
          },
          {
            value: "absenceDay",
            label: "病假天数"
          },
          {
            value: "absenceDay",
            label: "病假天数"
          },
          {
            value: "notworkDay",
            label: "旷工天数"
          },
          {
            value: "sickRate",
            label: "病假系数"
          },
          {
            value: "sickAdd",
            label: "病假增补"
          },
          {
            value: "notworkRate",
            label: "旷工系数"
          },
          {
            value: "notworkCut",
            label: "旷工扣减"
          },
          {
            value: "levelSalary",
            label: "基础工资"
          },
          {
            value: "postSalary",
            label: "岗位工资"
          },
          {
            value: "actualSalary",
            label: "实发"
          },
          
          {
            value: "performanceBase",
            label: "绩效基数"
          },
          {
            value: "scoreResult",
            label: "评分结果（分）"
          },
          {
            value: "valuationPerformanceActual",
            label: "评价绩效实发"
          },
          {
            value: "addPercentage",
            label: "新增提成"
          },
          {
            value: "renewPercentage",
            label: "续费提成"
          },
          {
            value: "otherPercentage",
            label: "其他提成"
          },
          {
            value: "otherPercentageDesc",
            label: "其他提成说明"
          },
          {
            value: "actualPercentage",
            label: "提成实发"
          },
          {
            value: "telephoneSalary",
            label: "电话补贴"
          },
          {
            value: "trafficSalary",
            label: "交通补贴"
          },
          {
            value: "mealSalary",
            label: "餐补"
          },
          {
            value: "otherActual",
            label: "其他实发"
          },
          {
            value: "otherDesc",
            label: "其他项说明"
          },
          {
            value: "subsidyActual",
            label: "补贴实发"
          },
          {
            value: "fullSalary",
            label: "满勤奖"
          },
          {
            value: "workAge",
            label: "工龄"
          },
          {
            value: "certificateSubsidy",
            label: "证书补贴"
          },
          {
            value: "otherSalary",
            label: "其他奖金"
          },
          {
            value: "otherSalaryDesc",
            label: "其他奖金说明"
          },
          {
            value: "incentiveBonusSum",
            label: "激励奖金合计"
          },
          {
            value: "otherCut",
            label: "其他扣减（-）"
          },
          {
            value: "otherCutDesc",
            label: "其他扣减说明"
          },          
          {
            value: "socialSecurityCut",
            label: "社保扣减（-）"
          },
          {
            value: "personalTaxCut",
            label: "个税扣减(-)"
          },
          {
            value: "payeeName",
            label: "收款人姓名"
          },

          {
            value: "bankNo",
            label: "工资卡号"
          },

          {
            value: "bank",
            label: "开户行"
          },
          {
            value: "allPreTaxSum",
            label: "所有应发税前总额（+）"
          },

          {
            value: "socialTaxCut",
            label: "社保、个税扣减合计（-）"
          },

          {
            value: "afterTaxSalary",
            label: "税后应发金额"
          },
          {
            value: "createdAt",
            label: "创建时间",
            width: 180,
            formatter(row) {
              return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          // {
          //   label: "操作",
          //   fixed: "right",
          //   width: 180,
          //   operType: "button",
          //   operations: [
          //     {
          //       label: "编辑",
          //       type: "primary",
          //       func: this.handleEdit,
          //     },
          //     {
          //       label: "删除",
          //       type: "danger",
          //       func: this.handleDelete,
                
          //     }
          //   ]
          // }

        ],

      searchList: [
        {
          label: "姓名",
          labelWidth: "80px",
          type: "input",
          prop: "userName"
        },
        {
          label: "年-月",
          labelWidth: "80px",
          type: "date",
          valueFormat: "yyyy-MM",
          dateType: "month",
          placeholder: "请选择年月",
          prop: "years"
        },
        {
          label: "所属分公司",
          labelWidth: "100px",
          type: "select",
          opts: [
            {
              label: "全部",
              value: '0'
            },
            {
              label: "总部",
              value: '1'
            },
            {
              label: "蚌埠分公司",
              value: '2'
            },
            {
              label: "合肥分公司",
              value: '3'
            },
            {
              label: "常州分公司",
              value: '4'
            },
          ],
          prop: "type"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
           
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "导出",
              icon: "el-icon-download",
              func: this.handleExport
            }
          ]
        }
      ]
    };

  },
  watch: {},
  mounted() {
    this.handleSearch();
  },
  methods: {
  
    close() {
        this.initForm = {};
        this.$refs.drawForm.close();
      },

      handleFormSearch(form) {
        this.pageIndex = 1;
        this.handleSearch(form);
      },

      tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

      handleSearch(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;
        this.tableLoading = true;
        findPage(params).then(res => {
          if (res.code == 200) {
            this.total = res.data.total;
            this.dataList = res.data.list;
          }
          this.tableLoading = false;
        });
      },


      getTemplateData() {
        getPayrollTemplate()
        .then(res => {
          const blob = new Blob([res]);
          const elink = document.createElement("a");
          elink.download = "import_payroll" + ".xlsx";
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        })
        .catch(error => {
          console.log(error);
        });
    },


      
     //文件发生改变就会触发的事件
     uploadByJsqd(file) {
      debugger
      //判断是否符合beforeAvatarUpload方法中的条件
      if (this.beforeAvatarUpload(file)) {
        this.fileList.name = file.name;
        this.fileList.url = "";
        var formdata = new FormData();
        formdata.append("file", file.raw);
        //importDevice：请求接口 formdata：传递参数
        uploadInfo(formdata).then(res => {
          if (res.code == 200) {
            this.$message.success("上传成功");
            this.importFile = "";
            this.fileList = [];
          }
        });
      }
    },

       //文件校验方法
       beforeAvatarUpload(file) {
      // 通过split方法和fileArr方法获取到文件的后缀名
      let fileArr = file.name.split(".");
      let suffix = fileArr[fileArr.length - 1];
      //只能导入.xls和.xlsx文件
      if (!/(xls|xlsx)/i.test(suffix)) {
        this.$message("文件格式不正确");
        return false;
      }
      //不能导入大小超过2Mb的文件
      if (file.size > 3 * 1024 * 1024) {
        this.$message("文件过大，请上传小于2MB的文件〜");
        return false;
      }
      return true;
    },


    handleExport() {
      let params = Object.assign({}, this.$refs.form.form);
      exporPayroll(params)
        .then(res => {
          const blob = new Blob([res]);
          const elink = document.createElement("a");
          elink.download = "工资表" + date.dateFormat() + ".xlsx";
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        })
        .catch(error => {
          console.log(error);
        });
    },


  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>