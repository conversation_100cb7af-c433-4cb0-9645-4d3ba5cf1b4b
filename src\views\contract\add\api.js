import {
    fetch,
    post,
    put,
    deleteHttp
  } from '@/utils/request'
  
  // 查询列表
  export const queryPage = data => {
    return fetch(`/contract`, data)
  }

  // 添加
export const addContract = data => {
  return post(`/contract`, data)
}

// 更新
export const updateContract = data => {
  return put(`/contract`, data)
}

  
  // 查询明细
  export const getContractById = data => {
    return fetch(`/contract/${data.id}`)
  }

  
  // 删除
  export const deletetSalary = data => {
    return deleteHttp(`/contract/${data.id}`)
  }


    
  // 查询所有员工
  export const getUserName = data => {
    return fetch(`/users/getUsers`,data)
  }


  //上传合同
  export const uploadFile = data => {
    return post(`/attachment/file/upload`, data)
  }



   //合同附件查询列表
   export const htPage = data => {
    return fetch(`/attachment`, data)
  }