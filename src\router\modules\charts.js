/** When your routing table is too long, you can split it into small modules**/
import Layout from '@/layout'

const chartsRouter = [{
  path: '/charts',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Charts',
  meta: {
    title: 'Charts',
    icon: 'chart'
  },
  children: [{
    path: 'test1',
    component: () => import('@/views/test1/index'),
    name: 'test1',
    meta: {
      title: '测试1',
      noCache: true
    }
  }, ]
}]

export default chartsRouter
