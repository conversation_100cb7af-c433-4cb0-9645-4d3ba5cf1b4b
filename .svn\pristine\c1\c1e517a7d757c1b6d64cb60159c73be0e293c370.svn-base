<template>
  <el-row class="page-container">
    <el-row class="page-main" v-loading="loading">
      <el-tabs>
       <el-tab-pane :label="$route.query.id ? '修改工资' : '新建工资'">
      <el-form ref="form" :model="form" label-width="232px" :inline="true">
        <el-divider content-position="left">工资表信息</el-divider>
        <template v-for="(item, index) in formList">
          <el-form-item
            :key="index"
            :style="item.style"
            :class="item.itemClass ? item.itemClass : ''"
            :label="item.label ? item.label + (item.hiddenColon ? '' : '：') : ''"
            :prop="item.prop"
            :rules="item.rules"
          >
          
            <template v-if="item.type === 'input'">
          
              <!-- 输入框 -->
              <el-input
                  :class="item.class ? item.class : 'el-input--small-8'"
                  :id="item.prop ? item.prop : ''"
                  :value="form[item.prop]"
                  :minlength="item.minlength"
                  :maxlength="item.maxlength"
                  :readonly="item.readonly"
                  :disabled="item.disabled"
                  :placeholder="item.placeholder"
                  autocomplete="off"
                  v-model.trim="form[item.prop]"
                  @focus="item.focusFunc ? item.focusFunc($event) : {}"
                
                  @change="showOtherChange"
                  @blur="item.blurFunc ? item.blurFunc($event) : {}"
                 
                >
                  <span v-if="item.count" slot="suffix">{{
                    (form[item.prop] ? form[item.prop].length : 0) +
                      "/" +
                      item.maxlength
                  }}</span>
                  <span
                    v-if="item.unit"
                    :slot="item.slot ? item.slot : 'append'"
                    >{{ item.unit }}</span
                  >
                </el-input>

              <div
                v-if="item.note"
                :class="[
                  item.class ? item.class : 'el-input--small-8',
                  'ele-note'
                ]"
                v-html="item.note"
              ></div>
            </template>

       

            <template v-if="item.label==='员工姓名'">
              <el-button type="primary" style="margin-left: 0px"  @click=" searchUserInfo">查询</el-button>        
            </template>
          
            <template v-if="item.type === 'textarea'">
              <el-input
                :class="item.class"
                :style="item.itemStyle"
                @change="item.func ? item.func($event) : {}"
                :resize="item.resize ? item.resize : 'none'"
                v-model.trim="form[item.prop]"
                type="textarea"
                :disabled="item.disabled"
                :placeholder="item.placeholder"
                :rows="item.rows"
                :autosize="item.autosize"
                :minlength="item.minlength"
                :maxlength="item.maxlength"
                :show-word-limit="item.showLimit"
              />

              <div
                v-if="item.note"
                :class="[
                  item.class ? item.class : 'el-input--small-8',
                  'ele-note'
                ]"
                v-html="item.note"
              ></div>
            </template>

         

            <!-- 日期 -->
            <el-date-picker
              v-else-if="item.type === 'date'"
              v-model="form[item.prop]"
              :type="item.dateType ? item.dateType : 'date'"
              :picker-options="item.options"
              :format="item.format"
              :value-format="item.valueFormat"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleChange"
            />

            <!-- 下拉框 -->
             <template v-else-if="item.type === 'select'">
              <el-select
                v-model="form[item.prop]"
                :class="item.class ? item.class : 'el-input--small-8'"
                :clearable="item.clearable || true"
                :multiple="item.multiple || false"
                :filterable="item.filterable || true"
                :disabled="item.disabled"
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                @change="item.func ? item.func($event) : {}"
                @remove-tag="item.removeFunc ? item.removeFunc($event) : {}"
              >
                <el-option
                  v-for="(opt, optIndex) in item.opts"
                  :key="optIndex"
                  :disabled="opt.disabled"
                  :label="item.isSelect ? opt[item.diyLabel] : opt.label"
                  :value="item.isSelect ? opt[item.diyValue] : opt.value"
                />
              </el-select>
              <div
                v-if="item.note"
                :class="[
                  item.class ? item.class : 'el-input--small-8',
                  'ele-note'
                ]"
                v-html="item.note"
              ></div>
            </template>

          </el-form-item>


        </template>

         <!-- <el-divider content-position="left">工资单、考勤原件文件上传</el-divider>
          <el-row>
            <el-form-item label="">
              <div class="import-container">
                <el-upload
                  action=""
                  :auto-upload="false"
                  :multiple="false"
                  class="upload-demo"
                  :limit="1"
                  :on-change="uploadFileData"
                  :file-list="form.fileList"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                   附件上传Excel格式，仅限上传单个文件且上传大小不超过50M
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-row>  -->

        <el-row class="btn-operate" style="text-align: center">
          <el-form-item>
            <el-button type="primary" @click="save">保存</el-button>
            <el-button @click="goback">返回</el-button>
          </el-form-item>
        </el-row>
      </el-form>
       </el-tab-pane>


       <el-tab-pane label="企业微信考勤原件、绩效提成明细上传" name="second">
        <span style="color:red">*</span>文件请上传Excel格式，仅限上传单个文件且上传大小不超过50M
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              :on-change="uploadByJsqd"
              :file-list="fileList">
              <el-button type="primary" style="margin-left:50px">选择文件</el-button>
            </el-upload>
          </div>
        </el-tab-pane>

       <!-- <el-tab-pane
          label="工资单、考勤原件文件上传"  name="second">
          
          <div class="import-container">
            <el-upload
                  action=""
                  :auto-upload="false"
                  :multiple="false"
                  class="upload-demo"
                  :limit="1"
                  :on-change="uploadFileData"
                  :file-list="form.fileList"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                   附件上传Excel格式，仅限上传单个文件且上传大小不超过50M
                  </div>
                </el-upload>
          </div>

           <div>
            <el-link
              type="primary"
              @click="getTemplateData"
              style="margin-top:10px;font-size: 16px;"
              >工资单模板下载</el-link>
          </div> 
      </el-tab-pane> -->

    </el-tabs>

    <el-dialog
      title="员工信息"
      :visible.sync="dialogTableVisible"
      :append-to-body="true"
      width="80%"
    >
      <el-table :data="gridData" border>
        <el-table-column
          align="center"
           width="200"
          property="name"
          label="员工姓名"
        ></el-table-column>
        <el-table-column
          align="center"
           width="200"
          property="id"
          label="员工ID"
        ></el-table-column>
        <el-table-column
          align="center"
          property="levelCodePm"
          label="职级（P/PM)"
        ></el-table-column>
        <el-table-column
          align="center"
          property="salaryLevel"
          label="薪档"
        ></el-table-column>
        <el-table-column
          align="center"
           width="180"
          property="groupName"
          label="部门"
        ></el-table-column>
        <el-table-column
          align="center"
           width="180"
          property="roleName"
          label="岗位"
        ></el-table-column>
        <el-table-column
          property="idNum"
          align="center"
          width="180"
          label="身份证"
        ></el-table-column>
        <el-table-column
          property="isRegular"
          align="center"
          width="180"
          label="是否转正"
        ></el-table-column>
        <el-table-column
          align="center"
          property="entryTime"
          label="入职日期"
        ></el-table-column>
        <el-table-column
          align="center"
          property="isJob"
          label="是否在职"
        ></el-table-column>
        <el-table-column
          align="center"
          property="bankNoName"
          label="收款人姓名"
        ></el-table-column>
        <el-table-column
          align="center"
          property="bankNo"
          label="工资卡"
        ></el-table-column>
        <el-table-column
          align="center"
          property="bank"
          label="开户行"
        ></el-table-column>
        <el-table-column align="center" fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="selectUserInfo(scope.row)"
              type="text"
              size="small"
              >选择</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    </el-row>
  </el-row>


</template>

<script>
import { addPayroll,updatePayroll, getById,uploadFile,getUserName,getAttendanceDay} from "./api";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters(["userInfo"])
  },
  data() {
    return {
      dialogTableVisible: false,
      gridData: [],
      showGlobalComponent: true,
      importFile: "",
      fileList: [], //上传的文件列表
      loading: false,
      activeName: "first",
      form: {
        cname: "",
        uname: "",
        fileList: [],
        userName:"",
        sickRate:"80%",
        notworkRate:"300%",
        attendanceDay:"",
        salaryLevel:"",
        otherPercentage:"",
        flagType:"",//区分是新增还是修改标识
        

      },
    
      formList: [
      {
          type: "input",
          label: "员工姓名",
          prop: "name",
          placeholder: "请填写姓名",
          rules: [
            {
              required: true,
              message: "请填写姓名",
              trigger: "blur"
            }
          ]
        },
        // {
        //   type: "input",
        //   label: "员工ID",
        //   prop: "userId",
        //   disabled: true, 
        //   // rules: [
        //   //   {
        //   //     required: true,
        //   //     message: "请填写姓名",
        //   //     trigger: "blur"
        //   //   }
        //   // ]
        // },
        {
          label: "工资年月份",
          // labelWidth: "80px",
          type: "date",
          valueFormat: "yyyy-MM",
          dateType: "month",
          placeholder: "请选择年月",
          prop: "years",
          rules: [
            {
              required: true,
              message: "请填写工资年月份",
              trigger: "blur"
            }
          ]
        },
        // {
        //   type: "date",
        //   label: "工资年月份",
        //   prop: "years",
        //   placeholder: "请填写工资年月份",
        //   valueFormat: "yyyy-MM",
        //   rules: [
        //     {
        //       required: true,
        //       message: "请填写工资年月份",
        //       trigger: "blur"
        //     }
        //   ]
        // },
        // {
        //   label: "员工姓名",
        //   type: "select",
        //   dataOrigin: "name",
        //   diyLabel: "name",
        //   diyValue: "id",
        //   isSelect: true,
        //   seniorSelect: true,
        //   prop: "userId",
        //   opts: [],
        //   rules: [
        //     {
        //       required: true,
        //       message: "请填写员工姓名",
        //       trigger: "blur"
        //     }
        //   ]
        // },
        
        // {
        //   label: "部门",
        //   type: "select",
        //   diyLabel: "name",
        //   diyValue: "id",
        //   isSelect: true,
        //   prop: "groupId",
        //   dataOrigin: "deptName",
        //   opts: [],
        //   rules: [
        //     {
        //       required: true,
        //       message: "请选择部门",
        //       trigger: "change"
        //     }
        //   ]
        // },
        // {
        //   label: "岗位角色",
        //   type: "select",
        //   diyLabel: "name",
        //   diyValue: "id",
        //   isSelect: true,
        //   prop: "roleId",
        //   dataOrigin: "roleName",
        //   rules: [
        //     {
        //       required: true,
        //       message: "请填写岗位角色",
        //       trigger: "blur"
        //     }
        //   ],
        //   opts: []
        // }, 
        {
          type: "input",
          label: "职级（P/PM）",
          prop: "levelCodePM",
          // placeholder: "请填写职级",
          disabled: true, 
          // rules: [
          //   {
          //     required: true,
          //     message: "请填写姓名",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "薪档",
          prop: "salaryLevel",
          // placeholder: "请填写薪档",
          disabled: true, 
          rules: [
            {
              required: true,
              message: "请填写薪档",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "身份证",
          prop: "idNum",
          disabled: true, 
          // placeholder: `请输入身份证`,
          rules: [
            {
              required: true,
              message: "请输入身份证",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "是否转正",
          prop: "isRegular",
          disabled: true, 
          // placeholder: `请输入是或否`,
          rules: [
            {
              required: true,
              message: "请输入是或否",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "入职日期",
          prop: "entryTime",
          // placeholder: `请选择入职日期`,
          disabled: true,
          // rules: [
          //   {
          //     required: true,
          //     message: "请选择入职日期",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "是否在职",
          prop: "isJob",
          disabled: true, 
          // placeholder: `请输入是或否`,
          rules: [
            {
              required: true,
              message: "请输入是或否",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "收款人姓名",
          prop: "bankNoName",
          // placeholder: `请输入收款人姓名`,
          disabled: true, 
          rules: [
            {
              required: true,
              message: "请输入收款人姓名",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "工资卡号",
          prop: "bankNo",
          // placeholder: `请输入工资卡号`,
          disabled: true, 
          rules: [
            {
              required: true,
              message: "请输入工资卡号",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "开户行",
          prop: "bank",
          // placeholder: `请输入开户行`,
          disabled: true, 
          rules: [
            {
              required: true,
              message: "请输入开户行",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "应出勤天数",
          prop: "attendanceDay",
          disabled: true, 
        },
        {
          type: "input",
          label: "实际出勤天数",
          prop: "actualDay",
          placeholder: `请输入实际出勤天数`,
          rules: [
            {
              required: true,
              message: "请输入实际出勤天数",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "调休天数",
          prop: "restDay",
          placeholder: `请输入调休天数`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入调休天数",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "病假天数",
          prop: "sickDay",
          placeholder: `请输入病假天数`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入病假天数",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "缺勤天数",
          prop: "absenceDay",
          placeholder: `请输入缺勤天数`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入缺勤天数",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "旷工天数",
          prop: "notworkDay",
          placeholder: `请输入旷工天数`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入旷工天数",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "病假系数",
          prop: "sickRate",
          disabled:true,
          // placeholder: `请输入小数`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入病假系数",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "旷工系数",
          prop: "notworkRate",
          disabled:true,
          // placeholder: `请输入小数`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入旷工系数",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "基础工资",
          prop: "levelSalary",
          placeholder: `请输入基础工资`,
          rules: [
            {
              required: true,
              message: "请输入基础工资",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "岗位工资",
          prop: "postSalary",
          placeholder: `请输入岗位工资`,
          rules: [
            {
              required: true,
              message: "请输入岗位工资",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "绩效基数",
          prop: "performanceBase",
          placeholder: `请输入绩效基数`,
          rules: [
            {
              required: true,
              message: "请输入绩效基数",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          label: "评分结果（分）",
          prop: "scoreResult",
          placeholder: `请输入评分结果`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入评分结果",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "新增提成",
          prop: "addPercentage",
          placeholder: `请输入新增提成`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入新增提成",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "续费提成",
          prop: "renewPercentage",
          placeholder: `请输入续费提成`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入续费提成",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "其他提成",
          prop: "otherPercentage",
          placeholder: `请输入其他提成`,
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入续费提成",
          //     trigger: "blur"
          //   }
          // ]
        },
        {
          type: "input",
          label: "其他提成说明",
          prop: "otherPercentageDesc",
          placeholder: `请输入其他提成说明`,
        },
        {
          type: "input",
          label: "电话补贴",
          prop: "telephoneSalary",
          placeholder: `请输入电话补贴`
        },
        {
          type: "input",
          label: "交通补贴",
          prop: "trafficSalary",
          placeholder: `请输入交通补贴`
        },
        {
          type: "input",
          label: "餐补",
          prop: "mealSalary",
          placeholder: `请输入餐补`
        },
        {
          type: "input",
          label: "其他实发",
          prop: "otherActual",
          placeholder: `请输入其他实发`
        },
        {
          type: "input",
          label: "其他项说明",
          prop: "otherDesc",
          placeholder: `请输入其他项说明`
        },
        {
          type: "input",
          label: "满勤奖",
          prop: "fullSalary",
          placeholder: `请输入满勤奖`
        },
        {
          type: "input",
          label: "工龄工资",
          prop: "workAge",
          placeholder: `请输入工龄工资`
        },
        {
          type: "input",
          label: "证书补贴",
          prop: "certificateSubsidy",
          placeholder: `请输入证书补贴`
        },
        {
          type: "input",
          label: "其他奖金",
          prop: "otherSalary",
          placeholder: `请输入其他奖金`
        },
        {
          type: "input",
          label: "其他奖金说明",
          prop: "otherSalaryDesc",
          placeholder: `请输入其他奖金说明`
        },
        {
          type: "input",
          label: "其他扣减（-）",
          prop: "otherCut",
          placeholder: `请输入其他扣减`
        },
        {
          type: "input",
          label: "其他扣减说明",
          prop: "otherCutDesc",
          placeholder: `请输入其他扣减说明`
        },
        {
          type: "input",
          label: "社保扣减（-）",
          prop: "socialSecurityCut",
          placeholder: `请输入社保扣减`
        },
        {
          type: "input",
          label: "个税扣减(-)",
          prop: "personalTaxCut",
          placeholder: `请输入个税扣减`
        },
        // {
        //   type: "select",
        //   label: "所属公司",
        //   labelWidth: "80px",
        //   prop: "type",
        //   opts: [
        //     {
        //       label: "总部",
        //       value: '"总部"'
        //     },
        //     {
        //       label: "蚌埠",
        //       value: '蚌埠'
        //     },
        //     {
        //       label: "合肥",
        //       value: '合肥'
        //     },
        //     {
        //       label: "常州",
        //       value: '常州'
        //     },
        //     {
        //       label: "无锡",
        //       value: '无锡'
        //     }
        //   ],
        //   rules: [
        //     {
        //       required: true,
        //       message: "请填写所属公司",
        //       trigger: "blur"
        //     }
        //   ]
        // },
      ]
    };
  },
  
  async mounted() {
    await this.initOpts();
    if (this.$route.query.id) {
      getById({ id: this.$route.query.id }).then(res => {
      if (res.code == 200) {
        res.data = res.data? res.data : [];
        res.data = Object.assign(res.data, res.data.contract);
        for (let i = 0; i < res.data.fileList.length; i++) {
          res.data.fileList[i].name = res.data.fileList[i].fileName;
        }
        this.form = Object.assign({}, res.data);
      }
    });
  }
  },



  methods: {  
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.attachmentId = "";
          for (let i = 0; i < this.form.fileList.length; i++) {
            if (i == 0) {
              this.form.attachmentId = this.form.fileList[i].id;
            } else {
              this.form.attachmentId =
                this.form.attachmentId + "," + this.form.fileList[i].id;
            }
          }
          let params = Object.assign({}, this.form);
          //80%  300% 固定值转换小数
          if(params.sickRate == '80%'){
              params.sickRate = '0.8'
          }
           if(params.notworkRate == '300%'){
            params.notworkRate ='3';
           }

          if (this.$route.query.id) {
            params.flagType='2';
            addPayroll(params).then(res => {
              if (res.code == 200) {
                this.$message.success("编辑成功");
                this.$router.go(-1);
              }
            });
          } else {
            params.flagType='1';
            addPayroll(params).then(res => {
              if (res.code == 200) {
                this.$message.success("保存成功");
                this.$router.push({ name: "addSalary" });
                this.$refs['form'].resetFields();
              }
            });
          }
        } else {
          // this.loading = false;
        }
      });
  },


     handleChange() {
     getAttendanceDay({ years: this.form.years }).then(res => {
        if (res.code == 200) {
          this.form.attendanceDay = res.data;
        }
      });

     },


     showOtherChange(){
      if(this.form.otherPercentage != null){
        otherPercentage
      }

     },






     //文件发生改变就会触发的事件
     uploadByJsqd(file) {
      //判断是否符合beforeAvatarUpload方法中的条件
      if (this.beforeAvatarUpload(file)) {
        this.fileList.name = file.name;
        this.fileList.url = "";
        var formdata = new FormData();
        formdata.append("file", file.raw);
        formdata.append("type",'1');
        //importDevice：请求接口 formdata：传递参数
        uploadFile(formdata).then(res => {
          if (res.code == 200) {
            this.$message.success("上传成功");
            this.importFile = "";
            this.fileList = [];
            this.tableLoading = true;
          }
        });
      }
    },


    //文件校验方法
    beforeAvatarUpload(file) {
      // 通过split方法和fileArr方法获取到文件的后缀名
      let fileArr = file.name.split(".");
      let suffix = fileArr[fileArr.length - 1];
      //只能导入.ppt和.pptx文件
      if (!/(xls|xlsx)/i.test(suffix)) {
        this.$message("文件格式不正确");
        return false;
      }
      //不能导入大小超过100Mb的文件
      if (file.size > 50 * 1024 * 1024) {
        this.$message("文件过大，请上传小于50MB的文件〜");
        return false;
      }
      return true;
    },



  //   uploadFileData(file) {
  //     if (!this.form.fileList) {
  //     this.form.fileList = [];
  //   }

  //   if (file.size > 50 * 1024 * 1024) {
  //     this.$message("文件过大，请上传小于50MB的文件〜");
  //     return false;
  //   }
  //   var formdata = new FormData();
  //   formdata.append("file", file.raw);
  //   formdata.append("type",'1');//工资单类型
  //   //importDevice：请求接口 formdata：传递参数
  //   uploadFile(formdata).then(res => {
  //     if (res.code == 200) {
  //       for (let i = 0; i < res.data.length; i++) {
  //         res.data[i].name = res.data[i].fileName;
  //       }
  //       this.form.fileList = this.form.fileList.concat(res.data);
  //       this.$message.success("上传成功");
  //     }
  //   });
  // },


  //   handleRemove(file) {
  //   for (let i = 0; i < this.form.fileList.length; i++) {
  //     if (file.id == this.form.fileList[i].id) {
  //       this.form.fileList.splice(i, 1);
  //       break;
  //     }
  //   }
  // },



  selectUserInfo(obj) {
      this.userObj = Object.assign({}, obj);
      this.form.levelCodePM = this.userObj.levelCodePm;
      this.form.salaryLevel = this.userObj.salaryLevel;
      this.form.idNum = this.userObj.idNum;
      this.form.isJob = this.userObj.isJob;
      this.form.entryTime = this.userObj.entryTime;
      this.dialogTableVisible = false;
      this.form.name = this.userObj.name;
      this.form.isRegular = this.userObj.isRegular;
      this.form.bankNoName = this.userObj.bankNoName;
      this.form.bankNo = this.userObj.bankNo;
      this.form.bank = this.userObj.bank;
      this.form.userId = this.userObj.id;

      this.form.levelSalary = this.userObj.levelSalary;
      this.form.postSalary = this.userObj.postSalary;
      this.form.performanceBase = this.userObj.postPerformanceSalary;
      this.form.telephoneSalary = this.userObj.telephoneSalary;
      this.form.trafficSalary = this.userObj.trafficSalary;
      this.form.mealSalary = this.userObj.mealSalary;
      
     
    },


   
    //购买方数据填充
    searchUserInfo() {
      if (!this.form.name) {
        this.$message.warning("员工姓名不能为空");
        return;
      }

      getUserName({ userName: this.form.name }).then(res => {
        if (res.code == 200) {
          this.gridData = res.data;
          this.dialogTableVisible = true;
        }
      });
    },




    goback() {
    this.$router.go(-1);
  },


    initOpts() {
      this.initForm = {sickRate: "80%"};
      // getUserName().then(res => {
      //   if (res.code == 200) {
      //     for (let i = 0; i < this.formList.length; i++) {
      //       if (this.formList[i].dataOrigin == "name") {
      //         this.formList[i].opts = res.data;
      //       }
      //     }
      //   }
      // });

      //部门
      // getDeptName().then(res => {
      //   if (res.code == 200) {
      //     for (let i = 0; i < this.formList.length; i++) {
      //       if (this.formList[i].dataOrigin == "deptName") {
      //         this.formList[i].opts = res.data;
      //         break;
      //       }
      //     }
      //   }
      // });

      //岗位
    //   getRoleName().then(res => {
    //     if (res.code == 200) {
    //       for (let i = 0; i < this.formList.length; i++) {
    //         if (this.formList[i].dataOrigin == "roleName") {
    //           this.formList[i].opts = res.data;
    //           break;
    //         }
    //       }
    //     }

    // });

  },



  }
};
</script>

<style lang="scss" scoped>
.import-container {
  display: inline-block;
}
.el-date-editor.el-input {
  width: 200px !important;
}
.el-cascader {
  width: 520px !important;
}
.btn-operate {
  margin-top: 15px;
  margin-left: 230px;
  .el-button {
    padding: 12px 35px;
  }
}
.el-divider {
  background-color: #009688;
}
.el-divider__text {
  color: #009688;
}
</style>
