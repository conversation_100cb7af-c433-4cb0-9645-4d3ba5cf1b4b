import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [{
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
      path: '/redirect/:path*',
      component: () => import('@/views/redirect/index')
    }]
  },
  {
    path: '/',
    hidden: true,
    redirect: '/login',
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    name: "userManage",
    redirect: '/userManage',
    children: [{
      path: 'userManage',
      component: () => import('@/views/userManage/index'),
      name: 'userManage',
      meta: {
        title: '员工管理',
        icon: 'userManage',
        noCache: true,
        value: 'User_index'
      }
    }]
  },
  {
    path: '/',
    component: Layout,
    name: "groups",
    redirect: '/groups',
    // meta: {
    //   title: '部门管理'
    // },
    children: [{
      path: 'groups',
      component: () => import('@/views/groups/index'),
      name: 'groups',
      meta: {
        title: '部门管理',
        icon: 'groups',
        noCache: true,
        value: 'Role_index'
      }
    }]
  },
  {
    path: '/',
    component: Layout,
    name: "positionSalary",
    redirect: '/positionSalary',
    children: [{
      path: 'positionSalary',
      component: () => import('@/views/positionSalary/index'),
      name: 'positionSalary',
      meta: {
        title: '薪酬架构设置',
        icon: 'positionSalary',
        noCache: true,
        value: 'Salary_index'
      }
    }]
  },
  {
    path: '/',
    component: Layout,
    name: "contract",
    redirect: '/contract',
    children: [{
      path: 'contract',
      component: () => import('@/views/contract/index'),
      name: 'contract',
      meta: {
        title: '合同管理',
        icon: 'contract',
        noCache: true,
        value: 'contract_index'
      }
    }]
  },
  {
    path: '/',
    component: Layout,
    name: "payroll",
    redirect: '/payroll',
    children: [{
      path: 'payroll',
      component: () => import('@/views/payroll/index'),
      name: 'payroll',
      meta: {
        title: '工资表',
        icon: 'payroll',
        noCache: true,
        value: 'payroll_index'
      }
    }]
  },
  {
    path: '/',
    component: Layout,
    name: "attendance",
    redirect: '/attendance',
    children: [{
      path: 'attendance',
      component: () => import('@/views/attendance/index'),
      name: 'attendance',
      meta: {
        title: '考勤管理',
        icon: 'attendance',
        noCache: true,
        value: 'attendance_index'
      }
    }]
  },
  {
    path: '/',
    component: Layout,
    name: "dict",
    redirect: '/dict',
    children: [{
      path: 'dict',
      component: () => import('@/views/dict/index'),
      name: 'dict',
      meta: {
        title: '数据字典',
        icon: 'dict',
        noCache: true,
        value: 'dict_index'
      }
    }]
  },

  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: 'noRedirect',
  //   name: 'home',
  //   children: [
  //     {
  //       path: 'home',
  //       component: () => import('@/views/home/<USER>'),
  //       name: 'home',
  //       meta: { title: '首页', icon: '' }
  //     }
  //   ]
  // },


  //     {
  //       path: 'shareManage',
  //       component: () => import('@/views/shareManage/index'),
  //       name: 'shareManage',
  //       meta: { title: '用户分享管理' }
  //     },
  //     {
  //       path: 'blackManage',
  //       component: () => import('@/views/blackManage/index'),
  //       name: 'blackManage',
  //       meta: { title: '黑名单管理' }
  //     },
  //     {
  //       path: 'diamondsManage',
  //       component: () => import('@/views/diamondsManage/index'),
  //       name: 'diamondsManage',
  //       meta: { title: '钻石管理' }
  //     },
  //     {
  //       path: 'integralManage',
  //       component: () => import('@/views/integralManage/index'),
  //       name: 'integralManage',
  //       meta: { title: '积分管理' }
  //     }
  //   ]
  // },
]







const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
