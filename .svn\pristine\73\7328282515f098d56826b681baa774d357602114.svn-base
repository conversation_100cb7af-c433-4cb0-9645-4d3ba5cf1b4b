// /** When your routing table is too long, you can split it into small modules **/

// import Layout from '@/layout'

// const crmRouter = [{
//     path: '/customerHome',
//     component: Layout,
//     name: 'customerHomeParent',
//     redirect: 'noRedirect',
//     children: [{
//       path: 'customerHome',
//       component: () => import('@/views/home/<USER>'),
//       name: 'customerHome',
//       meta: {
//         title: '主页',
//         icon: 'companyInfo',
//         noCache: true,
//       }
//     }]
//   },

//   {
//     path: '/crm/customer',
//     name: 'customer',
//     component: Layout,
//     meta: {
//       title: '客户管理',
//       icon: 'customer'
//     },
//     children: [{
//         path: 'addCustomer',
//         component: () => import('@/views/crm/customer/add/index'),
//         name: 'addCustomer',
//         hidden: false,
//         meta: {
//           title: '新建客户',
//           value: 'Client_add'
//         }
//       },
//       {
//         path: 'myCustomer',
//         component: () => import('@/views/crm/customer/my/index'),
//         name: 'myCustomer',
//         meta: {
//           title: '我的客户',
//           value: 'Client_my'
//         }
//       },
//       {
//         path: 'team',
//         component: () => import('@/views/crm/customer/my/team'),
//         name: 'team',
//         meta: {
//           title: '团队客户',
//           value: 'Client_team'
//         }
//       },
//       {
//         path: 'otherType',
//         component: () => import('@/views/crm/customer/my/otherType'),
//         name: 'otherType',
//         meta: {
//           title: '其它类型',
//           value: 'Client_other'
//         }
//       },
//       {
//         path: 'custSea',
//         component: () => import('@/views/crm/customer/my/custSea'),
//         name: 'custSea',
//         meta: {
//           title: '客户公海',
//           value: 'Client_sea'
//         }
//       },
//       {
//         path: 'repeat',
//         component: () => import('@/views/crm/customer/my/repeat'),
//         name: 'repeat',
//         meta: {
//           title: '重复客户数据',
//           value: 'Client_repeat'
//         }
//       }
//     ]
//   }, {
//     path: '/sales',
//     component: Layout,
//     name: 'sales',
//     meta: {
//       title: '销售助手',
//       icon: 'sales'
//     },
//     children: [{
//         path: 'sales/today',
//         component: () => import('@/views/crm/sales/today/index'),
//         name: 'salesToday',
//         meta: {
//           title: '今日计划',
//           value: 'Zhushou_today'
//         }
//       }, {
//         path: 'sales/nextSevenDay',
//         component: () => import('@/views/crm/sales/nextSevenDay/index'),
//         name: 'salesNextSevenDay',
//         meta: {
//           title: '未来七天',
//           value: 'Zhushou_seven'
//         }
//       },
//       {
//         path: 'sales/noContact',
//         component: () => import('@/views/crm/sales/noContact/index'),
//         name: 'salesNoContact',
//         meta: {
//           title: '未按时联系',
//           value: 'Zhushou_contact'
//         }
//       },
//       {
//         path: 'sales/sea',
//         component: () => import('@/views/crm/sales/sea/index'),
//         name: 'salesSea',
//         meta: {
//           title: '即将释放到公海',
//           value: 'Zhushou_release'
//         }
//       },
//     ]
//   }, {
//     path: '/vip',
//     component: Layout,
//     name: 'vipParent',
//     meta: {
//       title: 'VIP客户管理',
//       icon: 'vip'
//     },
//     children: [{
//       path: 'vip/my',
//       component: () => import('@/views/crm/vip/my/index'),
//       name: 'vipMy',
//       meta: {
//         title: '我的VIP客户',
//         value: 'Vip_my'
//       }
//     }, {
//       path: 'vip/team',
//       component: () => import('@/views/crm/vip/my/team'),
//       name: 'vipTeam',
//       meta: {
//         title: '团队VIP客户',
//         value: 'Vip_team'
//       }
//     }, {
//       path: 'vip/shareVip',
//       component: () => import('@/views/crm/vip/my/shareVip'),
//       name: 'vipShare',
//       meta: {
//         title: '待分配VIP客户',
//         value: 'Vip_left'
//       }
//     }, {
//       path: 'vip/expireSoonVip',
//       component: () => import('@/views/crm/vip/my/expireSoonVip'),
//       name: 'vipExpireSoon',
//       meta: {
//         title: '即将过期VIP客户',
//         value: 'Vip_waitOut'
//       }
//     }, {
//       path: 'vip/expireVip',
//       component: () => import('@/views/crm/vip/my/expireVip'),
//       name: 'vipExpire',
//       meta: {
//         title: '已过期VIP客户',
//         value: 'Vip_out'
//       }
//     }, {
//       path: 'vip/leavesVip',
//       component: () => import('@/views/crm/vip/my/leavesVip'),
//       name: 'leavesVip',
//       meta: {
//         title: '有流失风险客户',
//         value: 'Vip_leave'
//       }
//     }, {
//       path: 'vip/lossVip',
//       component: () => import('@/views/crm/vip/my/lossVip'),
//       name: 'lossVip',
//       meta: {
//         title: '中途流失客户',
//       }
//     }]
//   },
//   {
//     path: '/businessHand',
//     name: 'businessHand',
//     component: Layout,
//     meta: {
//       title: '业务办理',
//       icon: 'businessHand'
//     },
//     children: [{
//       path: 'businessHand/apply',
//       component: () => import('@/views/crm/businessHand/apply'),
//       name: 'apply',
//       meta: {
//         title: '代账成交申请',
//         value: 'Approve_index',
//       }
//     }, {
//       path: 'target/deal',
//       component: () => import('@/views/crm/businessHand/deal'),
//       name: 'deal',
//       meta: {
//         title: '协同待办',
//         value: 'Synegy_index',
//       }
//     }, {
//       path: 'target/refund',
//       component: () => import('@/views/crm/businessHand/refund'),
//       name: 'refund',
//       meta: {
//         title: '代账、协同退款',
//       }
//     }, ]
//   },
//   {
//     path: '/target',
//     component: Layout,
//     name: 'target',
//     meta: {
//       title: '目标管理',
//       icon: 'target'
//     },
//     children: [{
//       path: 'target/telephone',
//       component: () => import('@/views/crm/target/telephone'),
//       name: 'targetTelephone',
//       meta: {
//         title: '通话目标设置',
//         value: 'Target_talk'
//       }
//     }, {
//       path: 'target/performance',
//       component: () => import('@/views/crm/target/performance'),
//       name: 'performance',
//       meta: {
//         title: '业绩目标',
//         value: 'Target_index'
//       }
//     }, ]
//   }, {
//     path: '/number',
//     component: Layout,
//     name: 'number',
//     meta: {
//       title: '标记号码',
//       icon: 'number'
//     },
//     children: [{
//         path: 'number/vip',
//         component: () => import('@/views/crm/number/vip'),
//         name: 'numberVip',
//         meta: {
//           title: 'VIP号码',
//           value: 'Mark_vip'
//         }
//       },
//       {
//         path: 'number/rejected',
//         component: () => import('@/views/crm/number/rejected'),
//         name: 'numberRejected',
//         meta: {
//           title: '拒绝号码',
//           value: 'Mark_reject'
//         }
//       }, {
//         path: 'number/reserve',
//         component: () => import('@/views/crm/number/reserve'),
//         name: 'numberReserve',
//         meta: {
//           title: '人才储备',
//           value: 'Mark_same'
//         }
//       }
//     ]
//   },






//   {
//     path: '/statistics',
//     component: Layout,
//     name: 'statistics',
//     meta: {
//       title: '统计',
//       icon: 'statistics'
//     },
//     children: [{
//       path: 'statistics/customerNumber',
//       component: () => import('@/views/crm/statistics/customerNumber'),
//       name: 'statisticsCustomerNumber',
//       meta: {
//         title: '客户数量统计',
//         value: 'Mark_vip'
//       }
//     }, ]
//   },




//   {
//     path: '/crm',
//     component: Layout,
//     name: 'setting',
//     meta: {
//       title: '设置',
//       icon: 'setting'
//     },
//     children: [{
//         path: 'setting/business',
//         component: () => import('@/views/crm/setting/business/index'),
//         name: 'settingBusiness',
//         meta: {
//           title: '业务设置',
//           value: 'Serve_index'
//         }
//       }, {
//         path: 'setting/customer',
//         component: () => import('@/views/crm/setting/customer/index'),
//         name: 'settingCustomer',
//         meta: {
//           title: '客户类别设置',
//           value: 'ClientType_index',
//           noCache: true
//         }
//       }, {
//         path: 'setting/salesPhaseSetting',
//         component: () => import('@/views/crm/setting/salesPhaseSetting/index'),
//         name: 'settingSalesPhaseSetting',
//         meta: {
//           title: '销售阶段设置',
//           value: 'Stage_index',
//           noCache: true
//         }
//       }, {
//         path: 'setting/quickReply',
//         component: () => import('@/views/crm/setting/quickReply/index'),
//         name: 'quickReply',
//         meta: {
//           title: '快捷回复',
//           value: 'ShortCut_index',
//           noCache: true
//         }
//       }, {
//         path: 'setting/vip',
//         component: () => import('@/views/crm/setting/vip/index'),
//         name: 'vip',
//         meta: {
//           title: 'VIP到期提醒',
//           value: 'VipOutSet_index',
//           noCache: true
//         }
//       },
//       {
//         path: 'setting/smsRecord',
//         component: () => import('@/views/crm/setting/smsRecord/index'),
//         name: 'smsRecord',
//         meta: {
//           title: '短信记录',
//           noCache: true
//         }
//       },
//     ]
//   }
// ]
// export default crmRouter
