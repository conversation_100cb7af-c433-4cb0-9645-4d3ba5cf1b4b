<template>
  <el-row class="el-table-self el-table-ck">
    <el-table
      ref="selftab"
      v-loading="listLoading"
      :size="tabSize"
      :summary-method="getSummaries"
      :show-summary="showSummary"
      :span-method="spanMethod"
      :data="tableData"
      border
      :show-overflow-tooltip="true"
      :highlight-current-row="highLight"
      :height="tableHeight"
      :header-row-class-name="headerClass"
      :row-style="rowStyle"
      :cell-style="cellStyle"
      style="width: 100%"
      @cell-click="cellClick"
      @row-click="rowClick"
      @sort-change="sortChange"
      @select="selectChange"
      @selection-change="selectionChange"
      @select-all="selectAll"
    >
      <el-table-column
        v-if="tabType"
        :show-overflow-tooltip="true"
        :type="tabType"
        width="55"
        align="center"
        :label="tabLabel"
        :selectable="selecTable"
        :index="tabIndex"
      />

      <template v-for="(column, index) in columns">
        <template v-if="!column.hidden">
          <el-table-column
            v-if="column.operations"
            :key="index"
            :show-overflow-tooltip="true"
            :fixed="column.fixed"
            :prop="column.value"
            :label="column.label"
            :width="column.width"
            align="center"
          >
            <template slot-scope="scope">
              <template
                v-for="(operate, operIndex) in column.operations.formatter
                  ? column.operations.formatter(scope.row)
                  : column.operations"
              >
                <template
                  v-if="
                    !operate.isHidden ||
                      !operate.isHidden(scope.row, scope.$index)
                  "
                >
                  <template v-if="operate.type === 'selectBtn'">
                    <el-dropdown
                      style="margin-left:10px;"
                      @command="
                        operate.handleCommand
                          ? operate.handleCommand($event, scope.row)
                          : {}
                      "
                    >
                      <el-button type="primary">
                        {{ operate.name
                        }}<i class="el-icon-arrow-down el-icon--right"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          v-for="itemBtn in operate.list"
                          v-if="!itemBtn.hidden"
                          :key="itemBtn.label"
                          :command="itemBtn.command"
                          >{{ itemBtn.label }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>

                  <template
                    v-else-if="column.operType === 'button' && !column.hidden"
                  >
                    <el-button
                      :key="operIndex"
                      :disabled="
                        operate.formatter
                          ? operate.formatter(scope.row, scope.$index).disabled
                          : operate.disabled
                      "
                      :type="
                        operate.formatter
                          ? operate.formatter(scope.row).type
                          : operate.type || ''
                      "
                      :icon="operate.icon"
                      :plain="operate.plain"
                      :style="operate.style"
                      @click="
                        operate.func
                          ? operate.func(scope.row, scope.$index)
                          : {}
                      "
                      >{{
                        operate.formatter
                          ? operate.formatter(scope.row).label
                          : operate.label
                          ? operate.label
                          : scope.row[column.value]
                      }}</el-button
                    >
                  </template>

                  <template
                    v-else-if="column.operType === 'icon' && !operate.hidden"
                  >
                    <el-button
                      :key="operIndex"
                      type="text"
                      :icon="operate.iconClass"
                      :style="operate.style"
                      @click="operate.func(scope.row, scope.$index)"
                      >{{
                        operate.formatter
                          ? operate.formatter(scope.row).label
                          : operate.label
                          ? operate.label
                          : scope.row[column.value]
                      }}</el-button
                    >
                  </template>
                </template>
              </template>
            </template>
          </el-table-column>

          <el-table-column
            v-else-if="!column.isHidden && column.func"
            :key="index"
            :show-overflow-tooltip="true"
            :fixed="column.fixed"
            :prop="column.value"
            :label="column.label"
            :min-width="column.minWidth"
            align="center"
            :sortable="column.sortable"
            :formatter="column.formatter"
            :class-name="column.className"
          >
            <template slot-scope="scope">
              <span
                @click="column.func(scope.row, scope.$index)"
                :style="column.style"
                :class="column.spanClassName"
                >{{
                  column.formatter
                    ? column.formatter(scope.row).label
                    : scope.row[column.value]
                }}</span
              >
            </template>
          </el-table-column>

          <el-table-column
            v-else-if="!column.isHidden"
            :key="index"
            :show-overflow-tooltip="true"
            :fixed="column.fixed"
            :prop="column.value"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :align="column.align ? column.align : 'center'"
            :sortable="column.sortable"
            :formatter="column.formatter"
            :style="column.style"
            :class-name="column.className"
            @click.native="column.func ? column.func : {}"
          >
            <template slot-scope="scope">
              <el-checkbox
                v-if="
                  column.checkbox &&
                    (column.show ? column.show(scope.row) : true)
                "
                @change="v => column.func(v, scope.row)"
                v-model="scope.row[column.checkedField]"
              >
                <span :style="scope.row.highlight ? 'color:#3F86FF' : ''">{{
                  scope.row[column.value]
                }}</span>
              </el-checkbox>
              <span
                v-else-if="column.checkbox && !column.show(scope.row)"
                style="margin-left: 16px"
              >
                <span :style="scope.row.highlight ? 'color:#3F86FF' : ''">{{
                  scope.row[column.value]
                }}</span>
              </span>
              <!-- style="margin-left:24px" -->
              <span
                v-else-if="column.copy"
                class="g__copy-btn g__btn"
                :data-clipboard-text="
                  column.formatter
                    ? column.formatter(scope.row)
                    : scope.row[column.value]
                "
                >{{
                  column.formatter
                    ? column.formatter(scope.row)
                    : scope.row[column.value]
                }}</span
              >
              <span v-else>
                <span
                  v-if="
                    column.formatterTag ? column.formatterTag(scope.row) : false
                  "
                  style="color:red;margin:0 5px"
                  >*</span
                >
                {{
                  column.formatter
                    ? column.formatter(scope.row)
                    : scope.row[column.value]
                }}
              </span>
            </template>
          </el-table-column>
        </template>
      </template>
    </el-table>
    <div v-if="pageSize" class="pagination-footer">
      <span class="description">{{ description }}</span>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-row>
</template>

<script>
export default {
  name: "ElTableSelf",
  props: {
    listLoading: Boolean, // tab 加载层
    highLight: { type: Boolean, default: true },
    headerClass: { type: String, default: "default" }, // 头部背景色Class名称，默认default
    tabType: String, // 对应列的类型，selection/index/expand
    tabLabel: String,
    tableHeight: Number, // 表格的高度
    tabSize: { type: String },
    tableData: Array, // 表格数据
    columns: Array, // 表格列配置数据,{vlaue:对应数据对象中的属性，label：对应的是标题文字，fixed:列是否固定，width:列宽， sortable：是否可排序，formatter:列格式化， className：对应的是列的样式类名}
    sortChange: { type: Function, default: () => {} }, // 点击列表头进行排序 { column, prop, order }
    description: String, // 分页脚底左侧的数据说明
    totalCount: Number, // 表格数据总数
    pageSizes: Array, // 决定每页显示的条数[10,15,20,25]
    pageSize: Number,
    spanMethod: Function,
    operType: { type: String, default: "button" },
    getSummaries: Function,
    showSummary: { type: Boolean, default: false },
    type: { type: String, default: "text" },
    currentPage: { type: Number, default: 1 },
    selecTable: Function,
    disabled: { type: Boolean, default: false },
    tabIndex: Function,
    cellStyle: Function,
    rowStyle: Function
  },
  methods: {
    // 切换页面显示条数
    handleSizeChange(val) {
      this.$emit("pageSizeChange", val);
    },

    // 跳转页码
    handleCurrentChange(val) {
      this.$emit("currentPageChange", val);
    },

    // 复选框事件
    selectionChange(selections) {
      this.$emit("selectionChange", selections);
    },

    // 复选框选中当前行事件
    selectChange(selections, row) {
      this.$emit("selectChange", selections, row);
    },

    // 行点击
    rowClick(row, event) {
      this.$emit("rowClick", row, event);
    },

    // 列点击
    cellClick(row, column, cell, event) {
      this.$emit("cellClick", row, column, cell, event);
    },

    // 单选行高亮
    setCurrentRow(row) {
      this.$refs.selftab.setCurrentRow(row);
    },

    // 全选事件
    selectAll(selections) {
      this.$emit("selectAll", selections);
    }
  }
};
</script>
<style lang="scss" scoped>
.el-table-self {
  font-size: 14px !important;
  .tab-svg {
    width: 24px;
    height: 24px;
    cursor: pointer;
    vertical-align: middle;
  }
}
.pagination-footer {
  .description {
    float: left;
    margin-left: 20px;
    margin-top: 12px;
    font-size: 14px;
  }
  .el-pagination {
    float: right;
    margin-top: 8px;
    margin-bottom: 8px;
  }
}
.el-table__empty-block {
  position: relative;
  min-height: 60px;
  text-align: center;
  width: 100%;
  height: 100%;
}

.el-table__empty-text {
  position: absolute;
  left: 50%;
  width: 110px;
  height: 110px;
  top: 50%;
  line-height: 220px;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: #5e7382;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.tableSpan {
  display: inline-block;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.isPlan {
  font-size: 12px;
  color: #fff;
  padding: 2px 5px;
  background: #c1242a;
  border-radius: 3px;
}

v-deep .el-table__footer-wrapper tbody td {
  background: oldlace !important;
}
</style>

<style lang="scss">
.el-table-ck {
  th {
    background-color: #f7f8fa !important;
    box-shadow: 0px 1px 0px 0px #ebedf0;
    color: #303133;
    padding: 0 0;
    height: 46px !important;
  }
  .is-hidden .cell {
    padding-left: 34px !important;
  }
}
</style>
