import {
    fetch,
    post,
    put,
    deleteHttp
  } from '@/utils/request'
  
  // 查询列表
  export const queryPage = data => {
    return fetch(`/position/salary`, data)
  }

  // 添加
export const addSalary = data => {
  return post(`/position/salary`, data)
}

// 更新
export const updateSalary = data => {
  return put(`/position/salary`, data)
}

  
  // 查询明细
  export const getSalaryByid = data => {
    return fetch(`/position/salary/${data.id}`)
  }

  
  
  // 删除
  export const deletetSalary = data => {
    return deleteHttp(`/position/salary/${data.id}`)
  }
  