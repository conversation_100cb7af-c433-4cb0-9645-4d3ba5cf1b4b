<template>
  <el-row class="page-container">
    <el-row class="page-main">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self :columns="columns" :current-page="pageIndex" :list-loading="tableLoading" :table-data="dataList"
          :total-count="total" :page-sizes="pageSizes" :page-size="pageSize" @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>

      <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true"
        :label-width="'90'" :title="drawTitle"></draw-form>

    </el-row>
  </el-row>

</template>



<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import drawForm from "@/components/FormComponents/draw";
import { queryPage } from "./api";
export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
      total: 0,
      id: 0,
      drawTitle: "",
      dataList: [],
      drawForms: [],
      dataList: [],
      columns: [
        {
          label: "所属公司",
          value: "typeName"
        },
        {
          label: "部门",
          value: "groupName"
        },
        {
          label: "岗位",
          value: "roleName"
        },
        {
          label: "所在岗位人数",
          value: "num"
        }
      ],

      searchList: [
        {
          type: "select",
          label: "所属公司",
          labelWidth: "80px",
          prop: "type",
          opts: [
            {
              label: "总部",
              value: 1
            },
            {
              label: "蚌埠分公司",
              value: 2
            },
            {
              label: "合肥分公司",
              value: 3
            },
            {
              label: "常州分公司",
              value: 4
            },
            {
              label: "无锡分公司",
              value: 5
            }
          ]
        },
        {
          type: "select",
          label: "员工状态",
          labelWidth: "80px",
          prop: "disabled",
          opts: [
            {
              label: "在职",
              value: 0
            },
            {
              label: "离职",
              value: 1
            }
          ]
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            }
          ]
        }
      ]
    };

  },
  watch: {},
  async mounted() {
    this.handleSearch();
  },
  methods: {

    close() {
      this.initForm = {};
      this.$refs.drawForm.close();
    },

    handleFormSearch(form) {
      this.pageIndex = 1;
      this.handleSearch(form);
    },

    tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

    handleSearch(form) {
      const params = Object.assign(this.$refs.form.form, form);
      params.page = this.pageIndex;
      params.pageSize = this.pageSize;
      this.tableLoading = true;

      if (params.createdAt && params.createdAt.length > 0) {
        params.startTime = params.createdAt[0];
        params.endTime = params.createdAt[1];
        delete params.createdAt;
      }

      queryPage(params).then(res => {
        if (res.code == 200) {
          this.total = res.data.total;
          this.dataList = res.data.list;
        }
        this.tableLoading = false;
      });
    }
  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>
<style>
.select-user-name {
  max-width: 700px !important;
}
</style>
