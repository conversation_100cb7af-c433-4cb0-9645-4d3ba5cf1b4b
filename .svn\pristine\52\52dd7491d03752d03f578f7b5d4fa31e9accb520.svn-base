<template>
  <div class="login-container">
    <div class="login-main">
      <el-col>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          autocomplete="on"
          label-position="left"
        >
          <el-col class="welcome">欢迎登录</el-col>
          <el-col class="note">请使用您本人的账号密码登录</el-col>
          <el-form-item prop="loginName">
            <!-- <span class="svg-container">
            <svg-icon icon-class="user" style="color: #fff;" />
            </span>-->
            <el-input
              ref="loginName"
              v-model="loginForm.loginName"
              placeholder="请输入用户名"
              name="loginName"
              type="text"
            />
          </el-form-item>

          <el-form-item prop="password">
            <!-- <span class="svg-container">
            <svg-icon icon-class="password" style="color: #fff;" />
            </span>-->
            <el-input
              ref="password"
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              name="password"
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>
          <el-row class="forget">
            <!-- <el-col>
              <span @click.stop="forGet">忘记密码?</span>
            </el-col> -->
          </el-row>
          <el-col style="text-align:left;width:320px;">
            <el-button
              :loading="loading"
              type="primary"
              style="width:320px;margin:30px 0;height: 45px;font-size: 20px;border-radius:20px;"
              @click.native.prevent="handleLogin"
              >登&ensp;录</el-button
            >
          </el-col>
        </el-form>
      </el-col>
    </div>
    <dialog-form
      ref="audit"
      :width="'560px'"
      :title="dialogTitle"
      :form-data="auditData"
      :form-edit="auditEdit"
      @handleConfirm="handleConfirm"
    />
  </div>
</template>

<script>
import dialogForm from "@/components/FormComponents/dialogForm";
import { login} from "@/api/user";
import { setToken } from "@/utils/auth";
import { updatePwd } from "@/views/userManage/api";
import { mapGetters } from "vuex";
export default {
  name: "Login",
  components: {
    dialogForm
  },
  data() {
    const validateuserName = (rule, value, callback) => {
      if (value.length == 0) {
        callback(new Error("请输入用户名"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error("请输入密码"));
      } else {
        callback();
      }
    };
    return {
      dialogTitle: "",
      auditEdit: null,
      auditData: [],
      pwdAuditData: [
        {
          type: "input",
          name: "旧密码",
          inputType: "password",
          field: "oldPwd",
          rules: [
            {
              required: true,
              message: "请输入旧密码",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          inputType: "password",
          placeholder: "必须为包含大小写字母和数字，长度8~20位",
          name: "新密码",
          field: "newPwd",
          rules: [
            {
              required: true,
              message: "请输入新密码",
              trigger: "blur"
            }
          ]
        },
        {
          type: "input",
          name: "重复密码",
          placeholder: "必须为包含大小写字母和数字，长度8~20位",
          inputType: "password",
          field: "retypePwd",
          rules: [
            {
              required: true,
              message: "请输入重复密码",
              trigger: "blur"
            }
          ]
        }
      ],
      showCode: false,
      uuid: "",
      verifyCode: "",
      loginForm: {
        loginName: "",
        password: ""
      },
      loginRules: {
        loginName: [
          { required: true, trigger: "blur", validator: validateuserName }
        ],
        password: [{ required: true, trigger: "blur", validator: validatePassword }]
      },
      loading: false,
      redirect: undefined,
      otherQuery: {}
    };
    // return {
    //   loginForm: {
    //     loginName: "",
    //     password: "",
    //     Authorization: ""
    //   },
    //   loginRules: {
    //     loginName: [
    //       { required: true, trigger: "blur", validator: validateuserName }
    //     ],
    //     password: [
    //       { required: true, trigger: "blur", validator: validatePassword }
    //     ]
    //   },
    //   loading: false,
    //   redirect: undefined,
    //   otherQuery: {}
    // };
  },
  computed: {
    ...mapGetters(["userInfo"])
  },
  mounted() {
    // this.$store.dispatch("user/clearUserInfo");
    if (this.loginForm.loginName === "") {
      this.$refs.loginName.focus();
    } else if (this.loginForm.password === "") {
      this.$refs.password.focus();
    }
  },
  methods: {
    forGet() {
      this.$message.warning("请联系管理员！");
    },
    handleLogin() {
      let params = Object.assign({}, this.loginForm);
      debugger
      login(params).then(res => {
      if (res.code == 200) {
        setToken(res.data.accessToken);
        this.$store.dispatch("user/setUserInfo", res.data);
        if (res.data.accessToken) {
          setToken(res.data.accessToken);
          this.$store.dispatch("user/setUserInfo", res.data);
          this.$store.dispatch("permission/generateRoutes", res.data);
          this.$router.push("/userManage");
        }
      } else {
        this.$message.warning(res.msg);
      }
    })
   
    },
    handleConfirm() {
      let data = this.$refs.audit.form;
      if (data.newPwd != data.retypePwd) {
        this.$message.warning("两次密码不一致！");
        this.$refs.audit.loading = false;
        return;
      } else {
        updatePwd(this.$refs.audit.form)
          .then(res => {
            if (res.code == 200) {
              this.$message.success(res.message);
              this.$refs.audit.close();
              this.$store.dispatch("app/setActiveMenu", 1);
              this.$store.dispatch("permission/generateRoutes", 1);
              this.$router.push({ path: "/charts/test1" });
            } else {
              this.$message(res.message);
              this.$refs.audit.loading = false;
            }
          })
          .catch(e => {
            this.$refs.audit.loading = false;
          });
      }
    },
  
  }
};
</script>

<style lang="scss">
$bg: rgb(95, 149, 226);
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  background-color: rgba(0, 0, 0, 0.6);
  .el-input {
    display: inline-block;
    height: 40px;
    font-size: 18px;
    width: 76%;
    input {
      border: 0px;
      -webkit-appearance: none;
      border-radius: 3px;
      padding: 12px 5px 12px 15px;
      color: #000;
      height: 40px;
    }
  }
  .el-select .el-input {
    display: inline-block;
    height: 40px;
    font-size: 18px;
    width: 85%;
  }
  .el-form-item--small .el-form-item__error {
    font-size: 14px;
  }
}
</style>

<style lang="scss" scoped>
$bg: rgb(95, 149, 226);
$dark_gray: #889aa4;
$light_gray: #eee;

.top {
  height: 70px;
  line-height: 70px;
  color: #fff;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  background: #44a9ca;
}
.login-container {
  background: url(~@/assets/2.png) no-repeat;
  padding: 30px;
  height: 100%;
  width: 100%;
  background-size: cover;
  .el-form-item__content {
    font-size: 22px !important;
  }
  .welcome {
    letter-spacing: 5px;
    font-size: 28px;
    color: rgb(76, 141, 255);
  }
  .forget {
    letter-spacing: 1px;
    color: rgb(76, 141, 255);
    cursor: pointer;
  }
  .note {
    letter-spacing: 2px;
    margin: 10px 0 30px 0;
    color: rgb(156, 147, 174);
  }
  .login-form {
    position: relative;
    float: right;
    margin-right: calc(50% - 396px - 100px);
    // margin-right: 50%;
    margin-top: 180px;
    overflow: hidden;
  }
  .login-code {
    display: inline-block;
    width: 140px;
    height: 40px;
    vertical-align: bottom;
    img {
      width: 140px;
      height: 40px;
    }
  }
  .svg-container {
    padding: 6px 0 6px 6px;
    color: $dark_gray;
    vertical-align: middle;
    width: 40px;
    font-size: 22px;
    display: inline-block;
  }
}
.footer {
  height: 60px;
  line-height: 60px;
  background: #3e3a32;
  text-align: center;
  color: #fff;
  font-size: 12px;
}
</style>
<style lang="scss">
.mode-form {
  .el-dialog__header {
    border-bottom: 1px solid #dde2ee !important;
  }
  .el-dialog__title {
    font-size: 16px;
    font-weight: 700;
    color: #2a396d;
  }
  .el-dialog__body {
    padding: 15px 20px 10px 20px !important;
  }
  .el-radio {
    margin: 15px 0;
    width: 100%;
  }
  .el-dialog__footer {
    border-top: 1px solid #dde2ee !important;
    padding: 15px !important;
  }
}
.login-main {
  background: url(~@/assets/home.png) no-repeat;
  border-radius: 30px;
  height: 100%;
  width: 100%;
  background-size: cover;
  .login-form {
    .el-form {
      background-color: rgba(0, 0, 0, 0.6) !important;
    }
    .el-input__inner {
      width: 320px;
      border: 1px solid #dcdfe6;
      border-radius: 8px;
    }
  }
  .login-number {
    .el-input__inner {
      border-radius: 8px 0 0 8px !important;
    }
  }
  .svg-icon {
    color: rgba(0, 0, 0, 0.6) !important;
  }
}
</style>
