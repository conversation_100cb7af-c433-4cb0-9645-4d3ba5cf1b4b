<template>
  <el-drawer
    :title="title"
    v-if="drawer"
    custom-class="drawer-form crm-apply"
    :visible.sync="drawer"
    :direction="direction"
    :wrapper-closable="false"
    :destroy-on-close="true"
  >
    <el-row class="basic-info">

      <el-form
        :label-position="labelPosition"
        label-width="100px"
        ref="form"
        :inline="true"
        :model="formApply"
      >

      <el-form-item label="员工姓名" required prop="userId">
          <el-select
            style="width: 250px"
            type="select"
            v-model="formApply.userId"
            dataOrigin="userName"
            
            placeholder="请填写员工姓名"
          >
          </el-select>
        </el-form-item>
      
        <el-form-item label="合同编号" required prop="contractNo">
          <el-input
            style="width: 250px"
           
            v-model="formApply.contractNo"
            placeholder="请填写合同编号"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="合同主体" required prop="contractName">
          <el-input
            style="width: 250px"
        
            v-model="formApply.contractName"
            placeholder="请输入合同主体"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="合同是否已续签订" required prop="isRenewal">
          <el-radio-group v-model="formApply.isRenewal">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="合同签订日期" required prop="signDate">
          <el-date-picker
            style="width: 250px"
            v-model="formApply.signDate"
            type="date"
            :value-format="'yyyy-MM-dd'"
            placeholder="请选择合同签订日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="是否为首次签" required prop="oneTimes">
          <el-radio-group v-model="formApply.oneTimes">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="合同签订次数"  prop="times">
          <el-input
            style="width: 250px"
            v-model="formApply.times"
            placeholder="请输入合同签订次数"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="合同起始日期" required prop="startDate">
          <el-date-picker
            style="width: 250px"
            v-model="formApply.startDate"
            type="date"
            :value-format="'yyyy-MM-dd'"
            placeholder="请选择合同起始日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="合同到期日期" required prop="endDate">
          <el-date-picker
            style="width: 250px"
            v-model="formApply.endDate"
            type="date"
            :value-format="'yyyy-MM-dd'"
            placeholder="请选择合同到期日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="合同类型" required>
          <el-select
            style="width: 250px"
            filterable
            v-model="formApply.contractType"
            placeholder="请选择合同类型"
          >
            <el-option
              v-for="item in contractArrList"
              :key="item.dictCode"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        
        <el-form-item label="合同年限（年）"  prop="contractLimit" required>
          <el-input
            style="width: 250px"
            v-model="formApply.contractLimit"
            placeholder="请输入合合同年限"
          >
          </el-input>
        </el-form-item>
        
        <el-form-item label="附件" required>
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              class="upload-demo"
              :limit="10"
              :on-remove="handleRemove"
              :on-change="uploadFileData"
              :file-list="formApply.fileList"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">
                文件大小不超过10M
              </div>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </el-row>

    <div class="drawer-footer detail-footer">
      <el-button size="medium" @click="close">关 闭</el-button>
      <el-button size="medium" type="primary" @click="addData"
        >提交申请</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
import { uploadContractFile } from "@/api/file";
import { contractTypeEnum } from "@/api/store";
import { mapGetters } from "vuex";
import { addContract} from "./api";
export default {
  props: {
    size: { type: String, default: "small" },
    direction: { type: String, default: "rtl" },
    drawerSize: { type: String, default: "60%" },
    valueWidth: { type: String, default: "" },
    title: { type: String },
    labelPosition: { type: String, default: "right" },
    labelWidth: { type: String },
    formStyle: { type: Object },
    inlineFlag: { type: Boolean, default: false },
    lists: { type: Array }, // 按钮组
    btnStyle: { type: String, default: "margin-top:30px;text-align:center" }
  },
  computed: {
    ...mapGetters(["userInfo"])
  },
  // components: { viewFile },
  data() {
    return {
      dialogTableVisible: false,
      gridData: [],
      contractArrList: contractTypeEnum,
      bizList: [],
      formLoading: false,
      // serviceDateDisable: false,
      // numberDisable: false,
      // companyVal: "",
      contractType: null,
      companyObj: {},
      drawer: false,
      // disableAmount: false,
      form: {},
      formApply: {
        remark: "",
        approvalAdvice: "",
        approvalStatus: "",
        approver: 0,
        serviceDate: [],
        attachId: 0,
        workType: "0",
        companyName: "",
        number: "",
        createTime: "",
        creator: 0,
        quality: "",
        clientId: "",
        amount: "",
        accountType: 0,
        serviceEndPeriod: "",
        serviceStartPeriod: "",
        serveId: "",
        fileList: []
      }
    };
  },

  methods: {
    handleRemove(file) {
      for (let i = 0; i < this.formApply.fileList.length; i++) {
        if (file.id == this.formApply.fileList[i].id) {
          this.formApply.fileList.splice(i, 1);
          break;
        }
      }
    },

    uploadFileData(file) {
      if (!this.formApply.fileList) {
        this.formApply.fileList = [];
      }
      if (file.size > 10 * 1024 * 1024) {
        this.$message("文件过大，请上传小于10MB的文件〜");
        return false;
      }
      var formdata = new FormData();
      formdata.append("files", file.raw);
      //importDevice：请求接口 formdata：传递参数
      uploadContractFile(formdata).then(res => {
        if (res.code == 200) {
          for (let i = 0; i < res.data.length; i++) {
            res.data[i].name = res.data[i].fileName;
          }
          this.form.fileList = this.form.fileList.concat(res.data);
          this.$message.success("上传成功");
        }
      });
    },

    initOpts() {
      getUserName().then(res => {
        debugger
        if (res.code == 200) {
          for (let i = 0; i < this.formApply.length; i++) {
            if (this.formApply[i].dataOrigin == "userName") {
              this.formApply[i].opts = res.data;
              break;
            }
          }
        }
      });
    },


    // selectCompany(obj) {
      // this.companyObj = Object.assign({}, obj);
      // this.formApply.clientId = this.companyObj.id;
      // this.formApply.quality = this.companyObj.quality;
      // if (this.companyObj.allAmount) {
      //   this.formApply.allAmount = this.companyObj.allAmount;
      //   this.formApply.number = obj.number;
      //   this.formApply.serviceDate = obj.serveDate.split(" - ");
      //   this.serviceDateDisable = true;
      //   this.disableAmount = true;
      //   this.numberDisable = true;
      // } else {
      //   this.serviceDateDisable = false;
      //   this.disableAmount = false;
      //   this.numberDisable = false;
      // }
      // this.dialogTableVisible = false;
      // this.companyVal = this.companyObj.companyName;
    // },

    // searchCompany() {
    //   if (!this.companyVal) {
    //     this.$message.warning("员工姓名不能为空");
    //     return;
    //   }

    //   findCompany({ companyName: this.companyVal }).then(res => {
    //     if (res.code == 200) {
    //       this.gridData = res.data;
    //       this.dialogTableVisible = true;
    //     }
    //   });
    // },

    addData() {
      debugger
      this.$refs.form.validate(valid => {
        if (valid) {
          let params = Object.assign({}, this.formApply);

          params.files = "";
          for (let i = 0; i < this.form.fileList.length; i++) {
            if (i == 0) {
              params.files = this.form.fileList[i].id;
            } else {
              params.files = params.files + "," + this.form.fileList[i].id;
            }
          }

          // if (params.serviceDate && params.serviceDate.length > 0) {
          //   params.serveDate =
          //     params.serviceDate[0] + " - " + params.serviceDate[1];
          //   delete params.serviceDate;
          // }

          addContract(params).then(res => {
            if (res.code == 200) {
              this.$message.success("添加成功");
              this.close();
            }
          });
        } else {
          this.loading = false;
        }
      });
    },

    open(obj) {
      this.drawer = true;
    //   findByBizType().then(res => {
    //     if (res.code == 200) {
    //       this.bizList = res.data;
    //       this.formApply.serveId = res.data[0].id;
    //     }
    //   });
      this.initforms({});
      // if (obj && obj.allAmount) {
      //   this.formApply.allAmount = obj.allAmount;
      //   this.formApply.number = obj.number;
      //   this.formApply.serviceDate = obj.serveDate.split(" - ");
      //   this.disableAmount = true;
      //   this.serviceDateDisable = true;
      //   this.numberDisable = true;
      // } else {
      //   this.disableAmount = false;
      //   this.serviceDateDisable = false;
      //   this.numberDisable = false;
      // }
    },

    close() {
      this.companyObj = {};
      this.companyVal = "";
      this.formApply = {
        remark: "",
        approvalAdvice: "",
        approvalStatus: "",
        approver: 0,
        attachId: 0,
        workType: "0",
        serviceDate: [],
        companyName: "",
        fileList: [],
        number: "",
        createTime: "",
        creator: 0,
        quality: "",
        clientId: "",
        allAmount: "",
        amount: "",
        contractType: 1,
        serviceEndPeriod: "",
        serviceStartPeriod: "",
        serveId: ""
      };
      this.drawer = false;

      this.$emit("refresh");
    },

    showLoading() {
      this.formLoading = true;
    },

    hideLoading() {
      this.formLoading = false;
    },

    // 表单赋值
    initforms(formEdit) {
      this.$nextTick(() => {
        if (formEdit) {
          this.form = Object.assign(this.formApply, formEdit);
        } else {
          this.form = Object.assign({}, this.formApply);
        }
        this.loading = false;
        const drawerArr = document.getElementsByClassName("crm-apply");
        const footer = document.getElementsByClassName("detail-footer");
        footer[0].style.width = drawerArr[0].style.width;
      });
    },

    // 绑定部分值(此时表单已渲染)
    initfields(obj) {
      this.form = Object.assign(this.form, obj);
    }
  }
};
</script>

<style lang="scss">
.crm-apply {
  .el-drawer__header {
    background: #f8f9fb;
    padding: 0 16px;
    margin: 16px;
    height: 50px;
    font-family: PingFangSC-Medium;
    font-weight: 600;
    font-size: 18px;
    color: #303133;
    line-height: 50px;
  }
  .el-drawer__body {
    padding: 0 24px 64px 36px;
    .el-form-item {
      display: block;
    }
    .basic-info {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      .part-title {
        width: 100%;
        height: 30px;
        margin: 20px 0;
        background: rgb(51, 102, 255, 0.05);
        color: #303133;
        font-weight: 600;
        padding-left: 8px;
        &:first-child {
          margin-top: 0;
        }
        .border {
          display: inline-block;
          width: 3px;
          margin-top: 8px;
          height: 14px;
          border-left: 3px solid #3366ff;
        }

        .title {
          display: inline-block;
          margin-left: 6px;
          height: 30px;
        }
      }
      .label {
        display: inline-block;
        margin-left: 35px;
      }
      .basic-info-item {
        display: inline-block;
        margin: 10px 0;
        .value {
          display: inline-block;
          margin-right: 45px;
        }
      }
    }

    .el-divider {
      background-color: #009688;
    }
    .el-divider__text {
      color: #009688;
    }
  }
  .drawer-footer {
    text-align: right;
    padding: 16px;
    z-index: 9;
    background-color: #fff;
    box-shadow: 0 -2px 8px 0 rgba(200, 201, 204, 0.3);
    bottom: 0;
    position: fixed;
    width: 580px;
    right: 0;
  }
}
</style>
