<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
      <el-row>
        <direct-search
          ref="form"
          :formDefaults="formDefaults"
          :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }"
          :forms="searchList"
          @handleSearch="handleFormSearch"
        />
      </el-row>
      <!-- :tab-type="'index'"
      :tab-label="'序号'"-->
      <el-col style="padding:15px 0">
        <el-table-self
          :columns="columns"
          :current-page="pageIndex"
          :list-loading="listLoading"
          :table-data="dataList"
          :total-count="total"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :tab-index="tabIndex"
          @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange"
        />
      </el-col>

      <draw-form
        ref="drawForm"
        :forms="drawForms"
        :inline-flag="true"
        @handleConfirm="handleConfirm"
        :label-width="'110'"
        :title="drawTitle"
      ></draw-form>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import drawForm from "@/components/FormComponents/draw";
import paginationMixin from "@/components/TabComponents/mixin";
import { getAllGroupList } from "@/views/groups/api";
import { createSubordinate } from "@/api/user";
import { getUsersList, saveUsers, deleteUsers, putUsers } from "./api";
import date from "@/utils/date";

export default {
  components: {
    elTableSelf,
    drawForm,
    directSearch
  },
  mixins: [paginationMixin],
  data() {
    return {
      groupList: [],
      drawTitle: "",
      listLoading: false,
      total: 0,
      id: 0,
      uploadObj: {},
      auditEdit: null,
      cacheForm: {},
      formDefaults: {},
      drawForms: [
        {
          type: "input",
          class: "inputMore9",
          label: "账号",
          prop: "name",
          placeholder: `请填写账号`
        },
        {
          type: "input",
          class: "inputMore9",
          label: "密码",
          prop: "password",
          placeholder: `请填写密码`
        },
        {
          type: "input",
          class: "inputMore9",
          label: "姓名",
          prop: "realName",
          placeholder: `请填写姓名`
        },
        {
          type: "input",
          class: "inputMore9",
          label: "手机",
          prop: "mobile",
          placeholder: `请填写手机`
        },
        {
          type: "select",
          class: "inputMore9",
          label: "性别",
          prop: "sex",
          placeholder: `请选择性别`,
          opts: [
            {
              label: "男",
              value: 1
            },
            {
              label: "女",
              value: 2
            }
          ]
        },
        {
          type: "select",
          class: "inputMore9",
          label: "所属公司",
          prop: "subCom",
          placeholder: `请选择所属公司`,
          opts: [
            {
              label: "总部",
              value: 0
            },
            {
              label: "蚌埠分公司",
              value: 1
            },
            {
              label: "合肥分公司",
              value: 2
            },
            {
              label: "淮南分公司",
              value: 3
            }
          ]
        },
        {
          type: "select",
          class: "inputMore9",
          dataOrigin: "groupId",
          label: "部门",
          prop: "groupId",
          diyLabel: "name",
          diyValue: "id",
          isSelect: true,
          placeholder: `请选择部门`,
          opts: []
        },
        {
          type: "select",
          class: "inputMore9",
          label: "角色",
          prop: "roleId",
          placeholder: `请选择角色`,
          opts: [
            {
              label: "角色1",
              value: 0
            },
            {
              label: "角色11",
              value: 1
            }
          ]
        },
        {
          type: "input",
          class: "inputMore9",
          label: "职务",
          prop: "duty",
          placeholder: `请填写职务`
        },
        {
          type: "date",
          class: "inputMore9",
          label: "出生日期",
          prop: "birthday",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择出生日期`
        },
        {
          type: "date",
          class: "inputMore9",
          label: "入职时间",
          prop: "entryTime",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择入职时间`
        },
        {
          type: "date",
          class: "inputMore9",
          label: "离职日期",
          prop: "leaveTime",
          valueFormat: "yyyy-MM-dd",
          placeholder: `请选择离职日期`
        }
        // {
        //   type: "input",
        //   class: "inputMore9",
        //   label: "可用-able，不可用-unable",
        //   prop: "status",
        //   placeholder: `请填写可用-able，不可用-unable`,
        // },
      ],
      fileList: [],
      dataList: [],
      columns: [
        //   formatter(row) {
        //     return row.type == 1 ? '代理商' : row.type == 2 ? '分享商' : row.type == 3 ? '员工' : ''
        {
          value: "name",
          width: 100,
          label: "账号"
        },
        {
          value: "realName",
          label: "姓名"
        },
        {
          value: "sex",
          label: "性别",
          formatter(row) {
            return row.sex == 1 ? "男" : row.sex == 2 ? "女" : "";
          }
        },
        {
          value: "mobile",
          label: "手机"
        },
        {
          value: "subCom",
          label: "所属公司",
          formatter(row) {
            return row.subCom == 0
              ? "总部"
              : row.subCom == 1
              ? "蚌埠分公司"
              : row.subCom == 2
              ? "合肥分公司"
              : row.subCom == 3
              ? "淮南分公司"
              : "";
          }
        },
        {
          value: "groupId",
          label: "部门"
        },
        {
          value: "duty",
          label: "职务"
        },
        {
          value: "entryTime",
          label: "入职时间"
        },
        {
          value: "birthday",
          label: "出生日期"
        },
        {
          label: "注册时间",
          value: "createdAt",
          formatter(row) {
            return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "更新时间",
          value: "updatedAt",
          formatter(row) {
            return date.dateFormat(row.updatedAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          label: "操作",
          fixed: "right",
          width: 180,
          operType: "button",
          operations: [
            {
              label: "编辑",
              type: "primary",
              func: this.handleEdit
            },
            {
              label: "删除",
              type: "danger",
              func: this.handleDelete
            }
          ]
        }
      ],
      searchList: [
        {
          label: "姓名",
          labelWidth: "120px",
          type: "input",
          prop: "realName"
        },
        {
          label: "",
          labelWidth: "20px",
          type: "button",
          func: this.handleFormSearch,
          value: "查询",
          color: "primary"
        },
        {
          label: "",
          fixed: "right",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              value: "查询",
              color: "primary"
            },
            {
              type: "btn",
              labelWidth: "0px",
              color: "warning",
              value: "新增",
              icon: "el-icon-plus",
              func: this.handleAdd
            }
          ]
        }
      ]
    };
  },
  watch: {},
  mounted() {
    getAllGroupList().then(res => {
      if (res.code == 200) {
        this.groupList = res.data;
        for (let i = 0; i < this.drawForms.length; i++) {
          if (this.drawForms[i].dataOrigin == "groupId") {
            this.drawForms[i].opts = res.data;
          }
        }
      }
      this.listLoading = false;
    });
    this.handleSearch();
  },
  methods: {
    handleAdd() {
      this.drawTitle = "新增";
      this.$refs.drawForm.open();
      this.$refs.drawForm.initforms({
        name: "1",
        password: "1",
        realName: "1",
        sex: 1,
        mobile: "13101010202",
        groupId: 1,
        roleId: 1,
        subCom: "1",
        duty: "1",
        birthday: "2023-11-08",
        entryTime: "2023-11-16",
        leaveTime: "2023-11-25"
      });
    },
    handleEdit(row) {
      this.drawTitle = "编辑";
      this.$refs.drawForm.open();
      this.$refs.drawForm.initforms(row);
    },

    handleDelete(row) {
      this.$confirm(`确定要删除${row.realName}吗？`, "提示", {
        type: "warning"
      })
        .then(() => {
          this.tableLoading = true;
          deleteUsers({ id: row.id })
            .then(res => {
              if (res.code == 200) {
                this.$message.success("删除成功");
                this.handleSearch();
              } else {
                this.tableLoading = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },

    handleConfirm(row) {
      if (row.id) {
        putUsers(row).then(res => {
          if (res.code == 200) {
            this.$message.success("更新成功！");
            this.handleSearch();
            this.$refs.drawForm.close();
          }
        });
      } else {
        saveUsers(row).then(res => {
          if (res.code == 200) {
            this.$message.success("创建成功！");
            this.handleSearch();
            this.$refs.drawForm.close();
          }
        });
      }
    },

    handleFormSearch(form) {
      this.pageIndex = 1;
      this.handleSearch(form);
    },

    tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

    handleSearch(form) {
      this.cacheForm = this.cacheForm || form;
      const params = Object.assign(this.cacheForm, form);
      params.page = this.pageIndex;
      params.pageSize = this.pageSize;
      getUsersList(params).then(res => {
        if (res.code == 200) {
          this.total = res.data.total;
          this.dataList = res.data.records;
        }
        this.listLoading = false;
      });
    }
  }
};
</script>

<style lang="scss" scope>
.page-container .page-main {
  padding-top: 30px 25px;
}
.statistics {
  text-align: center;
  height: 120px;
  line-height: 120px;
}
</style>
