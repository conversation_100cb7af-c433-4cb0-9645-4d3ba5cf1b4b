<template>
  <div>
    <section class="file-preview__wrapper" :class="{'is-upload':uploadEnabled}">
      <article
        v-for="(fileItem,index) in filePreviewList"
        :key="index"
        class="preview__item"
        :class="[size]"
      >
        <div class="img-preview__item" v-if="fileItem.url">
          <img v-if="type=='baseImg'" class="item-img" :src="'data:image/png;base64,'+ fileItem.url" alt />
          <img v-else class="item-img" :src="fileItem.url" alt />
          <span class="item-action">
            <span class="action-preview" @click.stop="handlePictureCardPreview(fileItem.url)">
              <i class="el-icon-zoom-in"></i>
            </span>
            <span
              class="action-delete"
              @click.stop="removeHandle(fileItem.url,index)"
              v-if="removeEnabled"
            >
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </div>

        <div class="file-preview__item" v-else>
          <i class="item-file el-icon-document" :src="fileItem.url"></i>
          <span class="item-txt">{{fileItem.filename}}</span>
          <span class="item-action">
            <span class="action-preview" @click.stop="handleFilePreview(fileItem.url)">
              <i class="el-icon-zoom-in"></i>
            </span>
            <span
              class="action-delete"
              @click.stop="removeHandle(fileItem.url,index)"
              v-if="removeEnabled"
            >
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </div>
      </article>

      <footer v-if="uploadEnabled" class="preview__item" :class="[size]">
        <slot name="upload"></slot>
      </footer>
    </section>

    <el-dialog :visible.sync="dialogVisible" append-to-body top="3%">
      <img v-if="type=='baseImg'" width="100%" class="imgStyle" :src="'data:image/png;base64,'+ dialogImageUrl" alt />
      <img v-else width="100%" class="imgStyle" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    fileList: {
      default: []
    },
    removeEnabled: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: ''
    },
    uploadEnabled: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'medium'
    }
  },
  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  components: {},
  created() {},
  mounted() {},
  methods: {
    // ------------------ 图片预览 ------------------
    getFileType(filePath) {
      //获取最后一个.的位置
      var index = filePath.lastIndexOf('.')
      //获取后缀
      var ext = filePath.substr(index + 1)
      return (
        [
          'png',
          'jpg',
          'jpeg',
          'bmp',
          'gif',
          'webp',
          'psd',
          'svg',
          'tiff'
        ].indexOf(ext.toLowerCase()) >= 0
      )
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handleFilePreview(file) {
      window.open(file)
    },
    removeHandle(file, index) {
      this.$emit('removeHandle', file, index)
    }
    // ------------------ 图片预览 ------------------
  },
  computed: {
    filePreviewList: {
      get() {
        let fileList = this.fileList || []
        if (fileList.length > 0) {
          return fileList.map(item => {
            item = item && item.indexOf('url') >= 0 ? JSON.parse(item) : item
            return item instanceof Object ? item : { url: item, name: '' }
          })
        }
      }
    }
  }
}
</script>

<style lang="scss">
.file-preview__wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  &.is-upload {
    justify-content: unset;
  }
  .preview__item {
    &.mini {
      width: 60px;
      height: 60px;
      margin-right: 6px;
      margin-bottom: 6px;
    }
    &.small {
      width: 80px;
      height: 80px;
      margin-right: 8px;
      margin-bottom: 8px;
    }
    &.medium {
      width: 100px;
      height: 100px;
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  .img-preview__item,
  .file-preview__item,
  .my-upload-wrapper,
  .el-upload,
  .el-upload-dragger {
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }
  .img-preview__item,
  .file-preview__item {
    border: 1px dashed #adadad;
    border-radius: 5px;
    overflow: hidden;
  }
  .file-preview__item {
    flex-direction: column;
  }

  .item-img {
    width: 100%;
    height: 100%;
  }
  .item-file {
    // font-size: 36px;
    font-size: 24px;
  }
  .item-txt {
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .item-action {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    // background: rgba(0 ,0, 0,0.3);
    font-size: 18px;
    color: #fff;

    .action-delete,
    .action-preview {
      display: none;
      margin: 0 10px;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.3);
      .action-delete,
      .action-preview {
        display: block;
      }
    }
  }
  .el-icon-plus {
    font-size: 18px;
  }
}

.imgStyle {
  height: 100%;
}
</style>
