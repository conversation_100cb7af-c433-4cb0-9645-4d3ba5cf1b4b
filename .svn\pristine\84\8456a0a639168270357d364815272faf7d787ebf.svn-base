import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken, getDbKey, setDb<PERSON>ey, removetDb<PERSON><PERSON> } from '@/utils/auth'
import router, { resetRouter } from '@/router'

const state = {
  token: getToken(),
  name: '',
  userInfo: {},
  msgListFlag:false,
  detailVisible:false,
  userInfoId: '',
  avatar: '',
  dbKey: getDbKey(),
  introduction: '',
  roles: []
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },

  SET_MSG_FLAG: (state, flag) => {
    state.msgListFlag = flag
  },


  SET_detailVisible: (state, flag) => {
    state.detailVisible = flag
  },

  SET_userInfoId: (state, id) => {
    state.userInfoId = id
  },

  
  CLEAR_USER_INFO: (state, data) => {
    state.userInfo = {}
  },
  

  SET_DBKEY: (state, dbKey) => {
    state.dbKey = db<PERSON><PERSON>
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },

  SET_USER_INFO: (state, data) => {
    state.userInfo = Object.assign(state.userInfo,data)   
  },

  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password, dbKey } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password, dbKey: dbKey }).then(res => {
        commit('SET_TOKEN', res.access_token)
        commit('SET_DBKEY', res.dbKey)
        setToken(res.access_token)
        setDbKey(res.dbKey)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  setUserInfo({ commit }, obj) {
    commit('SET_USER_INFO', obj)
  },
  clearUserInfo({ commit }) {
    commit('CLEAR_USER_INFO')
  },

  

  setMsgFlag({ commit }, flag) {
    commit('SET_MSG_FLAG', flag)
  },

  setUserInfoId({ commit }, id) {
    commit('SET_userInfoId', id)
  },
  

  setDetailVisible({ commit }, flag) {
    commit('SET_detailVisible', flag)
  },
  

  // user logout
  logout({ commit, state, dispatch }) {
    removeToken()
    removetDbKey()
    commit('SET_TOKEN', '')
    commit('SET_USER_INFO', {})
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      removetDbKey()
      resolve()
    })
  },

  // dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    return new Promise(async resolve => {
      const token = role + '-token'

      commit('SET_TOKEN', token)
      setToken(token)

      const { roles } = await dispatch('getInfo')

      resetRouter()

      // generate accessible routes map based on roles
      const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })

      // dynamically add accessible routes
      router.addRoutes(accessRoutes)

      // reset visited views and cached views
      dispatch('tagsView/delAllViews', null, { root: true })

      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
