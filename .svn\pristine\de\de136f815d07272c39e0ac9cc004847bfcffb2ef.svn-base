import {
  fetch,
  post,
  put,
  formDataPost,
  deleteHttp
} from '@/utils/request'

// 查询列表
export const getUsersList = data => {
  return fetch(`/users`, data)
}

// 保存
export const saveUsers = data => {
  return post(`/users`, data)
}

// 更新
export const putUsers = data => {
  return put(`/users`, data)
}

// 详情
export const getDetail = data => {
  return fetch(`/users/${data.id}`)
}

// 删除
export const deleteUsers = data => {
  return deleteHttp(`/users/${data.id}`)
}


export const logoutUser = data => {
  return fetch(`/users/logout`, data)
}



// 下载模板
export const getUserTemplate = data => {
  return fetch(`/users/file/downExcel`, data, 'blob')
}


export const uploadInfo = data => {
  return post(`/users/file/upload`, data)
}



//合同信息
export const contractDetail = data => {
  return fetch(`/contract/getContract`, data)
}

