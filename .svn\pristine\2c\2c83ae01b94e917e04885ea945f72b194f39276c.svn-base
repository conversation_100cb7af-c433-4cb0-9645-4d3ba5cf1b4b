import Vue from 'vue'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css
import {
  createSocket,
  sendWSPush
} from './utils/webSocketUtil'
import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import echarts from "echarts"; // 引入echarts
Vue.prototype.$echarts = echarts; // 引入组件（将echarts注册为全局）
import Viewer from 'v-viewer'
import 'viewerjs/dist/viewer.css'
Vue.use(Viewer)
Viewer.setDefaults({
  Options: {
    'inline': true,
    'button': false,
    'navbar': false,
    'title': false,
    'toolbar': false,
    'tooltip': false,
    'movable': false,
    'zoomable': false,
    'rotatable': false,
    'scalable': false,
    'transition': false,
    'fullscreen': false,
    'keyboard': false,
    'url': 'data-source'
  }
})

Vue.use(Element, {
  size: 'small'
})

import audio from '@/components/mAudio/audio'

Vue.use(audio)

Vue.prototype.$wsConnection = createSocket
Vue.prototype.$sendWSPush = sendWSPush

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
