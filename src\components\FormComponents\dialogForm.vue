<template>
  <el-dialog center :title="title" :class="dialogComponent" :visible.sync="visible" :width="width" :close-on-click-modal="closeModal" @close="close">
    <div class="main-dialog-sict">
      <el-form ref="form" :rules="rules" :model="form" label-position="right" :label-width="labelWidth">
        <el-row>
          <template v-for="(item,index) in formData">
            <template v-if="!item.hidden">
              <el-col :key="index" :span="item.spanCount?item.spanCount:countLine" :class="item.class" :style="item.isDash?'border-top:1px dashed #ccc;padding-top:20px;margin:0':''">
                <el-form-item :key="index" :label="item.name?`${item.name}：`:''" :label-width="item.name?item.labelWidth:'10px'" :prop="item.field" :rules="item.rules" :class="item.className" :style="item.style">
                  <!-- 按钮控制 -->
                  <el-button v-if="item.operation" class="operBtn" :type="item.operation.type?item.operation.type:'text'" @click="item.operation.func">
                    {{ item.operation.text?item.operation.text:'新增' }}
                  </el-button>

                  <!-- 提示 -->
                  <el-tooltip v-if="item.tips" slot="label" effect="dark" :content="item.tips" placement="top">
                    <span>{{ item.name }}：</span>
                  </el-tooltip>

                  <!-- 输入框 input -->
                  <el-input v-if="item.type === 'input'" v-model="form[item.field]" :type="item.inputType?item.inputType:''" :disabled="item.disabled" :placeholder="item.placeholder" :resize="item.resize" :maxlength="item.maxlength" :rows="item.rows" :autosize="item.autosize" @input="item.func">
                    <span v-if="item.unit" :slot="item.slot?item.slot:'append'">{{ item.unit }}</span>
                  </el-input>

                  <!-- 数字输入框 -->
                  <el-input-number v-else-if="item.type === 'number'" v-model="form[item.field]" :min="item.min" :max="item.max" :precision="item.precision" :controls="item.controls || false" :disabled="item.disabled" :placeholder="item.placeholder" :maxlength="item.maxlength" @input="item.func" />

                  <!-- 模糊查询输入 -->
                  <el-autocomplete v-else-if="item.type === 9" v-model="form[item.field]" popper-class="my-autocomplete" :fetch-suggestions="item.func" :placeholder="item.placeholder" clearable @select="item.selectfun">

                    <template slot-scope="{ item }">
                      <div class="name">{{ item.value }}
                        <span v-if="item.specification">({{ item.specification }})</span>
                      </div>
                      <span class="description">{{ item.description }}</span>
                    </template>
                  </el-autocomplete>

                  <!-- 模糊查询选择 -->
                  <el-select v-else-if="item.type === 'remote'" v-model="form[item.field]" filterable remote reserve-keyword :placeholder="item.placeholder" :remote-method="item.remoteFunc" @change="item.func?item.func($event):{}">
                    <el-option v-for="(opt,optIndex) in item.opts" :key="optIndex" :label="opt.label" :value="opt.value" />
                  </el-select>

                  <!-- 日期 -->
                  <el-date-picker v-else-if="item.type === 'date'" v-model="form[item.field]" :type="item.dateType?item.dateType:'date'" :placeholder="item.placeholder" :picker-options="item.options" :format="item.format" :value-format="item.valueFormat" @change="item.func?item.func($event):{}" />

                  <!-- 单选框 -->
                  <el-radio v-for="(opt,optIndex) in item.opts" v-else-if="item.type === 'radio'" :key="optIndex" v-model="form[item.field]" :class="item.class" :label="opt.value" @change="item.func?item.func($event):{}">{{ opt.label }}
                  </el-radio>

                  <!-- 多选框 -->
                  <el-checkbox-group v-else-if="item.type === 'checkbox'" v-model="form[item.field]">
                    <el-checkbox v-for="(opt,optIndex) in item.opts" :key="optIndex" :label="opt.label" />
                  </el-checkbox-group>

                  <!-- 级联 -->
                  <el-cascader v-else-if="item.type === 'cascader'" v-model="form[item.field]" :options="item.list" :clearable="true" :filterable="true" />

                  <!-- 选择器 -->
                  <el-select v-else-if="item.type === 'select'" v-model="form[item.field]" :filterable="item.filterable" :clearable="item.clearable" :multiple="item.multiple" :placeholder="item.placeholder?item.placeholder:'请选择'" :value-key="item.key?item.key:'value'" :disabled="item.disabled" @change="item.func?item.func($event):{}">
                    <el-option v-for="(opt,optIndex) in item.opts" :key="optIndex" :label="opt.label" :value="opt.value" :disabled="opt.disabled">
                      <span style="float: left">{{ opt.label }}</span>
                      <span style="float: right; color: #8492a6; font-size: 10px">{{ opt.description }}</span>
                    </el-option>
                  </el-select>

                  <img v-else-if="item.type === 'image'" :src="form[item.field]">
                  <span v-else-if="item.type === 'span'">{{ form[item.field] }}</span>

                  <!-- 树菜单 -->
                  <el-tree v-else-if="item.type === 'tree'" ref="tree" :data="item.data" :props="item.defaultProps" show-checkbox node-key="id" />
                </el-form-item>
              </el-col>
            </template>
          </template>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button v-if="btnCancel" @click="visible = false;">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  props: {
    closeModal: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '系统提示'
    },
    countLine: {
      // 一行排列几个
      default: 24
    },
    width: {
      type: String,
      default: '365px'
    },
    labelWidth: {
      type: String,
      default: '100px'
    },
    btnCancel: {
      type: Boolean,
      default: true
    },
    formData: {
      type: Array
    },
    formEdit: {
      type: Object
    },

    rules: {
      type: Object
    },
    dialogComponent: {
      type: String,
      default: 'dialog-component'
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      form: ''
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.initforms()
      }
    }
  },
  methods: {
    open(obj) {
      this.visible = true
    },

    close() {
      this.loading = false
      this.visible = false
    },

    handleConfirm() {
      this.loading = true
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('handleConfirm', this.form)
        } else {
          this.loading = false
        }
      })
    },

    // 初始化整个表单（可在初始化时赋值）
    initforms(formEdit) {
      const form = {}
      this.formData.forEach(item => {
        if (!item.field || item.hidden) return false
        if (item.type === 'select-input' || item.type === 'input-input') {
          form[item.field1] = ''
          form[item.field2] = ''
        } else if (item.type === 'checkbox' || item.type === 'cascader') {
          form[item.field] = []
        } else {
          form[item.field] = ''
        }
      })
      if (formEdit) {
        this.form = Object.assign(form, formEdit)
      }
      if (this.formEdit) {
        this.form = Object.assign(form, this.formEdit)
      } else {
        this.form = Object.assign({}, form)
      }
      this.loading = false

      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },

    // 给表单部分字段赋值（此时表单已初始化）
    initFields(obj) {
      for (const key in obj) {
        this.form[key] = obj[key]
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-select {
    display: inline-block;
    position: relative;
    width: 100%;
}
</style>
<style  lang="scss">
.main-sict{
    .el-dialog__title {
        font-weight: 700 !important;
    }
}
</style>
