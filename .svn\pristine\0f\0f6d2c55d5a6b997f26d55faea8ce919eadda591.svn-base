<template>
  <el-row class="page-container">
    <el-row class="page-main">
      <el-row v-if="showSettingsFlag">
        <el-table-self
          
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :columns="columns"
          :data="tableData"
        ></el-table-self>
      </el-row>
      <el-row v-else>
        <permission
          ref="permission"
          @back="showSettingsFlag = true"
        ></permission>
      </el-row>
    </el-row>

    <dialog-form
      ref="audit"
      :title="formTitle"
      :form-data="auditData"
      :form-edit="auditEdit"
      @handleConfirm="handleConfirm"
    />
  </el-row>
</template>
<script>
import {
  getStaffList,
  deleteStaff,
  deleteRole,
  updateStaffStatus,
  updateRoleStatus,
  updateStaffRole,
  updateStaff,
  addStaff,
  findByParentId,
  addPosition
} from "./api";
// import { arr2Tree } from "@/utils/arr";
import date from "@/utils/date";
import { convertValue2Label } from "@/utils";
import { deptTypeEnum } from "./store";
import { mapGetters } from "vuex";
import store from "@/store";
import permission from "./components/permission";
import elTableSelf from "@/components/TabComponents/TreeTable/index";
import dialogForm from "@/components/FormComponents/dialogForm";
export default {
  components: {
    elTableSelf,
    dialogForm,
    permission
  },
  computed: {
    ...mapGetters(["userInfo"])
  },
  data() {
    return {
      tableData: [],
      columns: [
        {
          value: "name",
          label: "部门/岗位",
          width: 400
        },
        {
          value: "type",
          label: "部门类型",
          formatter(row) {
            return convertValue2Label(deptTypeEnum, row.type);
          }
        },
        {
          value: "createdAt",
          label: "日期",
          formatter(row) {
            return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
          }
        },
        {
          value: "weight",
          label: "显示顺序"
        },
        {
          label: "操作",
          fixed: "right",
          width: 390,
          operType: "text",
          operations: [
            {
              func: this.handleAddDept,
              class: "text-btn",
              label: "添加部门",
              isHidden(row) {
                return (
                  row.dataType == "post" ||
                  store.getters.userInfo.access.indexOf("Role_add") == -1
                );
              }
            },
            {
              func: this.handleAddPos,
              class: "text-btn",
              label: "添加岗位",
              isHidden(row) {
                return (
                  row.dataType == "post" ||
                  store.getters.userInfo.access.indexOf("Role_add") == -1
                );
              }
            },
            {
              func: this.handlePermission,
              class: "text-btn",
              label: "设置操作权限",
              isHidden(row) {
                return (
                  row.dataType == "dept" ||
                  row.parentId == 0 ||
                  store.getters.userInfo.access.indexOf("Role_power") == -1
                );
              }
            },
            {
              func: this.handleAddOrUpdate,
              class: "text-btn",
              formatter(row) {
                return {
                  label: "编辑"
                };
              },
              isHidden(row) {
                return store.getters.userInfo.access.indexOf("Role_edit") == -1;
              }
            },
            {
              func: this.handleStatus,
              class: "text-btn-warning",
              formatter(row) {
                return {
                  label: row.disabled == 0 ? "禁用" : "启用"
                };
              },
              isHidden(row) {
                return (
                  store.getters.userInfo.access.indexOf("Role_state") == -1
                );
              }
            },
            {
              func: this.handleDelete,
              class: "text-btn-danger",
              formatter(row) {
                return {
                  label: row.deleted ? "恢复" : "删除"
                };
              },
              isHidden(row) {
                return store.getters.userInfo.access.indexOf("Role_del") == -1;
              }
            }
          ]
        }
      ],
      formTitle: "",
      auditEdit: null,
      auditData: [],
      editAuditData: [
        {
          type: "input",
          name: "部门名称",
          field: "name"
        },
        {
          type: "input",
          name: "排序",
          field: "weight"
        }
      ],

      deptAuditData: [
        {
          type: "span",
          name: "上级部门",
          field: "parentName"
        },
        {
          type: "input",
          name: "部门名称",
          field: "name"
        },
        {
          type: "select",
          name: "类型",
          field: "type",
          opts: deptTypeEnum
        },
        {
          type: "input",
          name: "排序",
          field: "weight"
        }
      ],

      posAuditData: [
        {
          type: "input",
          name: "岗位名称",
          field: "name"
        },
        {
          type: "select",
          name: "复用权限",
          field: "roleId",
          opts: []
        }
      ],
      showSettingsFlag: true,
      dialogType: ""
    };
  },
  mounted() {
    this.handleSearch();
  },
  methods: {
    handleSearch() {
      getStaffList().then(res => {
        if (res.code == 200) {
          this.tableData = res.data;
        }
      });
    },
    // 设置操作权限
    handlePermission(row) {
      this.showSettingsFlag = false;
      this.$nextTick(() => {
        this.$refs.permission.initData(row);
      });
    },

    handleAddDept(row) {
      this.deptAuditData[0].hidden = false;
      this.deptAuditData[2].hidden = row.parentId != 0;
      this.auditData = this.deptAuditData.concat([]);
      this.formTitle = "添加部门";
      this.dialogType = "addDept";
      this.auditEdit = {
        name: "",
        depth: row.depth + 1,
        weight: "",
        dataType: "dept",
        deptType: row.deptType,
        parentName: row.name,
        parentId: row.id
      };
      this.$refs.audit.open();
    },

    async handleAddPos(row) {
      await findByParentId({ parentId: row.id }).then(res => {
        if (res.code == 200) {
          this.posAuditData[1].opts = [];
          res.data.forEach(item => {
            this.posAuditData[1].opts.push({
              label: item.name,
              value: item.roleId
            });
          });
        }
      });

      this.auditData = this.posAuditData.concat([]);
      this.formTitle = "添加岗位";
      this.dialogType = "addPos";
      this.auditEdit = {
        name: "",
        weight: "",
        deptType: row.deptType,
        depth: row.depth + 1,
        dataType: "post",
        parentName: row.name,
        parentId: row.id
      };
      this.$refs.audit.open();
    },

    // 编辑
    handleAddOrUpdate(row) {
      console.log(row);
      // 编辑根节点，编辑部门，编辑岗位
      if (row.dataType == "dept") {
        this.deptAuditData[0].hidden = true;
        this.auditData = this.deptAuditData.concat([]);
        this.formTitle = "编辑部门";
      } else if (row.dataType == "post") {
        this.auditData = this.posAuditData.concat([]);
        findByParentId({ parentId: row.parentId }).then(res => {
          if (res.code == 200) {
            this.posAuditData[1].opts = [];
            res.data.forEach(item => {
              this.posAuditData[1].opts.push({
                label: item.name,
                value: item.roleId
              });
            });
          }
        });
        this.formTitle = "编辑岗位";
      } else {
        this.auditData = this.editAuditData.concat([]);
        this.formTitle = "编辑名称";
      }

      this.dialogType = "edit";
      this.auditEdit = Object.assign({}, row);
      this.$refs.audit.open();
    },

    // 启用禁用
    handleStatus(row) {
      this.$confirm(
        `确定要${row.disabled == 0 ? "禁用" : "启用"}这行内容吗？`,
        "提示",
        {
          type: "warning"
        }
      )
        .then(() => {
          // enable-启用，disable-禁用

          this.tableLoading = true;

          if (row.dataType == "dept") {
            const data = {
              id: row.id,
              disabled: row.disabled == 0 ? 1 : 0
            };
            updateStaffStatus(data)
              .then(res => {
                if (res.code == 200) {
                  this.$message.success(
                    `${row.disabled == 0 ? "禁用" : "启用"}成功`
                  );
                  this.handleSearch();
                } else {
                  this.$message.warning(res.message);
                  this.tableLoading = false;
                }
              })
              .catch(err => {
                console.log(err);
                this.tableLoading = false;
              });
          } else {
            const data = {
              id: row.roleId,
              disabled: row.disabled == 0 ? 1 : 0
            };
            updateRoleStatus(data)
              .then(res => {
                if (res.code == 200) {
                  this.$message.success(
                    `${row.disabled == 0 ? "禁用" : "启用"}成功`
                  );
                  this.handleSearch();
                } else {
                  this.$message.warning(res.message);
                  this.tableLoading = false;
                }
              })
              .catch(err => {
                console.log(err);
                this.tableLoading = false;
              });
          }
        })
        .catch(() => {});
    },

    // 删除
    handleDelete(row) {
      this.$confirm(
        `确定要删除该部门吗？删除后统计信息以及相关信息将全部删除`,
        "提示",
        {
          type: "warning"
        }
      )
        .then(() => {
          this.tableLoading = true;
          if (row.dataType == "dept") {
            deleteStaff({ id: row.id })
              .then(res => {
                if (res.code == 200) {
                  this.$message.success(`删除成功`);
                  this.handleSearch();
                } else {
                  this.$message.warning(res.message);
                  this.tableLoading = false;
                }
              })
              .catch(err => {
                console.log(err);
                this.tableLoading = false;
              });
          } else {
            deleteRole({ id: row.roleId })
              .then(res => {
                if (res.code == 200) {
                  this.$message.success(`删除成功`);
                  this.handleSearch();
                } else {
                  this.$message.warning(res.message);
                  this.tableLoading = false;
                }
              })
              .catch(err => {
                console.log(err);
                this.tableLoading = false;
              });
          }
        })
        .catch(() => {});
    },

    handleConfirm(form) {
      if (this.dialogType == "edit") {
        if (form.dataType == "dept") {
          updateStaff(form).then(res => {
            if (res.code == 200) {
              this.$message.success(res.message);
              this.$refs.audit.close();
              this.handleSearch();
            } else {
              this.$message.warning(res.message);
              this.$refs.audit.loading = false;
            }
          });
        } else {
          form.groupId = form.parentId;
          updateStaffRole(form).then(res => {
            if (res.code == 200) {
              this.$message.success(res.message);
              this.$refs.audit.close();
              this.handleSearch();
            } else {
              this.$message.warning(res.message);
              this.$refs.audit.loading = false;
            }
          });
        }
      } else if (this.dialogType == "addDept") {
        addStaff(form).then(res => {
          if (res.code == 200) {
            this.$message.success(res.message);
            this.$refs.audit.close();
            this.handleSearch();
          } else {
            this.$message.warning(res.message);
            this.$refs.audit.loading = false;
          }
        });
      } else if (this.dialogType == "addPos") {
        form.groupId = form.parentId;
        addPosition(form).then(res => {
          if (res.code == 200) {
            this.$message.success(res.message);
            this.$refs.audit.close();
            this.handleSearch();
          } else {
            this.$message.warning(res.message);
            this.$refs.audit.loading = false;
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  min-height: calc(100vh - 134px);
  font-family: PingFangSC-Medium;
  font-weight: 700;

  width: 100%;
  justify-content: center;
  align-items: center;
  font-size: 32px;
}
</style>
