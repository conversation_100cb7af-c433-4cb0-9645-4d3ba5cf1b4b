import { fetch, post, put, del } from '@/utils/request'

// 获取业绩列表
export const getPerformanceList = data => {
  return fetch(`/target/list`, data)
}

// 新增业绩数据
export const addPerformance = data => {
  return post(`/target/add`, data)
}

// 修改业绩数据
export const updatePerformance = data => {
  return put(`/target/update`, data)
}

// 删除业绩数据
export const deletePerformance = id => {
  return del(`/target/delete/${id}`)
}

// 目标达成表上传
export const uploadTargetFile = data => {
  return post(`/target/file/upload`, data)
}

// 目标达成表模板下载
export const getTargetTemplate = data => {
  return fetch(`/target/file/downExcel`, data, 'blob')
}
