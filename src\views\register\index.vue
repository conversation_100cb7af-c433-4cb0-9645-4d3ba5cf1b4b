<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
      <el-row>
        <direct-search
          ref="form"
          :label-position="'right'"
          :form-style="{'text-align':'left','margin-bottom':'10px'}"
          :forms="searchList"
          @handleSearch="handleFormSearch"
        />
      </el-row>
      <!-- :tab-type="'index'"
      :tab-label="'序号'"-->
      <el-col style="padding:15px 0">
        <el-table-self
          :columns="columns"
          :current-page="pageIndex"
          :list-loading="listLoading"
          :table-data="dataList"
          :total-count="total"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :tab-index="tabIndex"
          @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange"
        />
      </el-col>

      <dialog-form
        ref="audit"
        :width="'500px'"
        :title="formTitle"
        :form-data="auditData"
        :form-edit="auditEdit"
        :footer-btn="footerBtn"
        @handleConfirm="handleConfirm"
      />
    </el-row>

    <el-dialog title="封禁" :visible.sync="dialogFormVisible" width="450px">
      <el-form :model="form" :rules="rules" ref="ruleForm" label-width="80px">
        <el-form-item label="封禁类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择封禁类型">
            <el-option label="账号" :value="1"></el-option>
            <el-option label="设备" :value="2"></el-option>
            <el-option label="ip" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="封禁理由" prop="reason">
          <el-input type="textarea" :rows="3" v-model="form.reason" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirmStop()">确 定</el-button>
      </div>
    </el-dialog>
  </el-row>
</template>

<script>
import dialogForm from '@/components/DialogComponents/Form'
import elTableSelf from '@/components/TabComponents/index'
import directSearch from '@/components/FormComponents/directSearch'
import paginationMixin from '@/components/TabComponents/mixin'
import {
  getUserAuthList,
  passAuth,
  joinBlack,
  cannotBlack,
  refuseUserContactStatus,
  refuseUserAuthStatus,
  getUserPhotoList
} from '@/api/user'

// getUserAuthList

import date from '@/utils/date'

export default {
  components: {
    elTableSelf,
    dialogForm,
    directSearch
  },

  mixins: [paginationMixin],
  data() {
    return {
      rules: {
        type: [
          { required: true, message: '请选择封禁类型', trigger: 'change' }
        ],
        reason: [{ required: true, message: '请输入理由', trigger: 'blur' }]
      },
      formTitle: '',
      dialogFormVisible: false,
      listLoading: false,
      total: 0,
      pageSize: 50,
      id: 0,
      form: {
        type: '',
        reason: ''
      },
      uploadObj: {},
      auditEdit: null,
      cacheForm: {},
      auditData: [
        {
          type: 'input',
          inputType: 'textarea',
          name: '文案',
          field: 'reasons'
        }
      ],
      footerBtn: [
        {
          value: 1,
          name: '通过'
        }
      ],
      fileList: [],
      dataList: [],
      columns: [
        // {
        //   value: 'id',
        //   width:80,
        //   label: 'id'
        // },
        {
          value: 'userId',
          className: 'openUserInfo',
          func: this.openFromUserInfo,
          label: 'ID'
        },
        {
          value: 'name',
          label: '昵称'
        },
        {
          value: 'createTime',
          label: '创建时间',
          width: 170,
          formatter(row) {
            return date.dateFormat(row.createTime, 'YYYY-MM-DD hh:mm:ss')
          }
        },
        {
          value: 'wxNo',
          label: '微信号'
        },
        {
          label: '头像',
          type: 'img',
          value: 'headImg'
        },
        {
          label: '认证图片',
          type: 'img',
          value: 'imgUrl'
        },
        {
          label: '微信二维码',
          type: 'img',
          value: 'wxNoQrCode'
        },
        {
          value: 'wxStatus',
          label: '微信状态',
          formatter(row) {
            return row.wxStatus == 1
              ? '审核中'
              : row.wxStatus == 2
              ? '驳回'
              : row.wxStatus == 3
              ? '通过'
              : row.wxStatus == 5
              ? '忽略'
              : ''
          }
        },
        {
          value: 'authStatus',
          label: '认证状态',
          formatter(row) {
            return row.authStatus == 1
              ? '审核中'
              : row.authStatus == 2
              ? '驳回'
              : row.authStatus == 3
              ? '通过'
              : ''
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 360,
          operType: 'button',
          operations: [
            {
              label: '通过',
              type: 'primary',
              func: this.pass
            },
            {
              label: '认证驳回',
              type: 'primary',
              func: this.handleView
            },
            {
              label: '微信驳回',
              type: 'primary',
              func: this.handleViewWx
            },
            {
              label: '忽略',
              type: 'primary',
              func: this.handleCannotBlack
            }
            // {
            //   label: '拉黑',
            //   type: 'danger',
            //   func: this.handleDelete
            // }
          ]
        }
      ],
      searchList: [
        {
          label: '创建时间',
          labelWidth: '120px',
          type: 'daterange',
          options: {
            shortcuts: [
              {
                text: '最近一周',
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                  picker.$emit('pick', [start, end])
                }
              },
              {
                text: '最近一个月',
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                  picker.$emit('pick', [start, end])
                }
              },
              {
                text: '最近三个月',
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                  picker.$emit('pick', [start, end])
                }
              }
            ]
          },
          valueFormat: 'yyyy-MM-dd',
          prop: 'timeArr'
        },
        {
          label: '微信审核状态',
          labelWidth: '120px',
          type: 'select',
          opts: [
            {
              label: '待审',
              value: '1'
            },
            {
              label: '已通过',
              value: '3'
            },
            {
              label: '微信驳回',
              value: '2'
            },
            {
              label: '忽略',
              value: '5'
            }
          ],
          prop: 'wxStatus'
        },
        {
          label: '',
          labelWidth: '65px',
          type: 'button',
          func: this.handleFormSearch,
          value: '查询',
          color: 'primary'
        }
      ]
    }
  },
  watch: {},
  mounted() {
     this.$nextTick(() => {
     this.$refs.form.form.wxStatus = '1'
     this.cacheForm.wxStatus = '1'
    })
    this.handleSearch()
  },
  methods: {
    openFromUserInfo(row) {
      this.$store.dispatch('user/setUserInfoId', row.fromUserId)
      this.$store.dispatch('user/setDetailVisible', true)
    },
    openToUserInfo(row) {
      this.$store.dispatch('user/setUserInfoId', row.toUserId)
      this.$store.dispatch('user/setDetailVisible', true)
    },
    handleCannotBlack(row) {
      let data = {
        id: row.id,
        status: 5,
        reasons: ''
      }
      refuseUserContactStatus(data).then(res => {
        if (res.code == 200) {
          this.$message.success('操作成功！')
          this.handleSearch()
        }
      })
    },
    handleConfirmStop() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          joinBlack(this.form).then(res => {
            if (res.code == 200) {
              this.$message.success('操作成功！')
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    handleConfirm() {
      if (this.auditEdit.type == 1) {
        let data = {
          authId: this.auditEdit.authId,
          reasons: this.auditEdit.reasons
        }
        refuseUserAuthStatus(data).then(res => {
          if (res.code == 200) {
            this.$refs.audit.close()
            this.$message.success('操作成功！')
            this.handleSearch()
          }
        })
      } else {
        let data = {
          id: this.auditEdit.id,
          status: 2,
          reasons: this.auditEdit.reasons
        }

        refuseUserContactStatus(data).then(res => {
          if (res.code == 200) {
             this.$refs.audit.close()
            this.$message.success('操作成功！')
            this.handleSearch()
          }
        })
      }
    },

    pass(row, val) {
      let data = {
        id: row.id,
        authId: row.authId
      }
      passAuth(data).then(res => {
        if (res.code == 200) {
          this.$message.success('已通过！')
          this.handleSearch()
        }
      })
    },

    handleView(row) {
      this.formTitle = '审核不通过'
      this.auditEdit = Object.assign({}, row)
      this.auditEdit.reasons =
        '很抱歉，通过人脸识别，您的头像与您本人匹配度过低，请重新选择照片进行人脸识别。'
      this.auditEdit.type = 1
      this.$refs.audit.open()
    },

    handleViewWx(row) {
      this.formTitle = '审核不通过'
      this.auditEdit = Object.assign({}, row)
     
      this.auditEdit.reasons =
        '您所填写的微信不存在或与二维码不相符，请重新提交正确的微信号及微信二维码。'
      this.auditEdit.type = 2
      this.$refs.audit.open()
    },

    handleFormSearch(form) {
      this.pageIndex = 1
      this.handleSearch(form)
    },

    tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize
    },

    initSelect(val) {
      this.searchList[1].hidden = val
      this.searchList[3].hidden = val
      this.searchList[2].hidden = !val
      this.searchList[4].hidden = !val
      if (val) {
        delete this.$refs.form.form.fromUserId
        delete this.$refs.form.form.toUserId
      } else {
        delete this.$refs.form.form.fromUserName
        delete this.$refs.form.form.toUserName
      }
    },

    handleSearch(form) {
      console.log(form)
      if (
        form &&
        form.selectFlag &&
        (form.selectFlag == 0 || form.selectFlag == 1)
      ) {
        console.log(form.selectFlag)
        this.initSelect(Boolean(form.selectFlag == 1 ? true : false))
      }

      this.cacheForm = this.cacheForm || form
      const params = Object.assign(this.cacheForm, form)
      params.page = this.pageIndex
      params.limit = this.pageSize
      if (form && form.timeArr&& form.timeArr.length == 2) {
        params.startTime = form.timeArr[0]
        params.endTime = form.timeArr[1]
        delete params.timeArr
      }else{
        params.startTime = ''
        params.endTime = ''
      }

      params.type = 1

      getUserAuthList(params).then(res => {
        if (res.code == 200) {
          this.total = res.count
          this.dataList = res.data
          if (this.dataList.length > 0) {
            this.dataList.forEach(item => {
              item.headImg = [item.headImg]
              item.imgUrl = [item.imgUrl]
              item.wxNoQrCode = [item.wxNoQrCode]
            })
          }
        }
        this.listLoading = false
      })
    },

    // 删除
    handleDelete(row) {
      this.dialogFormVisible = true
      this.form.userId = row.id
      // this.$confirm(`确认将【${row.profName}】拉黑？`, '提示', {
      //   type: 'warning'
      // })
      //   .then(() => {
      //     const data = { id: row.id }
      //     deleteProf(data).then(res => {
      //       if (res.code === 1) {
      //         this.$message.success(res.msg)
      //         this.handleSearch()
      //       }
      //     })
      //   })
      //   .catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.page-container .page-main {
  padding-top: 30px 25px;
}
.el-select {
  display: inline-block;
  position: relative;
  width: 100%;
}
</style>
