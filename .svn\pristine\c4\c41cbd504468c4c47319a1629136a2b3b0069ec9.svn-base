<template>
  <div id="app">
    <router-view />

    <el-dialog
      title="用户详情"
      :visible.sync="visible"
      width="650px"
      @close="close"
      top="3%"
      style="text-align:center"
      class="user-detail"
    >
      <el-row>
        <el-row
          v-for="(item,index) in detailList"
          :key="index"
          style="margin-top:25px"
          v-if="!item.hidden"
        >
          <el-col
            :span="item.span"
            v-for="itemObj in item.list"
            :key="itemObj.value"
            style="text-align:left"
          >
            <div class="grid-content bg-purple">
              <span>{{itemObj.label}}:</span>
              <!-- {{ itemObj.formatter?itemObj.formatter( detailObj[itemObj.value]):detailObj[itemObj.value] }} -->
              <span>{{ itemObj.formatter?itemObj.formatter(detailObj[itemObj.value]):detailObj[itemObj.value]?detailObj[itemObj.value]:'-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row style="text-align:left;margin-top:25px">
          <el-col style="margin-bottom:10px">照片资料</el-col>
          <el-col>
            <div
              style="display:inline-block"
              v-if="detailObj.avatar && detailObj.avatar.length>0 && detailObj.avatar[0]"
            >
              <span style="vertical-align: top;">头像：</span>
              <img-preview
                size="mini"
                :removeEnabled="false"
                style="display:inline-block"
                :uploadEnabled="false"
                :fileList="detailObj.avatar"
              ></img-preview>
            </div>
            <div
              v-if="detailObj.authPic && detailObj.authPic.length>0 && detailObj.authPic[0]"
              style="display:inline-block;margin-left:80px"
            >
              <span style="vertical-align: top;">认证：</span>
              <img-preview
                size="mini"
                style="display:inline-block"
                :removeEnabled="false"
                :uploadEnabled="false"
                :fileList="detailObj.authPic"
              ></img-preview>
            </div>
          </el-col>
        </el-row>

        <el-row style="text-align:left;margin-top:25px">
          <el-col style="margin-bottom:10px">照片墙</el-col>
          <el-col
            v-if="detailObj.photoList && detailObj.photoList.length>0 && detailObj.photoList[0]"
          >
            <img-preview
              size="mini"
              :removeEnabled="false"
              style="display:inline-block"
              :uploadEnabled="false"
              :fileList="detailObj.photoList"
            ></img-preview>
          </el-col>
        </el-row>

        <!-- <el-row style="text-align:left;margin-top:25px">
          <el-col style="margin-bottom:10px">动态相关</el-col>
          <el-col>
            <img-preview
              size="mini"
              :removeEnabled="false"
              style="display:inline-block"
              :uploadEnabled="false"
              :fileList="detailObj.avatar"
            ></img-preview>
            <img-preview
              size="mini"
              style="display:inline-block"
              :removeEnabled="false"
              :uploadEnabled="false"
              :fileList="detailObj.authPic"
            ></img-preview>
          </el-col>
        </el-row>-->
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import { getUserDetail, getUserPhotoList,getShareInfoDetailList } from '@/api/user'
import imgPreview from '@/components/UploadExcel/imgPreview'
import date from '@/utils/date'
export default {
  name: 'App',
  components: {
    imgPreview
  },
  data() {
    return {
      obj: {},
      detailObj: {},
      formTitle: '',
      visible: false,
      detailList: [
        {
          span: 8,
          list: [
            {
              label: '渠道码',
              value: 'channelCode'
            },
            {
              label: '来源渠道',
              value: 'originType'
            },
            {
              label: '昵称',
              value: 'name'
            }
          ]
        },
        //     {
        //       label: '职业',
        //       value: 'occupation'
        //     }
        // {
        //     label: '在线状态',
        //     value: 'onLineFlag'
        //   },
        // ,
        //     {
        //       label: '居住地',
        //       value: 'liveAddress'
        //     }
        {
          span: 8,
          list: [
            {
              label: 'ID号',
              value: 'id'
            },
            {
              label: '手机号',
              value: 'mobile'
            },
            {
              label: '性别',
              value: 'sex'
            }
          ]
        },

        {
          span: 12,
          list: [
            {
              label: '钻石数',
              value: 'diamondCount'
            },
            {
              label: '积分数',
              value: 'integralCount'
            }
          ]
        },
        {
          span: 12,
          list: [
            {
              label: '今日充值',
              value: 'todayRecharge'
            },
            {
              label: '累计充值',
              value: 'countRecharge'
            }
          ]
        },
        {
          span: 12,
          list: [
            {
              label: '是否认证',
              // （ 0 未认证 1 照片墙认证  2 认证状态丢失 3 头像已认证）
              value: 'authFlag',
              formatter(val) {
                return val == 0
                  ? '未认证'
                  : val == 1
                  ? '照片墙认证'
                  : val == 2
                  ? '认证状态丢失'
                  : val == 3
                  ? '头像已认证'
                  : ''
              }
            },
            {
              label: '会员标识',
              value: 'vipFlag',
              formatter(val) {
                return val == '1' ? '是' : '否'
              }
            }
          ]
        },
        {
          span: 24,
          list: [
            {
              label: '会员结束时间',
              value: 'vipEndTime',
              formatter(val) {
                return date.dateFormat(val, 'YYYY-MM-DD hh:mm:ss')
              }
            }
          ]
        },

        {
          span: 12,
          list: [
            {
              label: '注册时间',
              value: 'registerTime',
              formatter(val) {
                return date.dateFormat(val, 'YYYY-MM-DD hh:mm:ss')
              }
            },
            {
              label: '最后登录时间',
              value: 'lastUpdateTime',
              formatter(val) {
                return date.dateFormat(val, 'YYYY-MM-DD hh:mm:ss')
              }
            }
          ]
        },
        {
          span: 24,
          list: [
            {
              label: '个人介绍',
              value: 'introduction'
            }
          ]
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'detailVisible', 'userInfoId'])
  },
  watch: {
    detailVisible(val, oldVal) {
      if (val) {
        let auditEdit = {}
        this.formTitle = '详情'
        if (this.userInfoId.indexOf('US') > -1) {
          let data = {
             userShareNo:this.userInfoId
          }
           getShareInfoDetailList(data).then(res => { 
             console.log(111111111111);
             console.log(data);
            })
        } else {
          let data = { userId: this.userInfoId }
          getUserDetail(data).then(res => {
            if (res.message == 'success') {
              auditEdit = res.data
              let arr = []
              getUserPhotoList(data).then(res => {
                if (res.code == 200) {
                  let d = res.data
                  if (d.length > 0) {
                    d.forEach(currentItem => {
                      arr.push(currentItem.imgUrl)
                    })
                  }
                }
                this.detailObj = Object.assign({ photoList: arr }, auditEdit)
                this.detailObj.authPic = [this.detailObj.authPic]
                this.detailObj.avatar = [this.detailObj.avatar]
                this.detailObj.headImg = [this.detailObj.headImg]
                this.detailList[5].hidden = !this.detailObj.vipFlag
                this.visible = true
              })
            } else {
              this.close()
            }
          })
        }
      } else {
        this.visible = false
      }
      // this.$refs.audit.open()
    }
  },
  mounted() {
    this.$store.dispatch('user/setDetailVisible', false)
    const _this = this
    window.addEventListener('onmessageWS', function(e) {
      if (e.detail.messageEvent.data !== 'pong') {
        if (_this.$route.name != 'messageManageReply') {
          if (e.detail.messageEvent.data) {
            let obj = JSON.parse(e.detail.messageEvent.data)
            if (_this.$route.name == 'messageManage') {
              _this.$store.dispatch('user/setMsgFlag', true)
            }
            if (obj.event.senderId != _this.userInfo.userId) {
              _this.obj = obj.event
              _this.openMsg()
            }
          }
        }
      }
    })
  },
  methods: {
    close() {
      this.visible = false
      this.$store.dispatch('user/setDetailVisible', false)
    },
    openMsg() {
      const h = this.$createElement
      let _this = this
      this.$notify.info({
        title: '消息',
        message: '您有一条新留言！',
        onClick() {
          _this.doSomeThing()
        }
      })
    },
    doSomeThing() {
      this.$router.push({
        name: 'messageManageReply',
        query: {
          receiverId: this.obj.receiverId,
          senderId: this.obj.senderId
        }
      })
    }
  }
}
</script>

<style>
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #fff;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #aaa;
}

.el-menu-item {
  color: #fff;
}

body .el-table th.gutter {
  display: table-cell !important;
}
.el-notification__content {
  cursor: pointer;
}

.openUserInfo {
  color: #1890ff;
  cursor: pointer;
}
</style>
