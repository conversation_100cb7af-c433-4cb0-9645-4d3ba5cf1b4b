<template>
  <el-row class="page-container">
    <el-row class="page-main sict-upload-file">
      <el-tabs>
        <el-tab-pane :label="$route.query.id ? '修改考勤' : '考勤列表'">
      <el-row>
        <direct-search ref="form" :label-position="'right'"
          :form-style="{ 'text-align': 'left', 'margin-bottom': '10px' }" 
          :forms="searchList"
          @handleSearch="handleFormSearch" />
      </el-row>
      <el-col style="padding: 15px 0">
        <el-table-self 
           :columns="columns" 
           :current-page="pageIndex" 
           :list-loading="tableLoading" 
           :table-data="dataList"
           :total-count="total" 
           :page-sizes="pageSizes" 
           :page-size="pageSize" 
           @pageSizeChange="handleSizeChange"
          @currentPageChange="handleCurrentChange" />
      </el-col>

      <draw-form ref="drawForm" :custom-class="'charge-item-add'" :forms="drawForms" :inline-flag="true"
        @handleConfirm="handleConfirm" :label-width="'90'" :title="drawTitle"></draw-form>
      </el-tab-pane>
     
      <el-tab-pane
          label="考勤导入"
          name="second"
        >
          <!-- <span style="color:red">*</span>考勤导入 -->
          <div class="import-container">
            <el-upload
              action=""
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              :on-change="uploadByJsqd"
              :file-list="fileList">
              <el-button type="plain" style="margin-left:10px">选择文件</el-button>
            </el-upload>
          </div>

          <div>
            <el-link
              type="primary"
              @click="getTemplateData"
              style="margin-top:10px;font-size: 16px;"
              >考勤模板下载</el-link>
          </div>
      </el-tab-pane>

    </el-tabs>
    </el-row>
  </el-row>
</template>

<script>
import elTableSelf from "@/components/TabComponents/index";
import directSearch from "@/components/FormComponents/directSearch";
import paginationMixin from "@/components/TabComponents/mixin";
import date from "@/utils/date";
import drawForm from "@/components/FormComponents/draw";
import {queryPage,getAttendanceTemplate,uploadInfo,updateAttendance,getAttendanceById} from "./api";

export default {
  components: {
    elTableSelf,
    directSearch,
    drawForm
  },
  mixins: [paginationMixin],
  data() {
    return {
      tableLoading: false,
        total: 0,
        id: 0,
        drawTitle: "",
        drawForms: [
          {
              type: "date",
              label: "年-月",
              prop: "year",
              valueFormat: "yyyy-MM",
              placeholder: "请填写年-月",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写年-月",
                  trigger: "blur"
                }
              ]
            },
        //   {
        //     label: "姓名",
        //     type: "select",
        //     diyLabel: "name",
        //     diyValue: "id",
        //     isSelect: true,
        //     popperClass: "select-user-name",
        //     prop: "userId",
        //     dataOrigin: "userName",
        //     rules: [
        //         {
        //           required: true,
        //           message: "请填写姓名",
        //           trigger: "blur"
        //         }
        //       ]
        // },
        
        {
              type: "input",
              label: "实际出勤天数",
              prop: "actualDay",
              placeholder: "请填写实际出勤天数",
              class: "inputMore9",
              rules: [
                {
                  required: true,
                  message: "请填写实际出勤天数",
                  trigger: "blur"
                }
              ]
            },
            {
              type: "input",
              label: "病假天数",
              prop: "sickDay",
              placeholder: "请填写病假天数",
              class: "inputMore9"
            },
            {
              type: "input",
              label: "调休天数",
              prop: "restDay",
              placeholder: "请填写调休天数",
              class: "inputMore9"
            },
            {
              type: "input",
              label: "缺勤天数",
              prop: "absenceDay",
              placeholder: "请填写缺勤天数",
              class: "inputMore9"
            },
            {
              type: "input",
              label: "旷工天数",
              prop: "notworkDay",
              placeholder: "请填写字旷工天数",
              class: "inputMore9",          
            },
        ],
        dataList: [],
        fileList: [],
        columns: [
        {
            value: "type",
            label: "所属分公司",
            formatter(row) {
                return row.type == '1' 
                ? "总部" 
                : row.type == '2' 
                ? "蚌埠分公司" 
                : row.type == '3' 
                ? "合肥分公司" 
                : row.type == '4' 
                ? "常州分公司" 
                : "";
            }
          },
          {
            value: "year",
            label: "年-月",
            width: 120
          },
          {
            value: "userName",
            label: "姓名"
          },
          {
            value: "attendanceDay",
            label: "应出勤天数"
          },
          {
            value: "actualDay",
            label: "实际出勤天数"
          },
          {
            value: "sickDay",
            label: "病假天数"
          },
          {
            value: "sickRate",
            label: "病假系数"
          },
          {
            value: "restDay",
            label: "调休天数"
          },

          {
            value: "absenceDay",
            label: "缺勤天数"
          },
          {
            value: "notworkDay",
            label: "旷工天数"
          },

          {
            value: "notworkRate",
            label: "旷工系数"
          },
          {
            value: "createdAt",
            label: "创建时间",
            width: 180,
            formatter(row) {
              return date.dateFormat(row.createdAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
          {
            value: "updatedAt",
            label: "更新时间",
            width: 180,
            formatter(row) {
              return date.dateFormat(row.updatedAt, "YYYY-MM-DD hh:mm:ss");
            }
          },
         
         
          {
            label: "操作",
            fixed: "right",
            width: 180,
            operType: "button",
            operations: [
              {
                label: "编辑",
                type: "primary",
                func: this.handleEdit,
              },
              // {
              //   label: "删除",
              //   type: "danger",
              //   func: this.handleDelete,
                
              // }
            ]
          }

        ],

      searchList: [
        {
          label: "年-月",
          labelWidth: "80px",
          type: "date",
          valueFormat: "yyyy-MM",
          prop: "year"
        },
        {
          label: "姓名",
          labelWidth: "100px",
          type: "input",
          prop: "userName"
        },
        {
          label: "",
          type: "btn",
          list: [
            {
              func: this.handleFormSearch,
              style: "margin-left: 16px;",
              value: "查询",
              color: "primary"
            },
            // {
            //   type: "btn",
            //   labelWidth: "0px",
            //   color: "warning",
            //   value: "新增",
            //   icon: "el-icon-plus",
            //   func: this.handleAdd
            // }
          ]
        }
      ]
    };

  },
  watch: {},
  mounted() {
    this.handleSearch();
  },
  methods: {

    // initOpts() {
    //   getUserName().then(res => {
    //     if (res.code == 200) {
    //       for (let i = 0; i < this.drawForms.length; i++) {
    //         if (this.drawForms[i].dataOrigin == "userName") {
    //           this.drawForms[i].opts = res.data;
    //           break;
    //         }
    //       }
    //     }
    //   });
    // },


    getTemplateData() {
      getAttendanceTemplate()
        .then(res => {
          const blob = new Blob([res]);
          const elink = document.createElement("a");
          elink.download = "import_kq" + ".xlsx";
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        })
        .catch(error => {
          console.log(error);
        });
    },


      //文件发生改变就会触发的事件
      uploadByJsqd(file) {
      //判断是否符合beforeAvatarUpload方法中的条件
      if (this.beforeAvatarUpload(file)) {
        this.fileList.name = file.name;
        this.fileList.url = "";
        var formdata = new FormData();
        formdata.append("file", file.raw);
        formdata.append("type", '2');
        //importDevice：请求接口 formdata：传递参数
        uploadInfo(formdata).then(res => {
          if (res.code == 200) {
            this.$message.success("上传成功");
            this.importFile = "";
            this.fileList = [];
          }
        });
      }
    },


    //文件校验方法
    beforeAvatarUpload(file) {
      // 通过split方法和fileArr方法获取到文件的后缀名
      let fileArr = file.name.split(".");
      let suffix = fileArr[fileArr.length - 1];
      //只能导入.xls和.xlsx文件
      if (!/(xls|xlsx)/i.test(suffix)) {
        this.$message("文件格式不正确");
        return false;
      }
      //不能导入大小超过2Mb的文件
      if (file.size > 11 * 1024 * 1024) {
        this.$message("文件过大，请上传小于10MB的文件〜");
        return false;
      }
      return true;
    },



    // handleAdd() {
    //   this.drawTitle = "新增数据字典";
    //   this.$refs.drawForm.open();
    //   this.initForm = { };
    //   this.$refs.drawForm.initforms(this.initForm);
    // },


    // handleDelete(row) {
    //   this.$confirm(`确定要删除该记录吗?`, "提示", {
    //     type: "warning"
    //   })
    //     .then(() => {
    //       this.tableLoading = true;
    //       deletetDict({ id: row.id })
    //         .then(res => {
    //           if (res.code == 200) {
    //             this.$message.success("删除成功");
    //             this.handleSearch();
    //           } else {
    //             this.tableLoading = false;
    //           }
    //         })
    //         .catch(err => {
    //           console.log(err);
    //           this.tableLoading = false;
    //         });
    //     })
    //     .catch(() => {});
    // },


    handleEdit(row) {
      this.drawTitle = "编辑考勤";
      this.$refs.drawForm.open();
      this.$refs.drawForm.showLoading();
      getAttendanceById({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            this.$refs.drawForm.initforms(res.data);
            this.$nextTick(function() {
              this.initForm = Object.assign({}, this.$refs.drawForm.form);
            });
          }
          this.$refs.drawForm.hideLoading();
        })
        .catch(err => {
          console.log(err);
          this.$refs.drawForm.hideLoading();
        });
    },


    handleConfirm(obj) {
      this.$refs.drawForm.showLoading();
      const params = Object.assign({}, obj);
      if (obj.id) {
        updateAttendance(params)
          .then(res => {
            if (res.code == 200) {
              this.$message.success("更新成功");
              this.close();
              this.handleSearch();
            }
            this.$refs.drawForm.hideLoading();
          })
          .catch(err => {
            console.log(err);
            this.$refs.drawForm.hideLoading();
          });
      }
    },
    

    close() {
        this.initForm = {};
        this.$refs.drawForm.close();
      },

      handleFormSearch(form) {
        this.pageIndex = 1;
        this.handleSearch(form);
      },

      tabIndex(index) {
      return index + 1 + (this.pageIndex - 1) * this.pageSize;
    },

      handleSearch(form) {
        const params = Object.assign(this.$refs.form.form, form);
        params.page = this.pageIndex;
        params.pageSize = this.pageSize;
        this.tableLoading = true;
        queryPage(params).then(res => {
          if (res.code == 200) {
            this.total = res.data.total;
            this.dataList = res.data.list;
          }
          this.tableLoading = false;
        });
      }
  }
};
</script>


<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.sict-upload-file {
  .el-upload-list {
    text-align: left !important;
  }

  .el-upload-dragger {
    width: 600px;
    border: 2px dashed #bbb;
    height: 30vh;

    .el-icon-upload {
      line-height: 115px;
    }
  }
}

.el-tooltip__popper {
  max-width: 700px !important;
}
</style>